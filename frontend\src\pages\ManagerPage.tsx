import React, { useState, useEffect } from 'react';
import './ManagerPage.css';
import type { TarotSpread, SPREAD_CLASS_NAMES } from '../data/tarotSpreadsData';
import { tarotCardsData, type TarotCard } from '../data/tarotCardsData';

interface SpreadPosition {
  id: string;
  left: string;
  top: string;
  transform?: string;
}

interface ExtendedTarotSpread extends TarotSpread {  
  positions: SpreadPosition[];  
  discount: number; // 0-100 할인률  
  discountStartDate?: Date; // 할인 시작일
  discountEndDate?: Date; // 할인 종료일
  isActive: boolean; // 활성화 여부 
  order: number; // 표시 순서
  // 프롬프트 템플릿 관련 필드
  promptTemplate?: string; // 기본 프롬프트 템플릿
  systemInstruction?: string; // 시스템 명령어/가이드라인
  cardPositionLabels?: string; // JSON 형태의 카드 위치별 라벨
  customVariables?: string; // JSON 형태의 커스텀 변수
  // 카드 소개 템플릿 (새로 추가)
  cardIntroTemplates?: string; // JSON 형태의 카드별 소개 텍스트 배열
}

const ManagerPage: React.FC = () => {
  const [spreads, setSpreads] = useState<ExtendedTarotSpread[]>([]);
  const [selectedSpread, setSelectedSpread] = useState<ExtendedTarotSpread | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState<Partial<ExtendedTarotSpread>>({});
  const [activeTab, setActiveTab] = useState<'list' | 'editor' | 'layout' | 'prompt' | 'cardIntro' | 'global'>('list');
  const [selectedPositionId, setSelectedPositionId] = useState<string | null>(null);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  // 프롬프트 편집 관련 상태
  const [promptForm, setPromptForm] = useState({
    promptTemplate: '',
    systemInstruction: '',
    cardPositionLabels: '[]',
    customVariables: '{}'
  });
  const [isPromptEditing, setIsPromptEditing] = useState(false);
  const [promptSyntaxErrors, setPromptSyntaxErrors] = useState<string[]>([]);
  
  // 새로운 프롬프트 편집 고급 기능 상태들 (추가)
  const [promptPreview, setPromptPreview] = useState('');
  const [showPromptPreview, setShowPromptPreview] = useState(false);
  const [promptHistory, setPromptHistory] = useState<string[]>([]);
  const [currentHistoryIndex, setCurrentHistoryIndex] = useState(-1);
  const [availableVariables, setAvailableVariables] = useState<{[key: string]: string}>({});
  const [showVariableHelper, setShowVariableHelper] = useState(false);
  const [activePromptMode, setActivePromptMode] = useState<'basic' | 'advanced' | 'template'>('basic');
  const [templateLibrary, setTemplateLibrary] = useState<{[key: string]: string}>({});
  const [promptValidation, setPromptValidation] = useState<{
    isValid: boolean;
    issues: string[];
    suggestions: string[];
  }>({ isValid: true, issues: [], suggestions: [] });
  const [promptStats, setPromptStats] = useState<{
    wordCount: number;
    varCount: number;
    estimatedTokens: number;
  }>({ wordCount: 0, varCount: 0, estimatedTokens: 0 });
  const [promptTesting, setPromptTesting] = useState<{
    isTestMode: boolean;
    testResult: string;
    testData: {
      userName: string;
      userConcern: string;
      selectedCards: string[];
    };
  }>({
    isTestMode: false,
    testResult: '',
    testData: {
      userName: '테스트 사용자',
      userConcern: '연애운에 대해 궁금합니다.',
      selectedCards: []
    }
  });
  const [promptAISettings, setPromptAISettings] = useState<{
    style: string;
    tone: string;
    complexity: string;
    includeExamples: boolean;
  }>({
    style: 'traditional',
    tone: 'warm',
    complexity: 'medium',
    includeExamples: false
  });
  
  // 새로운 레이아웃 편집 상태들
  const [gridSize, setGridSize] = useState(20); // 그리드 스냅핑 크기
  const [showGrid, setShowGrid] = useState(true);
  const [selectedPositions, setSelectedPositions] = useState<string[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartPos, setDragStartPos] = useState({ x: 0, y: 0 });
  const [draggedCardId, setDraggedCardId] = useState<string | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [showRulers, setShowRulers] = useState(false);
  const [undoHistory, setUndoHistory] = useState<SpreadPosition[][]>([]);
  const [redoHistory, setRedoHistory] = useState<SpreadPosition[][]>([]);
  const [canvasSize, setCanvasSize] = useState({ width: 400, height: 300 });
  const [layoutPresets, setLayoutPresets] = useState<{[key: string]: SpreadPosition[]}>({});
  
  // 카드 소개 템플릿 편집 관련 상태 (새로 추가)
  const [cardIntroForm, setCardIntroForm] = useState({
    cardIntroTemplates: '[]',
    tarotStyle: 'modern',
    responseLength: 'medium'
  });
  const [isCardIntroEditing, setIsCardIntroEditing] = useState(false);
  const [cardIntroSyntaxErrors, setCardIntroSyntaxErrors] = useState<string[]>([]);
  
  // 글로벌 설정 관련 상태 (새로 추가)
  const [globalSettings, setGlobalSettings] = useState<{[key: string]: string}>({});
  const [isGlobalEditing, setIsGlobalEditing] = useState(false);

  // 초기 데이터 로드
  useEffect(() => {
    loadSpreads();
    loadGlobalSettings();
    
    // 저장된 프리셋 로드
    const savedPresets = localStorage.getItem('tarot-layout-presets');
    if (savedPresets) {
      try {
        setLayoutPresets(JSON.parse(savedPresets));
      } catch (error) {
        console.error('프리셋 로드 실패:', error);
      }
    }
  }, []);

  const loadSpreads = async () => {
    try {
      const response = await fetch('/api/tarot/spreads');
      const data = await response.json();
      
      if (data.success) {
        setSpreads(data.spreads.map((spread: any) => ({
          ...spread,
          positions: Array.isArray(spread.positions) ? spread.positions : JSON.parse(spread.positions || '[]'),
          discount: spread.discount || 0,
          isActive: spread.isActive !== false,
          order: spread.order || 0,
          promptTemplate: spread.promptTemplate || '',
          systemInstruction: spread.systemInstruction || '',
          cardPositionLabels: spread.cardPositionLabels || '[]',
          customVariables: spread.customVariables || '{}',
          cardIntroTemplates: spread.cardIntroTemplates || '[]'
        })));
      } else {
        console.error('스프레드 로드 실패:', data.message);
        setSpreads([]);
      }
    } catch (error) {
      console.error('스프레드 데이터 로드 실패:', error);
      setSpreads([]);
    }
  };

  // 글로벌 설정 로드 함수 (새로 추가)
  const loadGlobalSettings = async () => {
    try {
      const response = await fetch('/api/tarot/global-settings');
      const data = await response.json();
      
      if (data.success) {
        const settingsMap: {[key: string]: string} = {};
        data.settings.forEach((setting: any) => {
          settingsMap[setting.settingKey] = setting.settingValue;
        });
        setGlobalSettings(settingsMap);
      }
    } catch (error) {
      console.error('글로벌 설정 로드 실패:', error);
      // 오류 시 기본값 설정
      setGlobalSettings({
        global_system_instruction: '',
        default_tarot_style: 'modern',
        default_response_length: 'medium'
      });
    }
  };

  // 이름을 기반으로 스프레드 타입과 클래스명 자동 생성
  const generateSpreadTypeAndClass = (name: string) => {
    if (!name) return { spreadType: '', className: '' };
    
    // 한글과 특수문자 제거, 공백을 camelCase로 변환
    const cleanName = name
      .replace(/[^\w\s]/g, '') // 특수문자 제거
      .replace(/\s+/g, ' ') // 연속 공백을 하나로
      .trim()
      .split(' ')
      .map((word, index) => {
        if (index === 0) return word.toLowerCase();
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      })
      .join('');
    
    const spreadType = `custom${cleanName.charAt(0).toUpperCase() + cleanName.slice(1)}`;
    const className = `spread-${cleanName.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
    
    return { spreadType, className };
  };

  const handleCreateSpread = () => {
    const newSpread: Partial<ExtendedTarotSpread> = {
      id: '',
      name: '',
      description: '',
      cardCount: 3,
      cost: 10,
      iconLayout: '',
      spreadType: '',
      layoutDescription: '',
      className: '',
      positions: [],
      discount: 0,
      isActive: true,
      order: spreads.length // 새 스프레드는 맨 뒤에 추가
    };
    setEditForm(newSpread);
    setIsEditing(true);
    setActiveTab('editor');
  };

  const handleEditSpread = (spread: ExtendedTarotSpread) => {
    setSelectedSpread(spread);
    setEditForm(spread);
    setIsEditing(true);
    setActiveTab('editor');
  };

  const handleSaveSpread = async () => {
    try {
      const spreadData = {
        ...editForm,
        cardCount: Number(editForm.cardCount),
        cost: Number(editForm.cost),
        discount: Number(editForm.discount || 0),
        positions: editForm.positions || []
      };
      
      let response;
      if (editForm.id && editForm.id !== '') {
        // 기존 스프레드 업데이트
        response = await fetch(`/api/tarot/spreads/${editForm.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(spreadData),
        });
      } else {
        // 새 스프레드 생성
        response = await fetch('/api/tarot/spreads', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(spreadData),
        });
      }
      
      const data = await response.json();
      
      if (data.success) {
        // 성공시 목록 새로고침
        await loadSpreads();
        setIsEditing(false);
        setEditForm({});
        setActiveTab('list');
      } else {
        alert('저장 실패: ' + data.message);
      }
    } catch (error) {
      console.error('스프레드 저장 실패:', error);
      alert('저장 중 오류가 발생했습니다.');
    }
  };

  const handleDeleteSpread = async (spreadId: string) => {
    if (window.confirm('정말로 이 스프레드를 삭제하시겠습니까?')) {
      try {
        const response = await fetch(`/api/tarot/spreads/${spreadId}`, {
          method: 'DELETE',
        });
        
        const data = await response.json();
        
        if (data.success) {
          // 성공시 목록에서 제거
          setSpreads(prev => prev.filter(s => s.id !== spreadId));
        } else {
          alert('삭제 실패: ' + data.message);
        }
      } catch (error) {
        console.error('스프레드 삭제 실패:', error);
        alert('삭제 중 오류가 발생했습니다.');
      }
    }
  };

  const handleToggleActive = async (spreadId: string) => {
    try {
      const response = await fetch(`/api/tarot/spreads/${spreadId}/toggle`, {
        method: 'PATCH',
      });
      
      const data = await response.json();
      
      if (data.success) {
        // 성공시 로컬 상태 업데이트
        setSpreads(prev => prev.map(s =>
          s.id === spreadId ? { ...s, isActive: !s.isActive } : s
        ));
      } else {
        alert('상태 변경 실패: ' + data.message);
      }
    } catch (error) {
      console.error('상태 변경 실패:', error);
      alert('상태 변경 중 오류가 발생했습니다.');
    }
  };

  const generatePositions = (cardCount: number) => {
    const positions: SpreadPosition[] = [];
    for (let i = 0; i < cardCount; i++) {
      positions.push({
        id: (i + 1).toString(),
        left: `${25 + (i * 50 / (cardCount - 1 || 1))}%`,
        top: '50%'
      });
    }
    setEditForm(prev => ({ ...prev, positions }));
  };

  const handleGenerateAILayout = async () => {
    if (!selectedSpread) {
      alert('스프레드 정보가 필요합니다.');
      return;
    }
    
    try {
      const response = await fetch('/api/tarot/generate-layout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          spreadName: selectedSpread.name,
          cardCount: selectedSpread.cardCount,
          description: selectedSpread.description,
          layoutDescription: selectedSpread.layoutDescription
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setEditForm(prev => ({ ...prev, positions: data.positions }));
        alert('AI가 레이아웃을 생성했습니다. 필요시 수정 후 저장하세요.');
      } else {
        alert('레이아웃 생성 실패: ' + data.message);
      }
    } catch (error) {
      console.error('레이아웃 생성 실패:', error);
      alert('레이아웃 생성 중 오류가 발생했습니다.');
    }
  };

  const handleImproveLayout = async () => {
    if (!selectedSpread || !editForm.positions || editForm.positions.length === 0) {
      alert('개선할 레이아웃 정보가 필요합니다.');
      return;
    }
    
    try {
      const response = await fetch('/api/tarot/improve-layout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          spreadName: selectedSpread.name,
          cardCount: selectedSpread.cardCount,
          description: selectedSpread.description,
          layoutDescription: selectedSpread.layoutDescription,
          currentPositions: editForm.positions
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setEditForm(prev => ({ ...prev, positions: data.positions }));
        alert('AI가 배열 위치를 개선했습니다. 필요시 추가 수정 후 저장하세요.');
      } else {
        alert('배열 위치 개선 실패: ' + data.message);
      }
    } catch (error) {
      console.error('배열 위치 개선 실패:', error);
      alert('배열 위치 개선 중 오류가 발생했습니다.');
    }
  };

  const updatePosition = (positionId: string, left: string, top: string, transform?: string) => {
    setEditForm(prev => ({
      ...prev,
      positions: prev.positions?.map(pos =>
        pos.id === positionId ? { ...pos, left, top, ...(transform !== undefined && { transform }) } : pos
      ) || []
    }));
  };

  const rotateCard = (positionId: string, degrees: number) => {
    setEditForm(prev => ({
      ...prev,
      positions: prev.positions?.map(pos => {
        if (pos.id === positionId) {
          const currentTransform = pos.transform || '';
          const currentRotation = currentTransform.match(/rotate\((-?\d+)deg\)/);
          const rotation = currentRotation ? parseInt(currentRotation[1]) + degrees : degrees;
          
          const otherTransforms = currentTransform.replace(/rotate\(-?\d+deg\)\s*/g, '');
          const newTransform = otherTransforms + ` rotate(${rotation}deg)`;
          
          return {
            ...pos,
            transform: newTransform.trim()
          };
        }
        return pos;
      }) || []
    }));
  };

  // 순서 조정 함수
  const handleMoveUp = async (spreadId: string) => {
    const currentIndex = spreads.findIndex(s => s.id === spreadId);
    if (currentIndex <= 0) return;
    
    const updatedSpreads = [...spreads];
    [updatedSpreads[currentIndex - 1], updatedSpreads[currentIndex]] = 
      [updatedSpreads[currentIndex], updatedSpreads[currentIndex - 1]];
    
    // 순서 번호 업데이트
    const reorderData = updatedSpreads.map((spread, index) => ({
      id: spread.id,
      order: index
    }));
    
    try {
      const response = await fetch('/api/tarot/spreads/reorder', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ spreads: reorderData }),
      });
      
      if (response.ok) {
        setSpreads(updatedSpreads.map((spread, index) => ({
          ...spread,
          order: index
        })));
      } else {
        alert('순서 변경 실패');
      }
    } catch (error) {
      console.error('순서 변경 오류:', error);
      alert('순서 변경 중 오류가 발생했습니다.');
    }
  };

  const handleMoveDown = async (spreadId: string) => {
    const currentIndex = spreads.findIndex(s => s.id === spreadId);
    if (currentIndex >= spreads.length - 1) return;
    
    const updatedSpreads = [...spreads];
    [updatedSpreads[currentIndex], updatedSpreads[currentIndex + 1]] = 
      [updatedSpreads[currentIndex + 1], updatedSpreads[currentIndex]];
    
    // 순서 번호 업데이트
    const reorderData = updatedSpreads.map((spread, index) => ({
      id: spread.id,
      order: index
    }));
    
    try {
      const response = await fetch('/api/tarot/spreads/reorder', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ spreads: reorderData }),
      });
      
      if (response.ok) {
        setSpreads(updatedSpreads.map((spread, index) => ({
          ...spread,
          order: index
        })));
      } else {
        alert('순서 변경 실패');
      }
    } catch (error) {
      console.error('순서 변경 오류:', error);
      alert('순서 변경 중 오류가 발생했습니다.');
    }
  };

  // 프롬프트 편집 관련 함수들
  const handleEditPrompt = (spread: ExtendedTarotSpread) => {
    setSelectedSpread(spread);
    setPromptForm({
      promptTemplate: spread.promptTemplate || '',
      systemInstruction: spread.systemInstruction || '',
      cardPositionLabels: spread.cardPositionLabels || '[]',
      customVariables: spread.customVariables || '{}'
    });
    
    // 테스트 데이터 초기화 - 스프레드에 맞는 카드 수만큼 랜덤 선택
    const initialCards = getRandomCards(spread.cardCount);
    setPromptTesting({
      isTestMode: false,
      testResult: '',
      testData: {
        userName: '테스트 사용자',
        userConcern: '연애운에 대해 궁금합니다.',
        selectedCards: initialCards
      }
    });
    
    setIsPromptEditing(true);
    setActiveTab('prompt');
    
    // 미리보기 자동 활성화
    setShowPromptPreview(true);
    
    // 초기 검증 및 통계 계산 - 즉시 실행
    setTimeout(() => {
      const validation = validatePrompt(spread.promptTemplate || '');
      setPromptValidation(validation);
      const stats = calculatePromptStats(spread.promptTemplate || '');
      setPromptStats(stats);
      
      // 미리보기도 초기화
      generatePromptPreview();
    }, 100); // 상태 업데이트 후 실행
  };

  const handleSavePrompt = async () => {
    if (!selectedSpread) return;
    
    // JSON 검증
    const errors: string[] = [];
    try {
      JSON.parse(promptForm.cardPositionLabels);
    } catch (error) {
      errors.push('카드 위치 라벨 JSON 형식이 올바르지 않습니다.');
    }
    
    try {
      JSON.parse(promptForm.customVariables);
    } catch (error) {
      errors.push('커스텀 변수 JSON 형식이 올바르지 않습니다.');
    }
    
    if (errors.length > 0) {
      setPromptSyntaxErrors(errors);
      return;
    }
    
    try {
      const response = await fetch(`/api/tarot/spreads/${selectedSpread.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...selectedSpread,
          promptTemplate: promptForm.promptTemplate,
          systemInstruction: promptForm.systemInstruction,
          cardPositionLabels: promptForm.cardPositionLabels,
          customVariables: promptForm.customVariables
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        await loadSpreads();
        setIsPromptEditing(false);
        setActiveTab('list');
        setPromptSyntaxErrors([]);
        alert('프롬프트가 성공적으로 저장되었습니다.');
      } else {
        alert('저장 실패: ' + data.message);
      }
    } catch (error) {
      console.error('프롬프트 저장 실패:', error);
      alert('저장 중 오류가 발생했습니다.');
    }
  };

  // 새로운 고급 프롬프트 기능들 추가
  const validatePrompt = (prompt: string) => {
    const issues: string[] = [];
    const suggestions: string[] = [];
    
    if (!prompt.trim()) {
      issues.push('프롬프트가 비어있습니다.');
      return { isValid: false, issues, suggestions };
    }
    
    if (prompt.length < 50) {
      issues.push('프롬프트가 너무 짧습니다. (최소 50자 권장)');
      suggestions.push('더 구체적인 지시사항을 추가해보세요.');
    }
    
    if (prompt.length > 5000) {
      issues.push('프롬프트가 너무 깁니다. (최대 5000자 권장)');
      suggestions.push('핵심 내용만 남기고 간소화해보세요.');
    }
    
    // 변수 사용 검증
    const variables = prompt.match(/{[^}]+}/g) || [];
    if (variables.length === 0) {
      suggestions.push('동적 변수({userName}, {cards} 등)를 사용하면 더 개인화된 해석이 가능합니다.');
    }
    
    // 한국어 포함 여부 검증
    const hasKorean = /[ㄱ-ㅎ|ㅏ-ㅣ|가-힣]/.test(prompt);
    if (!hasKorean) {
      issues.push('한국어 내용이 포함되지 않았습니다.');
      suggestions.push('한국어로 지시사항을 작성해주세요.');
    }
    
    // 타로 관련 키워드 검증
    const tarotKeywords = ['타로', '카드', '해석', '의미', '상징', '메시지'];
    const hasTarotKeywords = tarotKeywords.some(keyword => prompt.includes(keyword));
    if (!hasTarotKeywords) {
      suggestions.push('타로 해석과 관련된 키워드를 포함하면 더 명확한 지시가 됩니다.');
    }
    
    return {
      isValid: issues.length === 0,
      issues,
      suggestions
    };
  };

  // 프롬프트 통계 계산
  const calculatePromptStats = (prompt: string) => {
    if (!prompt || !prompt.trim()) {
      return { wordCount: 0, varCount: 0, estimatedTokens: 0 };
    }
    
    const words = prompt.trim().split(/\s+/).filter(word => word.length > 0);
    const wordCount = words.length;
    const varCount = (prompt.match(/{[^}]+}/g) || []).length;
    const estimatedTokens = Math.ceil(prompt.length / 4); // 대략적인 토큰 수 계산
    
    return { wordCount, varCount, estimatedTokens };
  };

  // userConcern 축약 함수
  const summarizeUserConcern = (concern: string): string => {
    if (!concern || concern.length <= 50) return concern;
    
    // 문장 단위로 분리
    const sentences = concern.split(/[.!?]/);
    const firstSentence = sentences[0]?.trim();
    
    if (firstSentence && firstSentence.length <= 50) {
      return firstSentence;
    }
    
    // 50자로 자르고 "..." 추가
    return concern.substring(0, 47) + '...';
  };

  // 프롬프트 미리보기 생성
  const generatePromptPreview = () => {
    if (!promptForm.promptTemplate.trim()) {
      setPromptPreview('미리보기를 생성하려면 프롬프트를 입력하세요.');
      return;
    }
    
    let preview = promptForm.promptTemplate;
    const testData = promptTesting.testData;
    
    // 변수 치환
    preview = preview.replace(/{userName}/g, testData.userName || '테스트 사용자');
    preview = preview.replace(/{cardCount}/g, (testData.selectedCards?.length || 3).toString());
    preview = preview.replace(/{userConcern}/g, summarizeUserConcern(testData.userConcern || '일반적인 고민'));
    preview = preview.replace(/{spreadName}/g, selectedSpread?.name || '테스트 스프레드');
    preview = preview.replace(/{description}/g, selectedSpread?.description || '테스트 설명');
    
    // 카드 정보 생성
    const cardInfo = (testData.selectedCards || ['마법사', '여제', '태양']).map((card, index) => 
      `${index + 1}번째 카드: ${card}`
    ).join('\n');
    preview = preview.replace(/{cards}/g, cardInfo);
    
    // 커스텀 변수 치환
    try {
      const customVars = JSON.parse(promptForm.customVariables || '{}');
      Object.entries(customVars).forEach(([key, value]) => {
        const regex = new RegExp(`{${key}}`, 'g');
        preview = preview.replace(regex, value as string);
      });
    } catch (error) {
      console.warn('커스텀 변수 파싱 실패:', error);
    }
    
    setPromptPreview(preview);
  };

  // AI로 프롬프트 생성
  const handleGeneratePrompt = async () => {
    if (!selectedSpread) {
      alert('스프레드 정보가 필요합니다.');
      return;
    }
    
    try {
      let customVariables = {};
      if (promptForm.customVariables.trim()) {
      try {
        customVariables = JSON.parse(promptForm.customVariables);
      } catch (error) {
          console.warn('커스텀 변수 파싱 실패:', error);
      }
    }
    
      const response = await fetch('/api/tarot/generate-prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          spreadName: selectedSpread.name,
          cardCount: selectedSpread.cardCount,
          description: selectedSpread.description,
          layoutDescription: selectedSpread.layoutDescription,
          customVariables: customVariables,
          // 새로운 AI 설정 추가
          aiSettings: promptAISettings
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        // 기존 프롬프트를 히스토리에 추가
        if (promptForm.promptTemplate.trim()) {
          addPromptToHistory(promptForm.promptTemplate);
        }
        setPromptForm(prev => ({
          ...prev,
          promptTemplate: data.generatedPrompt
        }));
        alert('프롬프트가 생성되었습니다. 저장하려면 "저장" 버튼을 클릭하세요.');
      } else {
        alert('프롬프트 생성 실패: ' + data.message);
      }
    } catch (error) {
      console.error('프롬프트 생성 실패:', error);
      alert('프롬프트 생성 중 오류가 발생했습니다.');
    }
  };

  // AI로 프롬프트 테스트
  const testPromptWithAI = async () => {
    if (!promptForm.promptTemplate.trim()) {
      alert('테스트할 프롬프트를 입력해주세요.');
      return;
    }

    if (!selectedSpread) {
      alert('스프레드 정보가 필요합니다.');
      return;
    }
    
    setPromptTesting(prev => ({ ...prev, isTestMode: true, testResult: '' }));
    
    try {
      let customVariables = {};
      if (promptForm.customVariables.trim()) {
        try {
          customVariables = JSON.parse(promptForm.customVariables);
        } catch (error) {
          console.warn('커스텀 변수 파싱 실패:', error);
        }
      }
      
      const response = await fetch('/api/tarot/test-prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          promptTemplate: promptForm.promptTemplate,
          systemInstruction: promptForm.systemInstruction,
          testData: promptTesting.testData,
          spreadName: selectedSpread.name,
          cardCount: selectedSpread.cardCount,
          customVariables: customVariables
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setPromptTesting(prev => ({
          ...prev,
          isTestMode: false,
          testResult: data.testResult
        }));
      } else {
        setPromptTesting(prev => ({ ...prev, isTestMode: false }));
        alert('프롬프트 테스트 실패: ' + data.message);
      }
    } catch (error) {
      console.error('프롬프트 테스트 실패:', error);
      setPromptTesting(prev => ({ ...prev, isTestMode: false }));
      alert('프롬프트 테스트 중 오류가 발생했습니다.');
    }
  };

  const handleImprovePrompt = async () => {
    if (!selectedSpread || !promptForm.promptTemplate.trim()) {
      alert('개선할 프롬프트 내용이 필요합니다.');
      return;
    }
    
    // 기존 프롬프트를 히스토리에 추가
    addPromptToHistory(promptForm.promptTemplate);
    
    // 커스텀 변수 확인
    let customVariables = null;
    if (promptForm.customVariables && promptForm.customVariables.trim()) {
      try {
        customVariables = JSON.parse(promptForm.customVariables);
      } catch (error) {
        console.warn('커스텀 변수 JSON 파싱 실패:', error);
      }
    }
    
    try {
      const response = await fetch('/api/tarot/improve-prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPrompt: promptForm.promptTemplate,
          spreadName: selectedSpread.name,
          cardCount: selectedSpread.cardCount,
          description: selectedSpread.description,
          customVariables: customVariables,
          // 검증 결과도 함께 전송하여 더 나은 개선 제안
          validationIssues: promptValidation.issues,
          validationSuggestions: promptValidation.suggestions,
          // AI 설정 추가
          aiSettings: promptAISettings
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setPromptForm(prev => ({
          ...prev,
          promptTemplate: data.improvedPrompt
        }));
        alert('프롬프트가 개선되었습니다. 저장하려면 "저장" 버튼을 클릭하세요.');
      } else {
        alert('프롬프트 개선 실패: ' + data.message);
      }
    } catch (error) {
      console.error('프롬프트 개선 실패:', error);
      alert('프롬프트 개선 중 오류가 발생했습니다.');
    }
  };

  const validateJSON = (jsonString: string) => {
    try {
      JSON.parse(jsonString);
      return true;
    } catch {
      return false;
    }
  };

  // 카드 수에 따른 기본 템플릿 생성 함수
  const generateDefaultCardIntroTemplates = (cardCount: number): string[] => {
    const templates: string[] = [];
    
    for (let i = 0; i < cardCount; i++) {
      const cardIndex = i + 1;
      let template = `{cardIndex}번째 카드, **{cardName}**입니다.\\n\\n`;
      
      if (cardCount === 3) {
        if (i === 0) template += "이 카드는 {userName}님의 현재 상황이나 에너지를 보여줍니다.\\n\\n이 카드가 전하는 메시지를 통해 현재 어떤 상황에 놓여 있는지 확인해보세요.";
        else if (i === 1) template += "이 카드는 {userName}님이 직면한 도전이나 고려해야 할 사항을 나타냅니다.\\n\\n어떤 장애물이나 과제가 있는지 살펴보세요.";
        else template += "이 카드는 {userName}님의 미래 가능성과 나아갈 방향을 제시합니다.\\n\\n앞으로 어떤 결과나 조언을 받아들일지 생각해보세요.";
      } else if (cardCount === 5) {
        if (i === 0) template += "이 카드는 {userName}님의 현재 상황의 핵심을 보여줍니다.\\n\\n가장 중요한 에너지가 무엇인지 확인해보세요.";
        else if (i === 1) template += "이 카드는 {userName}님이 마주한 장애물이나 도전을 나타냅니다.\\n\\n어떤 어려움을 극복해야 하는지 살펴보세요.";
        else if (i === 2) template += "이 카드는 {userName}님에게 숨겨진 영향력이나 무의식적 요소를 드러냅니다.\\n\\n보이지 않는 힘이 무엇인지 탐구해보세요.";
        else if (i === 3) template += "이 카드는 {userName}님에게 필요한 조언과 행동 방향을 제시합니다.\\n\\n어떤 행동을 취해야 할지 고민해보세요.";
        else template += "이 카드는 {userName}님의 최종 결과와 미래의 모습을 암시합니다.\\n\\n어떤 결과를 기대할 수 있는지 상상해보세요.";
      } else if (cardCount === 7) {
        if (i === 0) template += "이 카드는 {userName}님의 현재 상황 전반을 보여줍니다.\\n\\n지금 어떤 상태에 있는지 파악해보세요.";
        else if (i === 1) template += "이 카드는 {userName}님이 즉시 직면한 도전이나 문제를 나타냅니다.\\n\\n당장 해결해야 할 것이 무엇인지 확인해보세요.";
        else if (i === 2) template += "이 카드는 {userName}님의 과거나 근본 원인을 드러냅니다.\\n\\n현재 상황의 뿌리가 어디에 있는지 탐색해보세요.";
        else if (i === 3) template += "이 카드는 {userName}님이 내려놓아야 할 것을 알려줍니다.\\n\\n무엇을 놓아주어야 하는지 성찰해보세요.";
        else if (i === 4) template += "이 카드는 {userName}님에게 도움이 될 자원이나 지원을 나타냅니다.\\n\\n어떤 도움을 받을 수 있는지 살펴보세요.";
        else if (i === 5) template += "이 카드는 {userName}님이 취해야 할 구체적인 행동이나 조언을 제시합니다.\\n\\n어떤 실천이 필요한지 계획해보세요.";
        else template += "이 카드는 {userName}님의 최종 결과와 도달할 목표를 보여줍니다.\\n\\n궁극적으로 어떤 성과를 얻을 수 있는지 확인해보세요.";
      } else if (cardCount === 10) {
        const meanings = [
          "이 카드는 {userName}님의 핵심 주제나 질문의 본질을 보여줍니다.\\n\\n가장 중요한 이슈가 무엇인지 파악해보세요.",
          "이 카드는 {userName}님에게 교차하는 영향이나 갈등을 나타냅니다.\\n\\n어떤 요소들이 복잡하게 얽혀있는지 살펴보세요.",
          "이 카드는 {userName}님의 무의식이나 깊은 기반을 드러냅니다.\\n\\n내면 깊숙한 동기가 무엇인지 탐구해보세요.",
          "이 카드는 {userName}님의 과거 경험이나 배경을 보여줍니다.\\n\\n어떤 과거의 영향이 현재에 작용하는지 확인해보세요.",
          "이 카드는 {userName}님이 달성할 수 있는 잠재적 성취를 나타냅니다.\\n\\n어떤 가능성이 열려있는지 상상해보세요.",
          "이 카드는 {userName}님의 가까운 미래나 즉시 다가올 일을 암시합니다.\\n\\n곧 어떤 변화가 올지 준비해보세요.",
          "이 카드는 {userName}님의 현재 태도나 접근 방식을 반영합니다.\\n\\n지금 어떤 마음가짐을 갖고 있는지 성찰해보세요.",
          "이 카드는 {userName}님 주변의 외부 환경이나 타인의 영향을 보여줍니다.\\n\\n주변 상황이 어떻게 작용하는지 관찰해보세요.",
          "이 카드는 {userName}님의 내면의 희망이나 두려움을 드러냅니다.\\n\\n진정으로 원하거나 걱정하는 것이 무엇인지 탐색해보세요.",
          "이 카드는 {userName}님의 궁극적인 결과나 최종 목표를 제시합니다.\\n\\n모든 것이 어떻게 마무리될지 전망해보세요."
        ];
        template += meanings[i] || `이 카드는 {userName}님의 ${cardIndex}번째 중요한 측면을 보여줍니다.\\n\\n이 위치가 전하는 메시지를 깊이 받아들여보세요.`;
      } else {
        // 기타 카드 수에 대한 일반적인 템플릿
        template += `이 카드는 {userName}님의 ${cardIndex}번째 중요한 메시지를 담고 있습니다.\\n\\n이 카드가 전하는 의미를 깊이 생각해보세요.`;
      }
      
      templates.push(template);
    }
    
    return templates;
  };

  // 카드 소개 템플릿 편집 관련 함수들 (새로 추가)
  const handleEditCardIntro = (spread: ExtendedTarotSpread) => {
    setSelectedSpread(spread);
    
    // 기존 템플릿이 있으면 사용, 없으면 기본 템플릿 생성
    let templates = spread.cardIntroTemplates || '[]';
    try {
      const parsedTemplates = JSON.parse(templates);
      if (!Array.isArray(parsedTemplates) || parsedTemplates.length !== spread.cardCount) {
        // 카드 수와 맞지 않으면 기본 템플릿 생성
        const defaultTemplates = generateDefaultCardIntroTemplates(spread.cardCount);
        templates = JSON.stringify(defaultTemplates, null, 2);
      }
    } catch (error) {
      // JSON 파싱 오류 시 기본 템플릿 생성
      const defaultTemplates = generateDefaultCardIntroTemplates(spread.cardCount);
      templates = JSON.stringify(defaultTemplates, null, 2);
    }
    
    setCardIntroForm({
      cardIntroTemplates: templates,
      tarotStyle: 'modern',
      responseLength: 'medium'
    });
    setIsCardIntroEditing(true);
    setActiveTab('cardIntro');
    setCardIntroSyntaxErrors([]);
  };

  const handleSaveCardIntro = async () => {
    if (!selectedSpread) return;
    
    // JSON 검증
    const errors: string[] = [];
    try {
      const templates = JSON.parse(cardIntroForm.cardIntroTemplates);
      if (!Array.isArray(templates)) {
        errors.push('카드 소개 템플릿은 배열 형태여야 합니다.');
      }
    } catch (error) {
      errors.push('카드 소개 템플릿 JSON 형식이 올바르지 않습니다.');
    }
    
    if (errors.length > 0) {
      setCardIntroSyntaxErrors(errors);
      return;
    }
    
    try {
      const response = await fetch(`/api/tarot/spreads/${selectedSpread.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...selectedSpread,
          cardIntroTemplates: cardIntroForm.cardIntroTemplates
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        await loadSpreads();
        setIsCardIntroEditing(false);
        setActiveTab('list');
        setCardIntroSyntaxErrors([]);
        alert('카드 소개 템플릿이 성공적으로 저장되었습니다.');
      } else {
        alert('저장 실패: ' + data.message);
      }
    } catch (error) {
      console.error('카드 소개 템플릿 저장 실패:', error);
      alert('저장 중 오류가 발생했습니다.');
    }
  };

  const handleGenerateCardPositionLabels = async () => {
    if (!selectedSpread) {
      alert('스프레드 정보가 필요합니다.');
      return;
    }
    
    try {
      const response = await fetch('/api/tarot/generate-position-labels', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          spreadName: selectedSpread.name,
          cardCount: selectedSpread.cardCount,
          description: selectedSpread.description,
          layoutDescription: selectedSpread.layoutDescription
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setPromptForm(prev => ({
          ...prev,
          cardPositionLabels: JSON.stringify(data.labels, null, 2)
        }));
        alert('카드 위치 라벨이 생성되었습니다.');
      } else {
        alert('라벨 생성 실패: ' + data.message);
      }
    } catch (error) {
      console.error('라벨 생성 실패:', error);
      alert('라벨 생성 중 오류가 발생했습니다.');
    }
  };

  const handleGenerateCustomVariables = async () => {
    if (!selectedSpread) {
      alert('스프레드 정보가 필요합니다.');
      return;
    }
    
    try {
      const response = await fetch('/api/tarot/generate-custom-variables', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          spreadName: selectedSpread.name,
          cardCount: selectedSpread.cardCount,
          description: selectedSpread.description,
          layoutDescription: selectedSpread.layoutDescription
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setPromptForm(prev => ({
          ...prev,
          customVariables: JSON.stringify(data.variables, null, 2)
        }));
        alert('커스텀 변수가 생성되었습니다.');
      } else {
        alert('변수 생성 실패: ' + data.message);
      }
    } catch (error) {
      console.error('변수 생성 실패:', error);
      alert('변수 생성 중 오류가 발생했습니다.');
    }
  };

  const handleGenerateCardIntro = async () => {
    if (!selectedSpread) {
      alert('스프레드 정보가 필요합니다.');
      return;
    }
    
    try {
      const response = await fetch('/api/tarot/generate-card-intro', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          spreadName: selectedSpread.name,
          cardCount: selectedSpread.cardCount,
          description: selectedSpread.description,
          layoutDescription: selectedSpread.layoutDescription,
          tarotStyle: cardIntroForm.tarotStyle,
          responseLength: cardIntroForm.responseLength
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setCardIntroForm(prev => ({
          ...prev,
          cardIntroTemplates: JSON.stringify(data.templates, null, 2)
        }));
        alert('카드 소개 템플릿이 생성되었습니다. 저장하려면 "저장" 버튼을 클릭하세요.');
      } else {
        alert('템플릿 생성 실패: ' + data.message);
      }
    } catch (error) {
      console.error('템플릿 생성 실패:', error);
      alert('템플릿 생성 중 오류가 발생했습니다.');
    }
  };

  const renderSpreadList = () => (
    <div className="spread-list">
      <div className="list-header">
        <h2>타로 스프레드 관리</h2>
        <button className="create-btn" onClick={handleCreateSpread}>
          새 스프레드 추가
        </button>
      </div>
      
      <div className="spread-grid">
        {spreads.map(spread => (
          <div key={spread.id} className="spread-item">
            <div className="spread-preview">
              <div className="spread-visual-mini">
                {spread.positions.map(pos => (
                  <div
                    key={pos.id}
                    className="mini-dot"
                    style={{
                      left: pos.left,
                      top: pos.top,
                      transform: pos.transform
                    }}
                  />
                ))}
              </div>
            </div>
            <div className="spread-info">
              <h3>{spread.name}</h3>
              <p>{spread.cardCount}장 • {spread.cost}크레딧</p>
              {spread.discount > 0 && (
                <span className="discount-badge">-{spread.discount}%</span>
              )}
              <div className="spread-actions">
                <div className="order-controls">
                  <button
                    onClick={() => handleMoveUp(spread.id)}
                    disabled={spreads.findIndex(s => s.id === spread.id) === 0}
                    className="order-btn"
                    title="위로 이동"
                  >
                    ↑
                  </button>
                  <button
                    onClick={() => handleMoveDown(spread.id)}
                    disabled={spreads.findIndex(s => s.id === spread.id) === spreads.length - 1}
                    className="order-btn"
                    title="아래로 이동"
                  >
                    ↓
                  </button>
                </div>
                <button onClick={() => handleEditSpread(spread)}>편집</button>
                <button
                  onClick={() => {
                    setSelectedSpread(spread);
                    setEditForm(spread);
                    setActiveTab('layout');
                  }}
                  className="layout-btn"
                >
                  배열 편집
                </button>
                <button
                  onClick={() => handleEditPrompt(spread)}
                  className="prompt-btn"
                >
                  프롬프트 편집
                </button>
                <button
                  onClick={() => handleEditCardIntro(spread)}
                  className="card-intro-btn"
                >
                  카드 소개 편집
                </button>
                <button
                  onClick={() => handleDeleteSpread(spread.id)}
                  className="delete-btn"
                >
                  삭제
                </button>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={spread.isActive}
                    onChange={() => handleToggleActive(spread.id)}
                  />
                  <span className="slider"></span>
                </label>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderEditor = () => (
    <div className="spread-editor">
      <div className="editor-header">
        <h2>{editForm.id ? '스프레드 편집' : '새 스프레드 추가'}</h2>
        <div className="editor-actions">
          <button onClick={handleSaveSpread} className="save-btn">저장</button>
          <button onClick={() => setActiveTab('list')} className="cancel-btn">취소</button>
        </div>
      </div>
      
      <div className="editor-form">
        <div className="form-section">
          <h3>기본 정보</h3>
          <div className="form-grid">
            <div className="form-group">
              <label>이름</label>
              <input
                type="text"
                value={editForm.name || ''}
                onChange={(e) => {
                  const name = e.target.value;
                  const { spreadType, className } = generateSpreadTypeAndClass(name);
                  setEditForm(prev => ({
                    ...prev,
                    name,
                    spreadType,
                    className
                  }));
                }}
              />
            </div>
            <div className="form-group">
              <label>카드 수</label>
              <input
                type="number"
                min="1"
                max="22"
                value={editForm.cardCount || 3}
                onChange={(e) => {
                  const cardCount = parseInt(e.target.value);
                  setEditForm(prev => ({ ...prev, cardCount }));
                  generatePositions(cardCount);
                }}
              />
            </div>
            <div className="form-group">
              <label>크레딧</label>
              <input
                type="number"
                min="0"
                value={editForm.cost || 10}
                onChange={(e) => setEditForm(prev => ({ ...prev, cost: parseInt(e.target.value) }))}
              />
            </div>
            <div className="form-group">
              <label>할인률 (%)</label>
              <input
                type="number"
                min="0"
                max="100"
                value={editForm.discount || 0}
                onChange={(e) => setEditForm(prev => ({ ...prev, discount: parseInt(e.target.value) }))}
              />
            </div>
          </div>
          
          {(editForm.discount || 0) > 0 && (
            <div className="form-group discount-period">
              <h4>📅 할인 기간 설정</h4>
              <p className="field-description">
                기간을 설정하지 않으면 관리자가 변경하기 전까지 항시 적용됩니다.
              </p>
              <div className="date-range">
                <div className="date-field">
                  <label>할인 시작일</label>
                  <input
                    type="datetime-local"
                    value={editForm.discountStartDate ? new Date(editForm.discountStartDate).toISOString().slice(0, 16) : ''}
                    onChange={(e) => setEditForm(prev => ({ 
                      ...prev, 
                      discountStartDate: e.target.value ? new Date(e.target.value) : undefined 
                    }))}
                  />
                </div>
                <div className="date-field">
                  <label>할인 종료일</label>
                  <input
                    type="datetime-local"
                    value={editForm.discountEndDate ? new Date(editForm.discountEndDate).toISOString().slice(0, 16) : ''}
                    onChange={(e) => setEditForm(prev => ({ 
                      ...prev, 
                      discountEndDate: e.target.value ? new Date(e.target.value) : undefined 
                    }))}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
        
        <div className="form-section">
          <h3>설명</h3>
          <textarea
            value={editForm.description || ''}
            onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
            rows={3}
            className="rich-textarea"
            placeholder="스프레드의 용도와 특징을 설명해주세요..."
          />
        </div>
        
        <div className="form-section">
          <h3>레이아웃 설명</h3>
          <textarea
            value={editForm.layoutDescription || ''}
            onChange={(e) => setEditForm(prev => ({ ...prev, layoutDescription: e.target.value }))}
            rows={3}
            className="rich-textarea"
            placeholder="카드들이 어떻게 배치되는지, 각 위치의 의미를 설명해주세요..."
          />
        </div>
        
        <div className="form-section">
          <div className="advanced-header">
            <h3>고급 설정</h3>
            <button
              type="button"
              onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
              className="toggle-advanced-btn"
            >
              {showAdvancedSettings ? '숨기기' : '표시'}
            </button>
          </div>
          {showAdvancedSettings && (
            <>
              <div className="advanced-info">
                <p>🔧 <strong>고급 사용자용 설정</strong></p>
                <p>• <strong>스프레드 타입</strong>: API에서 식별하는 고유 ID (자동 생성됨)</p>
                <p>• <strong>CSS 클래스</strong>: 스타일링용 클래스명 (자동 생성됨)</p>
                <p>특별한 이유가 없다면 자동 생성된 값을 사용하세요.</p>
              </div>
              <div className="form-grid">
                <div className="form-group">
                  <label>스프레드 타입 (자동 생성)</label>
                  <input
                    type="text"
                    value={editForm.spreadType || ''}
                    onChange={(e) => setEditForm(prev => ({ ...prev, spreadType: e.target.value }))}
                    placeholder="예: customThreeCard"
                    style={{ fontFamily: 'monospace', fontSize: '0.9rem' }}
                  />
                </div>
                <div className="form-group">
                  <label>CSS 클래스 (자동 생성)</label>
                  <input
                    type="text"
                    value={editForm.className || ''}
                    onChange={(e) => setEditForm(prev => ({ ...prev, className: e.target.value }))}
                    placeholder="예: spread-three-card"
                    style={{ fontFamily: 'monospace', fontSize: '0.9rem' }}
                  />
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );

  const renderPromptEditor = () => (
    <div className="prompt-editor-pro">
      <div className="editor-header">
        <h2>🎯 고급 프롬프트 편집기: {selectedSpread?.name}</h2>
        <div className="editor-mode-tabs">
          <button 
            className={activePromptMode === 'basic' ? 'active' : ''}
            onClick={() => setActivePromptMode('basic')}
          >
            기본 편집
          </button>
          <button 
            className={activePromptMode === 'advanced' ? 'active' : ''}
            onClick={() => setActivePromptMode('advanced')}
          >
            고급 도구
          </button>
          <button 
            className={activePromptMode === 'template' ? 'active' : ''}
            onClick={() => setActivePromptMode('template')}
          >
            템플릿 라이브러리
          </button>
        </div>
        <div className="editor-actions">
          <button 
            onClick={() => setShowPromptPreview(!showPromptPreview)} 
            className={showPromptPreview ? "preview-btn active" : "preview-btn"}
          >
            {showPromptPreview ? '🔍 미리보기 끄기' : '🔍 미리보기'}
          </button>
          <button 
            onClick={() => setShowVariableHelper(!showVariableHelper)} 
            className={showVariableHelper ? "helper-btn active" : "helper-btn"}
          >
            {showVariableHelper ? '📝 변수 도우미 끄기' : '📝 변수 도우미'}
          </button>
          <button onClick={testPromptWithAI} className="test-btn" disabled={promptTesting.isTestMode}>
            {promptTesting.isTestMode ? '🔄 테스트 중...' : '🧪 AI 테스트'}
          </button>
          <button onClick={handleGeneratePrompt} className="generate-btn">
            🤖 AI 생성
          </button>
          <button onClick={handleImprovePrompt} className="improve-btn">
            ✨ AI 개선
          </button>
          <button onClick={handleSavePrompt} className="save-btn">
            💾 저장
          </button>
          <button onClick={() => setActiveTab('list')} className="cancel-btn">
            ← 돌아가기
          </button>
        </div>
      </div>
      
      <div className="prompt-workspace">
        {/* 왼쪽 패널 - 도구 및 설정 */}
        <div className="left-prompt-panel">
          {/* AI 생성 설정 */}
          <div className="panel-section">
            <h3>🤖 AI 생성 설정</h3>
            <div className="ai-settings-grid">
              <div className="setting-group">
                <label>스타일</label>
                <select
                  value={promptAISettings.style}
                  onChange={(e) => setPromptAISettings(prev => ({ ...prev, style: e.target.value }))}
                >
                  <option value="traditional">전통적</option>
                  <option value="modern">현대적</option>
                  <option value="intuitive">직관적</option>
                  <option value="scientific">분석적</option>
                </select>
              </div>
              <div className="setting-group">
                <label>어조</label>
                <select
                  value={promptAISettings.tone}
                  onChange={(e) => setPromptAISettings(prev => ({ ...prev, tone: e.target.value }))}
                >
                  <option value="warm">따뜻한</option>
                  <option value="professional">전문적</option>
                  <option value="mysterious">신비로운</option>
                  <option value="encouraging">격려하는</option>
                </select>
              </div>
              <div className="setting-group">
                <label>복잡도</label>
                <select
                  value={promptAISettings.complexity}
                  onChange={(e) => setPromptAISettings(prev => ({ ...prev, complexity: e.target.value }))}
                >
                  <option value="simple">단순</option>
                  <option value="medium">보통</option>
                  <option value="complex">복합</option>
                  <option value="expert">전문가</option>
                </select>
              </div>
              <div className="setting-group">
                <label>
                  <input
                    type="checkbox"
                    checked={promptAISettings.includeExamples}
                    onChange={(e) => setPromptAISettings(prev => ({ ...prev, includeExamples: e.target.checked }))}
                  />
                  예시 포함
                </label>
              </div>
            </div>
          </div>

          {/* 프롬프트 통계 */}
          <div className="panel-section">
            <h3>📊 프롬프트 통계</h3>
            <div className="stats-grid">
              <div className="stat-item">
                <span className="stat-label">단어 수</span>
                <span className="stat-value">{promptStats.wordCount}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">변수 수</span>
                <span className="stat-value">{promptStats.varCount}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">예상 토큰</span>
                <span className="stat-value">{promptStats.estimatedTokens}</span>
              </div>
            </div>
          </div>

          {/* 검증 결과 */}
          <div className="panel-section">
            <h3>🔍 검증 결과</h3>
            <div className={`validation-status ${promptValidation.isValid ? 'valid' : 'invalid'}`}>
              <div className="validation-icon">
                {promptValidation.isValid ? '✅' : '⚠️'}
              </div>
              <div className="validation-text">
                {promptValidation.isValid ? '프롬프트가 유효합니다' : `${promptValidation.issues.length}개 문제 발견`}
              </div>
            </div>
            
            {promptValidation.issues.length > 0 && (
              <div className="validation-issues">
                <h4>문제점:</h4>
                {promptValidation.issues.map((issue, index) => (
                  <div key={index} className="issue-item">❌ {issue}</div>
                ))}
              </div>
            )}
            
            {promptValidation.suggestions.length > 0 && (
              <div className="validation-suggestions">
                <h4>개선 제안:</h4>
                {promptValidation.suggestions.map((suggestion, index) => (
                  <div key={index} className="suggestion-item">💡 {suggestion}</div>
                ))}
              </div>
            )}
          </div>

          {/* 프롬프트 히스토리 */}
          {promptHistory.length > 0 && (
            <div className="panel-section">
              <h3>📜 최근 프롬프트</h3>
              <div className="history-list">
                {promptHistory.slice(0, 5).map((prompt, index) => (
                  <div 
                    key={index} 
                    className="history-item"
                    onClick={() => {
                      if (window.confirm('이 프롬프트로 복원하시겠습니까?')) {
                        setPromptForm(prev => ({ ...prev, promptTemplate: prompt }));
                      }
                    }}
                  >
                    <div className="history-preview">
                      {prompt.substring(0, 50)}...
                    </div>
                    <div className="history-meta">
                      {Math.floor(prompt.length / 4)} 토큰
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 템플릿 라이브러리 (템플릿 모드일 때) */}
          {activePromptMode === 'template' && (
            <div className="panel-section">
              <h3>📚 템플릿 라이브러리</h3>
              <div className="template-actions">
                <input
                  type="text"
                  placeholder="템플릿 이름"
                  id="template-name-input"
                  className="template-name-input"
                />
                <button
                  onClick={() => {
                    const input = document.getElementById('template-name-input') as HTMLInputElement;
                    if (input.value.trim() && promptForm.promptTemplate.trim()) {
                      saveToTemplateLibrary(input.value.trim(), promptForm.promptTemplate);
                      input.value = '';
                      alert('템플릿이 저장되었습니다.');
                    }
                  }}
                  className="save-template-btn"
                >
                  현재 프롬프트 저장
                </button>
              </div>
              <div className="template-list">
                {Object.entries(templateLibrary).map(([name, template]) => (
                  <div key={name} className="template-item">
                    <div className="template-info">
                      <div className="template-name">{name}</div>
                      <div className="template-preview">
                        {template.substring(0, 80)}...
                      </div>
                    </div>
                    <div className="template-actions-mini">
                      <button
                        onClick={() => loadTemplateFromLibrary(name)}
                        className="load-template-btn"
                      >
                        불러오기
                      </button>
                      <button
                        onClick={() => {
                          if (window.confirm(`"${name}" 템플릿을 삭제하시겠습니까?`)) {
                            const newLibrary = { ...templateLibrary };
                            delete newLibrary[name];
                            setTemplateLibrary(newLibrary);
                            localStorage.setItem('tarot-prompt-templates', JSON.stringify(newLibrary));
                          }
                        }}
                        className="delete-template-btn"
                      >
                        삭제
                      </button>
                    </div>
                  </div>
                ))}
                {Object.keys(templateLibrary).length === 0 && (
                  <div className="empty-templates">
                    저장된 템플릿이 없습니다.
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 중앙 편집 영역 */}
        <div className="center-prompt-panel">
          {/* 오류 메시지 */}
        {promptSyntaxErrors.length > 0 && (
          <div className="error-messages">
            {promptSyntaxErrors.map((error, index) => (
                <div key={index} className="error-message">❌ {error}</div>
            ))}
          </div>
        )}
        
          {/* 메인 프롬프트 편집 */}
          <div className="main-prompt-section">
            <div className="section-header">
              <h3>🎯 메인 프롬프트 템플릿</h3>
              <div className="character-count">
                {promptForm.promptTemplate.length} / 5000 문자
              </div>
            </div>
            <div className="prompt-editor-container">
          <textarea
            value={promptForm.promptTemplate}
            onChange={(e) => setPromptForm(prev => ({ ...prev, promptTemplate: e.target.value }))}
                rows={activePromptMode === 'basic' ? 20 : 15}
                className="prompt-textarea-enhanced"
                placeholder="프롬프트를 입력하세요. 변수는 {userName}, {cards} 형태로 사용할 수 있습니다..."
              />
              {showVariableHelper && (
                <div className="variable-helper-overlay">
                  <h4>🔧 변수 삽입 도우미</h4>
                  <div className="variable-buttons">
                    {Object.entries(availableVariables).map(([variable, description]) => (
                      <button
                        key={variable}
                        onClick={() => insertVariableIntoPrompt(variable)}
                        className="variable-btn"
                        title={description}
                      >
                        {variable}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
        </div>
        
          {/* 시스템 명령어 */}
          <div className="system-instruction-section">
            <h3>⚙️ 시스템 명령어</h3>
          <textarea
            value={promptForm.systemInstruction}
            onChange={(e) => setPromptForm(prev => ({ ...prev, systemInstruction: e.target.value }))}
              rows={4}
              className="instruction-textarea-enhanced"
              placeholder="AI가 따라야 할 전반적인 가이드라인을 입력하세요..."
          />
        </div>
        
          {/* 프롬프트 미리보기 */}
          {showPromptPreview && (
            <div className="prompt-preview-section">
              <div className="section-header">
                <h3>👁️ 실시간 미리보기</h3>
                <div className="preview-controls">
                  <button
                    onClick={() => {
                      setPromptTesting(prev => ({
                        ...prev,
                        testData: {
                          ...prev.testData,
                          userName: prompt('테스트 사용자 이름을 입력하세요:') || prev.testData.userName
                        }
                      }));
                      generatePromptPreview();
                    }}
                    className="edit-test-data-btn"
                  >
                    테스트 데이터 수정
                  </button>
                  <button
                    onClick={() => {
                      // 강제로 미리보기 업데이트
                      setTimeout(() => {
                        generatePromptPreview();
                      }, 0);
                    }}
                    className="refresh-preview-btn"
                  >
                    🔄 새로고침
                  </button>
                </div>
              </div>
              <div className="preview-content">
                {promptPreview || '미리보기를 생성하려면 프롬프트를 입력하세요.'}
              </div>
            </div>
          )}

          {/* AI 테스트 결과 */}
          {promptTesting.testResult && (
            <div className="test-result-section">
              <h3>🧪 AI 테스트 결과</h3>
              <div className="test-result-content">
                {promptTesting.testResult}
              </div>
              <div className="test-result-actions">
                <button
                  onClick={() => setPromptTesting(prev => ({ ...prev, testResult: '' }))}
                  className="clear-test-btn"
                >
                  결과 지우기
                </button>
                <button
                  onClick={testPromptWithAI}
                  className="retest-btn"
                  disabled={promptTesting.isTestMode}
                >
                  다시 테스트
                </button>
              </div>
            </div>
          )}
        </div>

        {/* 오른쪽 패널 - 고급 설정 */}
        <div className="right-prompt-panel">
          {/* 카드 위치 라벨 */}
          <div className="panel-section">
            <div className="section-header-small">
              <h4>🏷️ 카드 위치 라벨</h4>
              <button 
                onClick={handleGenerateCardPositionLabels} 
                className="mini-generate-btn"
              >
                AI 생성
              </button>
            </div>
            <textarea
              value={promptForm.cardPositionLabels}
              onChange={(e) => setPromptForm(prev => ({ ...prev, cardPositionLabels: e.target.value }))}
              rows={6}
              className={`json-textarea-mini ${!validateJSON(promptForm.cardPositionLabels) ? 'error' : ''}`}
              placeholder='["과거", "현재", "미래"]'
            />
            <div className="field-hint">
              각 카드 위치의 의미를 정의합니다. JSON 배열 형태로 입력하세요.
            </div>
          </div>
          
          {/* 커스텀 변수 */}
          <div className="panel-section">
            <div className="section-header-small">
              <h4>🎨 커스텀 변수</h4>
              <button 
                onClick={handleGenerateCustomVariables} 
                className="mini-generate-btn"
              >
                AI 생성
              </button>
            </div>
            <textarea
              value={promptForm.customVariables}
              onChange={(e) => setPromptForm(prev => ({ ...prev, customVariables: e.target.value }))}
              rows={6}
              className={`json-textarea-mini ${!validateJSON(promptForm.customVariables) ? 'error' : ''}`}
              placeholder='{"mood": "신비로운", "style": "따뜻한"}'
            />
            <div className="field-hint">
              프롬프트에서 사용할 추가 변수들을 JSON 객체로 정의하세요.
          </div>
        </div>
        
          {/* 스프레드 정보 */}
          <div className="panel-section">
            <h4>📋 스프레드 정보</h4>
            <div className="spread-info-compact">
              <div className="info-row">
                <span className="info-label">이름:</span>
                <span className="info-value">{selectedSpread?.name}</span>
            </div>
              <div className="info-row">
                <span className="info-label">카드 수:</span>
                <span className="info-value">{selectedSpread?.cardCount}장</span>
            </div>
              <div className="info-row">
                <span className="info-label">설명:</span>
                <span className="info-value">{selectedSpread?.description || '없음'}</span>
            </div>
              <div className="info-row">
                <span className="info-label">레이아웃:</span>
                <span className="info-value">{selectedSpread?.layoutDescription || '없음'}</span>
            </div>
          </div>
        </div>
        
          {/* 테스트 데이터 설정 */}
          <div className="panel-section">
            <h4>🧪 테스트 데이터</h4>
            <div className="test-data-form">
              <div className="test-input-group">
                <label>사용자 이름</label>
                <input
                  type="text"
                  value={promptTesting.testData.userName}
                  onChange={(e) => setPromptTesting(prev => ({
                    ...prev,
                    testData: { ...prev.testData, userName: e.target.value }
                  }))}
                  className="test-input"
                />
            </div>
              <div className="test-input-group">
                <label>사용자 고민</label>
                <textarea
                  value={promptTesting.testData.userConcern}
                  onChange={(e) => setPromptTesting(prev => ({
                    ...prev,
                    testData: { ...prev.testData, userConcern: e.target.value }
                  }))}
                  rows={3}
                  className="test-textarea"
                />
            </div>
              <div className="test-input-group">
                <div className="label-with-button">
                  <label>테스트 카드 ({selectedSpread?.cardCount || 3}장)</label>
                  <button
                    type="button"
                    onClick={reshuffleTestCards}
                    className="mini-ai-btn"
                    title="카드 재배치"
                  >
                    🎲 재배치
                  </button>
            </div>
                <div className="test-cards-display">
                  {promptTesting.testData.selectedCards.map((cardName, index) => (
                    <div key={index} className="test-card-item">
                      <span className="card-number">{index + 1}.</span>
                      <span className="card-name">{cardName}</span>
            </div>
                  ))}
            </div>
                <input
                  type="text"
                  value={promptTesting.testData.selectedCards.join(', ')}
                  onChange={(e) => setPromptTesting(prev => ({
                    ...prev,
                    testData: { 
                      ...prev.testData, 
                      selectedCards: e.target.value.split(',').map(s => s.trim()).filter(s => s) 
                    }
                  }))}
                  className="test-input"
                  placeholder="직접 편집할 수도 있습니다 (쉼표로 구분)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderCardIntroEditor = () => (
    <div className="card-intro-editor">
      <div className="editor-header">
        <h2>카드 소개 템플릿 편집: {selectedSpread?.name}</h2>
        <div className="editor-actions">
          <button 
            onClick={() => {
              if (selectedSpread) {
                const defaultTemplates = generateDefaultCardIntroTemplates(selectedSpread.cardCount);
                setCardIntroForm(prev => ({
                  ...prev,
                  cardIntroTemplates: JSON.stringify(defaultTemplates, null, 2)
                }));
              }
            }} 
            className="auto-layout-btn"
          >
            기본 템플릿
          </button>
          <button onClick={handleGenerateCardIntro} className="improve-btn">
            AI로 생성
          </button>
          <button onClick={handleSaveCardIntro} className="save-btn">
            저장
          </button>
          <button onClick={() => setActiveTab('list')} className="cancel-btn">
            취소
          </button>
        </div>
      </div>
      
      <div className="card-intro-form">
        {cardIntroSyntaxErrors.length > 0 && (
          <div className="error-messages">
            {cardIntroSyntaxErrors.map((error, index) => (
              <div key={index} className="error-message">{error}</div>
            ))}
          </div>
        )}
        
        <div className="form-section">
          <h3>AI 생성 설정</h3>
          <div className="form-grid" style={{ marginBottom: '20px' }}>
            <div className="form-group">
              <label>타로 스타일</label>
              <select
                value={cardIntroForm.tarotStyle}
                onChange={(e) => setCardIntroForm(prev => ({ ...prev, tarotStyle: e.target.value }))}
              >
                <option value="traditional">전통적 (격식 있고 정중한)</option>
                <option value="modern">현대적 (친근하고 실용적인)</option>
                <option value="intuitive">직관적 (영감을 주고 감성적인)</option>
              </select>
            </div>
            <div className="form-group">
              <label>응답 길이</label>
              <select
                value={cardIntroForm.responseLength}
                onChange={(e) => setCardIntroForm(prev => ({ ...prev, responseLength: e.target.value }))}
              >
                <option value="short">간결함 (1-2문장)</option>
                <option value="medium">적당함 (2-3문장)</option>
                <option value="detailed">상세함 (3-4문장)</option>
              </select>
            </div>
          </div>
        </div>

        <div className="form-section">
          <h3>카드 소개 템플릿 배열</h3>
          <p className="field-description">
            각 카드 위치별로 표시될 소개 텍스트를 정의합니다. 
            배열의 순서는 카드 위치 순서와 일치해야 하며, 현재 <strong>{selectedSpread?.cardCount}장</strong> 스프레드에 맞춰 <strong>{selectedSpread?.cardCount}개</strong>의 템플릿이 필요합니다.
            <br/>
            <strong>사용 가능한 변수:</strong> {`{userName}, {cardName}, {cardIndex}, {totalCards}`}
            <br/>
            💡 <strong>팁:</strong> "기본 템플릿" 버튼으로 카드 수에 맞는 기본 템플릿을 생성하거나, "AI로 생성" 버튼으로 위에서 설정한 스타일에 맞는 맞춤 템플릿을 생성할 수 있습니다.
          </p>
          <textarea
            value={cardIntroForm.cardIntroTemplates}
            onChange={(e) => setCardIntroForm(prev => ({ ...prev, cardIntroTemplates: e.target.value }))}
            rows={20}
            className={`json-textarea ${!validateJSON(cardIntroForm.cardIntroTemplates) ? 'error' : ''}`}
            placeholder={`[
  "{cardIndex}번째 카드, **{cardName}**입니다.\\n\\n이 카드는 {userName}님의 현재 상황을 보여줍니다.",
  "{cardIndex}번째 카드, **{cardName}**입니다.\\n\\n이 카드는 도전과 과제를 나타냅니다.",
  "마지막 {cardIndex}번째 카드, **{cardName}**입니다.\\n\\n이 카드는 미래의 가능성을 보여줍니다."
]`}
          />
          
          <div className="template-help">
            <h4>템플릿 작성 가이드:</h4>
            <ul>
              <li><strong>{`{userName}`}</strong> - 사용자 이름으로 치환됩니다</li>
              <li><strong>{`{cardName}`}</strong> - 선택된 카드의 이름으로 치환됩니다</li>
              <li><strong>{`{cardIndex}`}</strong> - 카드 번호 (1, 2, 3...)로 치환됩니다</li>
              <li><strong>{`{totalCards}`}</strong> - 전체 카드 수로 치환됩니다</li>
              <li><strong>**텍스트**</strong> - 볼드체로 표시됩니다</li>
              <li><strong>\\n</strong> - 줄바꿈으로 표시됩니다</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const renderLayoutEditor = () => (
    <div className="layout-editor-pro">
      <div className="editor-header">
        <h2>프로 배열 편집기: {selectedSpread?.name}</h2>
      </div>

      {/* 툴바 */}
      <div className="layout-toolbar">
        <div className="toolbar-section">
          <button onClick={handleUndo} disabled={undoHistory.length === 0} className="undo-btn">↶ 실행취소</button>
          <button onClick={handleRedo} disabled={redoHistory.length === 0} className="redo-btn">↷ 다시실행</button>
        </div>

        <div className="toolbar-section">
          <label>
            <input 
              type="checkbox" 
              checked={showGrid} 
              onChange={(e) => setShowGrid(e.target.checked)} 
            />
            그리드 표시
          </label>
          <input 
            type="range" 
            min="10" 
            max="50" 
            value={gridSize} 
            onChange={(e) => setGridSize(Number(e.target.value))}
            className="grid-size-slider"
          />
          <span>{gridSize}px</span>
        </div>

        <div className="toolbar-section">
          <label>
            <input 
              type="checkbox" 
              checked={showRulers} 
              onChange={(e) => setShowRulers(e.target.checked)} 
            />
            눈금자 표시
          </label>
        </div>


      </div>

      {/* 메인 작업 영역 */}
      <div className="layout-workspace-pro">
        {/* 왼쪽 패널 - 빠른 배열 템플릿 */}
        <div className="left-panel">
          <h3>빠른 배열</h3>
          <div className="quick-arrangements">
            <button onClick={() => arrangePositions('line')} className="arrangement-btn">
              일렬로 배치
            </button>
            <button onClick={() => arrangePositions('circle')} className="arrangement-btn">
              원형 배치
            </button>
            <button onClick={() => arrangePositions('grid')} className="arrangement-btn">
              격자 배치
            </button>
            <button onClick={() => arrangePositions('cross')} className="arrangement-btn">
              십자 배치
            </button>
            <button onClick={() => arrangePositions('pyramid')} className="arrangement-btn">
              피라미드 배치
            </button>
          </div>

          <h3>AI 도구</h3>
          <div className="ai-tools">
            <button onClick={handleGenerateAILayout} className="ai-btn">
              🤖 스마트 자동 배열
            </button>
            <button onClick={handleImproveLayout} className="ai-btn">
              🔧 배열 최적화
            </button>
          </div>

          <h3>프리셋 관리</h3>
          <div className="preset-manager">
            <input 
              type="text" 
              placeholder="프리셋 이름" 
              id="preset-name-input"
              className="preset-name-input"
            />
            <button 
              onClick={() => {
                const input = document.getElementById('preset-name-input') as HTMLInputElement;
                if (input.value.trim()) {
                  savePreset(input.value.trim());
                  input.value = '';
                }
              }}
              className="save-preset-btn"
            >
              저장
            </button>
            <div className="preset-list">
              {Object.keys(layoutPresets).map(presetName => (
                <div key={presetName} className="preset-item">
                  <span onClick={() => loadPreset(presetName)}>{presetName}</span>
                  <button 
                    onClick={() => {
                      const newPresets = { ...layoutPresets };
                      delete newPresets[presetName];
                      setLayoutPresets(newPresets);
                      localStorage.setItem('tarot-layout-presets', JSON.stringify(newPresets));
                    }}
                    className="delete-preset-btn"
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 중앙 캔버스 */}
        <div className="center-canvas">
          <div className="canvas-header">
            <div className="canvas-info">
              <span>캔버스 크기: {canvasSize.width} × {canvasSize.height}px</span>
              <span>카드 수: {editForm.positions?.length || 0}</span>
            </div>
            <div className="canvas-controls">
              <div className="size-input-group">
                <label>너비:</label>
                <input 
                  type="number" 
                  min="200" 
                  max="1000" 
                  value={canvasSize.width} 
                  onChange={(e) => updateCanvasSize(Number(e.target.value), canvasSize.height)}
                  className="size-input"
                />
                <span>px</span>
              </div>
              <div className="size-input-group">
                <label>높이:</label>
                <input 
                  type="number" 
                  min="150" 
                  max="800" 
                  value={canvasSize.height} 
                  onChange={(e) => updateCanvasSize(canvasSize.width, Number(e.target.value))}
                  className="size-input"
                />
                <span>px</span>
              </div>
            </div>
          </div>
          
          <div 
            className={`canvas-container ${showGrid ? 'show-grid' : ''} ${showRulers ? 'show-rulers' : ''} ${isDragging ? 'dragging' : ''}`}
            style={{ 
              width: canvasSize.width,
              height: canvasSize.height,
              '--grid-size': `${gridSize}px`
            } as React.CSSProperties}
          >
            {showRulers && (
              <>
                <div className="ruler-horizontal"></div>
                <div className="ruler-vertical"></div>
              </>
            )}
            
            {editForm.positions?.map((pos, index) => (
              <div
                key={pos.id}
                className={`position-marker-pro ${
                  selectedPositionId === pos.id ? 'selected' : ''
                } ${selectedPositions.includes(pos.id) ? 'multi-selected' : ''}`}
                style={{
                  left: pos.left,
                  top: pos.top,
                  transform: pos.transform || 'translate(-50%, -50%)'
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  if (e.ctrlKey || e.metaKey) {
                    // 다중 선택
                    setSelectedPositions(prev => 
                      prev.includes(pos.id) 
                        ? prev.filter(id => id !== pos.id)
                        : [...prev, pos.id]
                    );
                  } else {
                    setSelectedPositionId(pos.id);
                    setSelectedPositions([pos.id]);
                  }
                }}
                onMouseDown={(e) => handleMouseDown(e, pos.id)}
              >
                <span className="position-number">{pos.id}</span>
                <div className="position-controls-mini">
                  <button onClick={(e) => { e.stopPropagation(); rotateCard(pos.id, 15); }}>↻</button>
                  <button onClick={(e) => { e.stopPropagation(); rotateCard(pos.id, -15); }}>↺</button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 오른쪽 패널 - 속성 편집 */}
        <div className="right-panel">
          <h3>속성 편집</h3>
          
          {selectedPositionId && (() => {
            const selectedPos = editForm.positions?.find(p => p.id === selectedPositionId);
            if (!selectedPos) return null;
            
            return (
              <div className="property-editor">
                <h4>카드 {selectedPos.id}</h4>
                
                <div className="property-group">
                  <label>X 위치 (%)</label>
                  <input
                    type="number"
                    step="0.1"
                    value={parseFloat(selectedPos.left)}
                    onChange={(e) => updatePosition(selectedPos.id, `${e.target.value}%`, selectedPos.top)}
                  />
                </div>
                
                <div className="property-group">
                  <label>Y 위치 (%)</label>
                  <input
                    type="number"
                    step="0.1"
                    value={parseFloat(selectedPos.top)}
                    onChange={(e) => updatePosition(selectedPos.id, selectedPos.left, `${e.target.value}%`)}
                  />
                </div>
                
                <div className="property-group">
                  <label>회전 (도)</label>
                  <input
                    type="number"
                    value={(() => {
                      const match = selectedPos.transform?.match(/rotate\((-?\d+)deg\)/);
                      return match ? parseInt(match[1]) : 0;
                    })()}
                    onChange={(e) => {
                      const newTransform = selectedPos.transform?.replace(/rotate\(-?\d+deg\)/g, '') || '';
                      updatePosition(selectedPos.id, selectedPos.left, selectedPos.top, 
                        `${newTransform} rotate(${e.target.value}deg)`.trim());
                    }}
                  />
                </div>
                
                <div className="property-group">
                  <label>변형 (CSS Transform)</label>
                  <textarea
                    value={selectedPos.transform || ''}
                    onChange={(e) => updatePosition(selectedPos.id, selectedPos.left, selectedPos.top, e.target.value)}
                    rows={3}
                    className="transform-input"
                  />
                </div>
              </div>
            );
          })()}

          {selectedPositions.length > 1 && (
            <div className="multi-selection-controls">
              <h4>다중 선택 ({selectedPositions.length}개)</h4>
              <button onClick={() => {
                const avgLeft = selectedPositions.reduce((sum, id) => {
                  const pos = editForm.positions?.find(p => p.id === id);
                  return sum + (pos ? parseFloat(pos.left) : 0);
                }, 0) / selectedPositions.length;
                bulkUpdatePositions({ left: `${avgLeft}%` });
              }}>
                수직 정렬
              </button>
              <button onClick={() => {
                const avgTop = selectedPositions.reduce((sum, id) => {
                  const pos = editForm.positions?.find(p => p.id === id);
                  return sum + (pos ? parseFloat(pos.top) : 0);
                }, 0) / selectedPositions.length;
                bulkUpdatePositions({ top: `${avgTop}%` });
              }}>
                수평 정렬
              </button>
              <button onClick={() => {
                selectedPositions.forEach((id, index) => {
                  const spacing = 100 / (selectedPositions.length + 1);
                  updatePosition(id, `${spacing * (index + 1)}%`, '50%');
                });
              }}>
                균등 분배
              </button>
            </div>
          )}
          
          <div className="all-positions-pro">
            <h4>모든 카드 ({editForm.positions?.length || 0}개)</h4>
            <div className="position-list">
              {editForm.positions?.map((pos, index) => (
                <div 
                  key={pos.id} 
                  className={`position-item ${selectedPositionId === pos.id ? 'selected' : ''}`}
                  onClick={() => setSelectedPositionId(pos.id)}
                >
                  <span className="position-label">카드 {pos.id}</span>
                  <span className="position-coords">
                    ({parseFloat(pos.left).toFixed(1)}%, {parseFloat(pos.top).toFixed(1)}%)
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 하단 액션 바 */}
      <div className="layout-actions">
        <button onClick={handleSaveSpread} className="save-btn-large">
          💾 배열 저장
        </button>
        <button onClick={() => setActiveTab('list')} className="close-btn-large">
          ← 목록으로 돌아가기
        </button>
      </div>
    </div>
  );

  const renderGlobalSettings = () => (
    <div className="global-settings">
      <div className="editor-header">
        <h2>글로벌 타로 설정</h2>
        <div className="editor-actions">
          <button onClick={handleSaveGlobalSettings} className="save-btn">
            저장
          </button>
        </div>
      </div>
      
      <div className="global-settings-form">
        <div className="form-section">
          <h3>글로벌 시스템 명령어</h3>
          <p className="field-description">
            모든 타로 해석에 공통으로 적용될 시스템 명령어입니다.
            이 설정은 개별 스프레드의 시스템 명령어보다 우선적으로 적용됩니다.
          </p>
          <textarea
            value={globalSettings.global_system_instruction || ''}
            onChange={(e) => setGlobalSettings(prev => ({ 
              ...prev, 
              global_system_instruction: e.target.value 
            }))}
            rows={10}
            className="instruction-textarea"
            placeholder="예: 반드시 한국어로 응답하고, 긍정적이면서도 현실적인 어조를 사용하세요. 사용자의 이름을 자연스럽게 1-2번 언급하고..."
          />
        </div>
        
        <div className="form-section">
          <h3>기타 글로벌 설정</h3>
          <div className="form-grid">
            <div className="form-group">
              <label>기본 타로 스타일</label>
              <select
                value={globalSettings.default_tarot_style || 'modern'}
                onChange={(e) => setGlobalSettings(prev => ({ 
                  ...prev, 
                  default_tarot_style: e.target.value 
                }))}
              >
                <option value="traditional">전통적</option>
                <option value="modern">현대적</option>
                <option value="intuitive">직관적</option>
              </select>
            </div>
            
            <div className="form-group">
              <label>응답 길이 기본값</label>
              <select
                value={globalSettings.default_response_length || 'medium'}
                onChange={(e) => setGlobalSettings(prev => ({ 
                  ...prev, 
                  default_response_length: e.target.value 
                }))}
              >
                <option value="short">간결함</option>
                <option value="medium">적당함</option>
                <option value="detailed">상세함</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // 글로벌 설정 저장 함수
  const handleSaveGlobalSettings = async () => {
    try {
      const response = await fetch('/api/tarot/global-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ settings: globalSettings }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        alert('글로벌 설정이 성공적으로 저장되었습니다.');
      } else {
        alert('저장 실패: ' + data.message);
      }
    } catch (error) {
      console.error('글로벌 설정 저장 실패:', error);
      alert('저장 중 오류가 발생했습니다.');
    }
  };

  // 그리드에 스냅하는 함수
  const snapToGrid = (value: number, gridSize: number): number => {
    return Math.round(value / gridSize) * gridSize;
  };
  
  // 퍼센트를 픽셀로 변환
  const percentToPixel = (percent: string, containerSize: number): number => {
    return (parseFloat(percent) / 100) * containerSize;
  };
  
  // 픽셀을 퍼센트로 변환
  const pixelToPercent = (pixel: number, containerSize: number): string => {
    return ((pixel / containerSize) * 100).toFixed(1);
  };
  
  // 히스토리 저장
  const saveToHistory = () => {
    if (editForm.positions) {
      setUndoHistory(prev => [...prev.slice(-9), [...editForm.positions!]]);
      setRedoHistory([]); // redo 히스토리 초기화
    }
  };
  
  // 실행 취소
  const handleUndo = () => {
    if (undoHistory.length > 0) {
      const lastState = undoHistory[undoHistory.length - 1];
      setRedoHistory(prev => [editForm.positions || [], ...prev.slice(0, 9)]);
      setUndoHistory(prev => prev.slice(0, -1));
      setEditForm(prev => ({ ...prev, positions: lastState }));
    }
  };
  
  // 다시 실행
  const handleRedo = () => {
    if (redoHistory.length > 0) {
      const nextState = redoHistory[0];
      setUndoHistory(prev => [...prev, editForm.positions || []]);
      setRedoHistory(prev => prev.slice(1));
      setEditForm(prev => ({ ...prev, positions: nextState }));
    }
  };
  
  // 위치들을 정렬하는 함수
  const arrangePositions = (arrangement: 'line' | 'circle' | 'grid' | 'cross' | 'pyramid') => {
    if (!editForm.positions) return;
    
    saveToHistory();
    const count = editForm.positions.length;
    const newPositions = [...editForm.positions];
    
    switch (arrangement) {
      case 'line':
        newPositions.forEach((pos, index) => {
          newPositions[index] = {
            ...pos,
            left: `${(100 / (count + 1)) * (index + 1)}%`,
            top: '50%',
            transform: 'translate(-50%, -50%)'
          };
        });
        break;
        
      case 'circle':
        newPositions.forEach((pos, index) => {
          const angle = (index / count) * 2 * Math.PI;
          const radius = 35; // 35% 반지름
          const x = 50 + radius * Math.cos(angle);
          const y = 50 + radius * Math.sin(angle);
          newPositions[index] = {
            ...pos,
            left: `${x}%`,
            top: `${y}%`,
            transform: 'translate(-50%, -50%)'
          };
        });
        break;
        
      case 'grid':
        const cols = Math.ceil(Math.sqrt(count));
        const rows = Math.ceil(count / cols);
        newPositions.forEach((pos, index) => {
          const row = Math.floor(index / cols);
          const col = index % cols;
          const x = (100 / (cols + 1)) * (col + 1);
          const y = (100 / (rows + 1)) * (row + 1);
          newPositions[index] = {
            ...pos,
            left: `${x}%`,
            top: `${y}%`,
            transform: 'translate(-50%, -50%)'
          };
        });
        break;
        
      case 'cross':
        if (count >= 5) {
          const center = Math.floor(count / 2);
          newPositions.forEach((pos, index) => {
            if (index === center) {
              newPositions[index] = { ...pos, left: '50%', top: '50%', transform: 'translate(-50%, -50%)' };
            } else if (index < center) {
              newPositions[index] = { ...pos, left: `${30 + index * 10}%`, top: '25%', transform: 'translate(-50%, -50%)' };
            } else {
              newPositions[index] = { ...pos, left: `${30 + (index - center - 1) * 10}%`, top: '75%', transform: 'translate(-50%, -50%)' };
            }
          });
        }
        break;
        
      case 'pyramid':
        const pyramidRows = Math.ceil((-1 + Math.sqrt(1 + 8 * count)) / 2);
        let cardIndex = 0;
        for (let row = 0; row < pyramidRows && cardIndex < count; row++) {
          const cardsInRow = row + 1;
          const startX = 50 - (cardsInRow - 1) * 10;
          for (let col = 0; col < cardsInRow && cardIndex < count; col++) {
            newPositions[cardIndex] = {
              ...newPositions[cardIndex],
              left: `${startX + col * 20}%`,
              top: `${20 + row * 25}%`,
              transform: 'translate(-50%, -50%)'
            };
            cardIndex++;
          }
        }
        break;
    }
    
    setEditForm(prev => ({ ...prev, positions: newPositions }));
  };
  
  // 선택된 위치들에 대한 일괄 변경
  const bulkUpdatePositions = (changes: Partial<Pick<SpreadPosition, 'left' | 'top' | 'transform'>>) => {
    if (selectedPositions.length === 0) return;
    
    saveToHistory();
    setEditForm(prev => ({
      ...prev,
      positions: prev.positions?.map(pos => {
        if (selectedPositions.includes(pos.id)) {
          return { ...pos, ...changes };
        }
        return pos;
      }) || []
    }));
  };
  
  // 프리셋 저장/로드
  const savePreset = (name: string) => {
    if (editForm.positions) {
      setLayoutPresets(prev => ({
        ...prev,
        [name]: [...editForm.positions!]
      }));
      localStorage.setItem('tarot-layout-presets', JSON.stringify({
        ...layoutPresets,
        [name]: editForm.positions
      }));
    }
  };
  
  const loadPreset = (name: string) => {
    const preset = layoutPresets[name];
    if (preset) {
      saveToHistory();
      setEditForm(prev => ({ ...prev, positions: [...preset] }));
    }
  };
  
  // 캔버스 크기 변경
  const updateCanvasSize = (width: number, height: number) => {
    setCanvasSize({ width, height });
  };

  // 개선된 드래그 이벤트 핸들러들
  const handleMouseDown = (e: React.MouseEvent, positionId: string) => {
    e.preventDefault();
    e.stopPropagation();
    
    const rect = (e.currentTarget.parentElement as HTMLElement)?.getBoundingClientRect();
    if (!rect) return;
    
    saveToHistory();
    
    const offsetX = e.clientX - rect.left;
    const offsetY = e.clientY - rect.top;
    
    setIsDragging(true);
    setDraggedCardId(positionId);
    setDragOffset({ x: offsetX, y: offsetY });
    setDragStartPos({ x: e.clientX, y: e.clientY });
    
    // 선택 상태 업데이트
    if (e.ctrlKey || e.metaKey) {
      setSelectedPositions(prev => 
        prev.includes(positionId) 
          ? prev.filter(id => id !== positionId)
          : [...prev, positionId]
      );
    } else {
      setSelectedPositionId(positionId);
      setSelectedPositions([positionId]);
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !draggedCardId) return;
    
    const canvas = document.querySelector('.canvas-container') as HTMLElement;
    if (!canvas) return;
    
    const rect = canvas.getBoundingClientRect();
    
    // 마우스 위치를 캔버스 내 상대 좌표로 변환
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // 퍼센트로 변환
    let leftPercent = (x / rect.width) * 100;
    let topPercent = (y / rect.height) * 100;
    
    // 경계 체크
    leftPercent = Math.max(2, Math.min(98, leftPercent));
    topPercent = Math.max(2, Math.min(98, topPercent));
    
    // 그리드 스냅핑 적용
    if (showGrid) {
      const gridStepX = (gridSize / rect.width) * 100;
      const gridStepY = (gridSize / rect.height) * 100;
      leftPercent = snapToGrid(leftPercent, gridStepX);
      topPercent = snapToGrid(topPercent, gridStepY);
    }
    
    // 위치 업데이트
    updatePosition(draggedCardId, `${leftPercent}%`, `${topPercent}%`);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setDraggedCardId(null);
    setDragOffset({ x: 0, y: 0 });
  };

  // 드래그 이벤트 리스너 등록/해제
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'none'; // 드래그 중 텍스트 선택 방지
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.userSelect = '';
      };
    }
  }, [isDragging, draggedCardId, dragOffset, showGrid, gridSize]);

  // 변수를 프롬프트에 삽입
  const insertVariableIntoPrompt = (variable: string) => {
    const textarea = document.querySelector('.prompt-textarea-enhanced') as HTMLTextAreaElement;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const currentValue = promptForm.promptTemplate;
      const newValue = currentValue.substring(0, start) + `{${variable}}` + currentValue.substring(end);
      
      setPromptForm(prev => ({ ...prev, promptTemplate: newValue }));
      
      // 커서 위치 조정
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + variable.length + 2, start + variable.length + 2);
      }, 0);
    }
  };

  // 프롬프트 히스토리에 추가
  const addPromptToHistory = (prompt: string) => {
    if (prompt.trim() && !promptHistory.includes(prompt)) {
      const newHistory = [prompt, ...promptHistory.slice(0, 9)]; // 최대 10개 유지
      setPromptHistory(newHistory);
      localStorage.setItem('tarot-prompt-history', JSON.stringify(newHistory));
    }
  };

  // 템플릿 라이브러리에 저장
  const saveToTemplateLibrary = (name: string, template: string) => {
    const newLibrary = { ...templateLibrary, [name]: template };
    setTemplateLibrary(newLibrary);
    localStorage.setItem('tarot-prompt-templates', JSON.stringify(newLibrary));
  };

  // 템플릿 라이브러리에서 불러오기
  const loadTemplateFromLibrary = (name: string) => {
    const template = templateLibrary[name];
    if (template) {
      if (promptForm.promptTemplate.trim() && 
          !window.confirm('현재 프롬프트를 덮어쓰시겠습니까?')) {
        return;
      }
      
      // 현재 프롬프트를 히스토리에 추가
      if (promptForm.promptTemplate.trim()) {
        addPromptToHistory(promptForm.promptTemplate);
      }
      
      setPromptForm(prev => ({ ...prev, promptTemplate: template }));
    }
  };



  // 프롬프트 폼 변경 시 실시간 검증 및 통계 업데이트
  useEffect(() => {
    // 프롬프트가 있든 없든 항상 통계와 검증을 업데이트
    const validation = validatePrompt(promptForm.promptTemplate || '');
    setPromptValidation(validation);
    
    const stats = calculatePromptStats(promptForm.promptTemplate || '');
    setPromptStats(stats);
    
    // 미리보기도 항상 업데이트 (빈 상태라도)
    if (showPromptPreview) {
      generatePromptPreview();
    }
  }, [promptForm.promptTemplate, promptForm.customVariables, promptTesting.testData, showPromptPreview, selectedSpread]);

  // 사용 가능한 변수 목록 초기화
  useEffect(() => {
    setAvailableVariables({
      'userName': '사용자 이름',
      'cardCount': '카드 수',
      'cards': '선택된 카드 정보',
      'userConcern': '사용자 고민',
      'spreadName': '스프레드 이름',
      'description': '스프레드 설명'
    });
  }, []);

  // 로컬 스토리지에서 히스토리와 템플릿 불러오기
  useEffect(() => {
    const savedHistory = localStorage.getItem('tarot-prompt-history');
    if (savedHistory) {
      try {
        setPromptHistory(JSON.parse(savedHistory));
      } catch (error) {
        console.warn('프롬프트 히스토리 로드 실패:', error);
      }
    }
    
    const savedTemplates = localStorage.getItem('tarot-prompt-templates');
    if (savedTemplates) {
      try {
        setTemplateLibrary(JSON.parse(savedTemplates));
      } catch (error) {
        console.warn('템플릿 라이브러리 로드 실패:', error);
      }
    }
  }, []);

  // 랜덤 카드 선택 함수 (중복 없음)
  const getRandomCards = (cardCount: number): string[] => {
    const shuffled = [...tarotCardsData].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, cardCount).map(card => card.name);
  };

  // 테스트 카드 재배치 함수
  const reshuffleTestCards = () => {
    const cardCount = selectedSpread?.cardCount || 3;
    const newCards = getRandomCards(cardCount);
    setPromptTesting(prev => ({
      ...prev,
      testData: {
        ...prev.testData,
        selectedCards: newCards
      }
    }));
  };

  return (
    <div className="manager-page">
      <div className="manager-header">
        <h1>타로 관리자</h1>
        <nav className="manager-nav">
          <button 
            className={activeTab === 'list' ? 'active' : ''}
            onClick={() => setActiveTab('list')}
          >
            스프레드 목록
          </button>
          <button 
            className={activeTab === 'editor' ? 'active' : ''}
            onClick={() => setActiveTab('editor')}
            disabled={!isEditing}
          >
            편집기
          </button>
          <button 
            className={activeTab === 'layout' ? 'active' : ''}
            onClick={() => setActiveTab('layout')}
            disabled={!selectedSpread}
          >
            배열 편집
          </button>
          <button 
            className={activeTab === 'prompt' ? 'active' : ''}
            onClick={() => setActiveTab('prompt')}
            disabled={!isPromptEditing}
          >
            프롬프트 편집
          </button>
          <button 
            className={activeTab === 'cardIntro' ? 'active' : ''}
            onClick={() => setActiveTab('cardIntro')}
            disabled={!isCardIntroEditing}
          >
            카드 소개 편집
          </button>
          <button 
            className={activeTab === 'global' ? 'active' : ''}
            onClick={() => {
              setActiveTab('global');
              setIsGlobalEditing(true);
            }}
          >
            글로벌 설정
          </button>
        </nav>
      </div>
      
      <div className="manager-content">
        {activeTab === 'list' && renderSpreadList()}
        {activeTab === 'editor' && renderEditor()}
        {activeTab === 'layout' && renderLayoutEditor()}
        {activeTab === 'prompt' && renderPromptEditor()}
        {activeTab === 'cardIntro' && renderCardIntroEditor()}
        {activeTab === 'global' && renderGlobalSettings()}
      </div>
    </div>
  );
};

export default ManagerPage; 