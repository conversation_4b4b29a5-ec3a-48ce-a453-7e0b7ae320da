{"name": "@prisma/engines-version", "version": "6.7.0-36.3cff47a7f5d65c3ea74883f1d736e41d68ce91ed", "main": "index.js", "types": "index.d.ts", "license": "Apache-2.0", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "prisma": {"enginesVersion": "3cff47a7f5d65c3ea74883f1d736e41d68ce91ed"}, "repository": {"type": "git", "url": "https://github.com/prisma/engines-wrapper.git", "directory": "packages/engines-version"}, "devDependencies": {"@types/node": "18.19.76", "typescript": "4.9.5"}, "files": ["index.js", "index.d.ts"], "scripts": {"build": "tsc -d"}}