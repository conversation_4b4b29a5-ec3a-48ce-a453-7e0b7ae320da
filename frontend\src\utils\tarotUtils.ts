import { TarotCard } from '../data/tarotCardsData';

// 상수 정의
export const YEAR_FORTUNE_COST = 5;
export const NUM_CARDS_FIVE_SPREAD = 5;
export const NUM_CARDS_THREE_SPREAD = 3;

// 커스텀 타로 비용 정의
export const CUSTOM_TAROT_COST_3_CARDS = 3;
export const CUSTOM_TAROT_COST_5_CARDS = 5;
export const CUSTOM_TAROT_COST_7_CARDS = 8;
export const CUSTOM_TAROT_COST_10_CARDS = 10;

// 다크 심리학을 활용한 운세 문구 템플릿 (바넘 효과)
export const FORTUNE_TEMPLATES = [
  "당신은 자신에게 너무 비판적일 때가 있지만, 이것은 당신의 높은 기준에서 비롯됩니다. 오늘 {card1}의 에너지는 당신에게 자기 자신을 더 받아들이라고 조언합니다.",
  "{card1}과 {card2}의 조합은 당신이 평소에는 숨기고 있는 창의적이고 독특한 관점을 가지고 있음을 보여줍니다. 오늘은 그 재능을 표현할 좋은 기회입니다.",
  "당신은 외향적으로 보일 수 있지만, {card3}이(가) 나타내듯이 내면의 평화와 고요함을 소중히 여기는 사람입니다. 오늘은 그 내면의 목소리에 귀를 기울여보세요.",
  "{card1}과 {card5}의 에너지는 당신이 다른 사람들의 필요에 민감하게 반응하는 공감 능력을 가지고 있음을 보여줍니다. 때로는 이것이 부담이 될 수 있지만, 당신의 큰 강점이기도 합니다.",
  "당신은 중요한 순간에 현명한 결정을 내리는 능력이 있습니다. 오늘 {card4}는 그 직관을 믿으라고 조언합니다.",
  "{card2}와 {card3}의 만남은 당신이 과거의 경험으로부터 배우는 지혜를 갖고 있음을 보여줍니다. 이 지혜가 오늘의 도전을 헤쳐나가는데 도움이 될 것입니다.",
  "당신은 다양한 상황에 적응하는 유연성을 가지고 있습니다. {card5}의 메시지는 이 적응력이 앞으로의 변화를 헤쳐나가는데 큰 자산이 될 것임을 암시합니다.",
  "{card1}, {card3}, {card5}의 에너지는 당신이 평소에 인정받지 못한다고 느낄 때가 있지만, 실제로는 주변 사람들이 당신의 가치를 알고 있음을 보여줍니다.",
  "당신은 때때로 완벽주의적인 경향이 있습니다. 오늘 {card4}는 완벽함보다는 진정성을 우선시하라고 조언합니다.",
  "{card2}와 {card4}의 조합은 당신이 삶에서 균형을 찾기 위해 노력하고 있음을 나타냅니다. 오늘은 그 균형점에 더 가까워질 수 있는 날입니다."
];

// 랜덤 카드 선택 함수
export const getRandomCards = (allCards: TarotCard[], count: number): TarotCard[] => {
  const shuffled = [...allCards].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

// 카드 수에 따른 비용 계산 함수
export const getCustomTarotCost = (cardCount: number): number => {
  switch (cardCount) {
    case 3:
      return CUSTOM_TAROT_COST_3_CARDS;
    case 5:
      return CUSTOM_TAROT_COST_5_CARDS;
    case 7:
      return CUSTOM_TAROT_COST_7_CARDS;
    case 10:
      return CUSTOM_TAROT_COST_10_CARDS;
    default:
      return CUSTOM_TAROT_COST_3_CARDS;
  }
};

// 운세 타입에 따른 카드 수 반환 함수
export const getCardCountByFortuneType = (fortuneType: string): number => {
  switch (fortuneType) {
    case 'today':
      return NUM_CARDS_THREE_SPREAD;
    case 'year':
      return NUM_CARDS_FIVE_SPREAD;
    case 'customThreeCard':
      return 3;
    case 'customFiveCard':
      return 5;
    case 'customSevenCard':
      return 7;
    case 'customTenCard':
      return 10;
    default:
      return NUM_CARDS_THREE_SPREAD;
  }
};

// 운세 타입에 따른 API 타입 변환 함수
export const getApiFortuneType = (fortuneType: string): string => {
  switch (fortuneType) {
    case 'today':
      return 'todayThreeCard';
    case 'year':
      return 'yearFiveCard';
    case 'customThreeCard':
      return 'customThreeCard';
    case 'customFiveCard':
      return 'customFiveCard';
    case 'customSevenCard':
      return 'customSevenCard';
    case 'customTenCard':
      return 'customTenCard';
    default:
      return 'todayThreeCard';
  }
};

// 카드 소개 텍스트 생성 함수
export const generateCardIntroductionText = (
  card: TarotCard, 
  index: number, 
  activeSpreads?: any[]
): string => {
  const cardPosition = index + 1;
  
  // 기본 소개 텍스트
  let introText = `${cardPosition}번째 카드는 "${card.name}"입니다. `;
  
  // 카드의 기본 의미 추가
  if (card.meaning) {
    introText += `이 카드는 ${card.meaning}을(를) 상징합니다. `;
  }
  
  // 위치별 의미 추가 (활성 스프레드가 있는 경우)
  if (activeSpreads && activeSpreads.length > 0) {
    const activeSpread = activeSpreads[0]; // 첫 번째 활성 스프레드 사용
    if (activeSpread.positions && activeSpread.positions[index]) {
      const position = activeSpread.positions[index];
      if (position.meaning) {
        introText += `이 위치는 "${position.meaning}"을(를) 나타냅니다. `;
      }
    }
  }
  
  return introText;
};

// 전체 화면 뷰 목록
export const getFullScreenViews = (): string[] => {
  return [
    'meditationIntro',
    'cardCountSelection', 
    'cardDrawing',
    'cardRevealAndInterpretation',
    'finalInterpretation',
    'resultEnd',
    'cooldownMessage',
    'loginRequiredMessage',
    'creditsRequiredMessage'
  ];
};

// 운세 관련 뷰 목록
export const getFortuneViews = (): string[] => {
  return [
    'meditationIntro', 
    'cardCountSelection', 
    'cardRevealAndInterpretation', 
    'finalInterpretation', 
    'resultEnd'
  ];
};

// 일반적인 가명/닉네임 목록
export const getGenericNames = (): string[] => {
  return [
    '홍길동', '김아무개', '닉네임', '테스트', '1234', 
    'aaaa', 'test', 'asdf', '유저', 'user', '익명'
  ];
};

// 명상 텍스트 생성 함수
export const generateMeditationTexts = (
  userName: string,
  userConcern: string,
  fortuneType: string
): string[] => {
  const isYearFortune = fortuneType === 'year';
  const isCustomFortune = ['customThreeCard', 'customFiveCard', 'customSevenCard', 'customTenCard'].includes(fortuneType);

  let text1 = "잠시 우주의 기운을 느껴보세요..."; // Default for today
  let text2 = `${userName}님을 위한 별의 메시지가 준비되고 있습니다.`; // Default for today
  let text3 = "마음의 준비가 되셨다면, 아래 버튼을 눌러주세요."; // Default for today

  if (isYearFortune) {
    text1 = "한 해의 지혜를 구하기 전, 잠시 우주의 거대한 흐름을 느껴보세요...";
    text2 = `이제 ${userName}님의 다가올 한 해를 위한 별들의 속삭임이 준비되고 있습니다.`;
    text3 = "깊은 심호흡과 함께, 아래 버튼을 눌러 운명의 카드들을 맞이하세요.";
  } else if (isCustomFortune) {
    text1 = "당신의 질문이 우주에 닿았습니다...";
    text2 = `"${userConcern}" 에 대한 답을 찾기 위해, 별들이 정렬되고 있습니다.`;
    text3 = "마음이 평온해지면, 카드를 선택하여 그들의 속삭임을 들어보세요.";
  }

  return [text1, text2, text3];
};

// 시간 포맷팅 함수
export const formatRemainingTime = (hours: number, minutes: number): string => {
  if (hours > 0) {
    return `약 ${hours}시간 ${minutes}분`;
  } else {
    return `약 ${minutes}분`;
  }
};

// 에러 메시지 생성 함수
export const generateErrorMessage = (errorType: string, additionalInfo?: any): string => {
  switch (errorType) {
    case 'network':
      return '네트워크 연결을 확인해주세요. 잠시 후 다시 시도해주세요.';
    case 'server':
      return '서버에 일시적인 문제가 발생했습니다. 잠시 후 다시 시도해주세요.';
    case 'credits':
      return `크레딧이 부족합니다. 필요한 크레딧: ${additionalInfo?.required || 0}, 보유 크레딧: ${additionalInfo?.current || 0}`;
    case 'cooldown':
      const { hours = 0, minutes = 0 } = additionalInfo?.remainingTime || {};
      return `이미 해당 운세를 보셨습니다. ${formatRemainingTime(hours, minutes)} 후에 다시 시도해주세요.`;
    case 'login_required':
      return '로그인이 필요한 서비스입니다. 로그인 후 이용해주세요.';
    case 'validation':
      return additionalInfo?.message || '입력 정보를 확인해주세요.';
    default:
      return '알 수 없는 오류가 발생했습니다. 잠시 후 다시 시도해주세요.';
  }
};

// 카드 이름에서 특수문자 제거 함수
export const sanitizeCardName = (cardName: string): string => {
  return cardName.replace(/[^\w\s가-힣]/g, '').trim();
};

// 운세 결과 공유 텍스트 생성 함수
export const generateShareText = (
  fortuneType: string,
  selectedCards: TarotCard[],
  finalInterpretation?: string
): string => {
  const fortuneTypeText = fortuneType === 'today' ? '오늘의 운세' : 
                         fortuneType === 'year' ? '올해의 운세' : '타로 운세';
  
  const cardNames = selectedCards.map(card => card.name).join(', ');
  
  let shareText = `🔮 ${fortuneTypeText} 결과\n\n`;
  shareText += `선택된 카드: ${cardNames}\n\n`;
  
  if (finalInterpretation) {
    const shortInterpretation = finalInterpretation.length > 100 
      ? finalInterpretation.substring(0, 100) + '...' 
      : finalInterpretation;
    shareText += `해석: ${shortInterpretation}\n\n`;
  }
  
  shareText += `#타로 #운세 #신비`;
  
  return shareText;
};
