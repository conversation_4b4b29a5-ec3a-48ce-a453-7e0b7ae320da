const axios = require('axios');

// 테스트용 타로 리딩 API 호출
async function testTarotReading() {
  try {
    console.log('=== 타로 리딩 히스토리 테스트 시작 ===');
    
    const testData = {
      userName: "테스트사용자",
      userConcern: "최근 연애 운이 어떨까요? 새로운 만남이 있을지 궁금합니다.",
      fortuneType: "custom_three_card",
      selectedCards: [
        {
          name: "The Fool",
          description: "새로운 시작과 무한한 가능성을 상징하는 카드",
          image: "fool.jpg"
        },
        {
          name: "The Lovers",
          description: "사랑과 선택, 화합을 상징하는 카드",
          image: "lovers.jpg"
        },
        {
          name: "The Star",
          description: "희망과 영감, 치유를 상징하는 카드",
          image: "star.jpg"
        }
      ]
    };
    
    console.log('타로 리딩 API 호출 중...');
    
    const response = await axios.post('http://localhost:3000/api/fortune/generate', testData, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Test-Client/1.0'
      },
      timeout: 60000 // 60초 타임아웃
    });
    
    console.log('✅ 타로 리딩 성공!');
    console.log('응답 길이:', response.data.interpretation?.length || 0, '자');
    
    // 잠시 대기 후 히스토리 확인
    setTimeout(async () => {
      console.log('\n=== 히스토리 저장 확인 ===');
      
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();
      
      try {
        const history = await prisma.userQuestionHistory.findMany({
          orderBy: { createdAt: 'desc' },
          take: 1
        });
        
        if (history.length > 0) {
          const latest = history[0];
          console.log('✅ 최신 히스토리 저장 확인:');
          console.log('- 질문:', latest.question.substring(0, 50) + '...');
          console.log('- 질문 타입:', latest.questionType);
          console.log('- 응답 요약:', latest.responseSummary.substring(0, 50) + '...');
          console.log('- 생성 시간:', latest.createdAt);
        } else {
          console.log('❌ 히스토리가 저장되지 않았습니다.');
        }
      } catch (error) {
        console.error('히스토리 확인 중 오류:', error);
      } finally {
        await prisma.$disconnect();
      }
    }, 3000); // 3초 대기
    
  } catch (error) {
    console.error('❌ 타로 리딩 테스트 실패:', error.message);
    if (error.response) {
      console.error('응답 상태:', error.response.status);
      console.error('응답 데이터:', error.response.data);
    }
  }
}

testTarotReading(); 