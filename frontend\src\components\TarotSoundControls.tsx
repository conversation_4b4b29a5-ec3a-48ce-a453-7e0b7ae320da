import React from 'react';

interface TarotSoundControlsProps {
  isSoundMuted: boolean;
  setIsSoundMuted: (muted: boolean) => void;
  soundVolume: number;
  setSoundVolume: (volume: number) => void;
  audioRef: React.RefObject<HTMLAudioElement | null>;
  effectAudioRef: React.RefObject<HTMLAudioElement | null>;
}

const TarotSoundControls: React.FC<TarotSoundControlsProps> = ({
  isSoundMuted,
  setIsSoundMuted,
  soundVolume,
  setSoundVolume,
  audioRef,
  effectAudioRef,
}) => {
  const toggleMute = () => {
    setIsSoundMuted(!isSoundMuted);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setSoundVolume(newVolume);
  };

  return (
    <>
      {/* 사운드 컨트롤 */}
      <div className="sound-controls">
        <button 
          className="sound-toggle-btn" 
          onClick={toggleMute}
          aria-label={isSoundMuted ? "음소거 해제" : "음소거"}
        >
          {isSoundMuted ? (
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M16.5 12C16.5 10.23 15.5 8.71 14 7.97V10.18L16.45 12.63C16.48 12.43 16.5 12.22 16.5 12ZM19 12C19 12.94 18.8 13.82 18.46 14.64L19.97 16.15C20.63 14.91 21 13.5 21 12C21 7.72 18 4.14 14 3.23V5.29C16.89 6.15 19 8.83 19 12ZM4.27 3L3 4.27L7.73 9H3V15H7L12 20V13.27L16.25 17.52C15.58 18.04 14.83 18.46 14 18.7V20.77C15.38 20.45 16.63 19.82 17.68 18.96L19.73 21L21 19.73L12 10.73L4.27 3ZM12 4L9.91 6.09L12 8.18V4Z" fill="currentColor"/>
            </svg>
          ) : (
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 9V15H7L12 20V4L7 9H3ZM16.5 12C16.5 10.23 15.5 8.71 14 7.97V16.02C15.5 15.29 16.5 13.77 16.5 12ZM14 3.23V5.29C16.89 6.15 19 8.83 19 12C19 15.17 16.89 17.85 14 18.71V20.77C18.01 19.86 21 16.28 21 12C21 7.72 18.01 4.14 14 3.23Z" fill="currentColor"/>
            </svg>
          )}
        </button>
        
        <div className="volume-control">
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={soundVolume}
            onChange={handleVolumeChange}
            className="volume-slider"
            aria-label="볼륨 조절"
          />
          <span className="volume-label">{Math.round(soundVolume * 100)}%</span>
        </div>
      </div>

      {/* 오디오 엘리먼트들 */}
      <audio ref={audioRef} /> {/* 배경 음악용 오디오 */}
      <audio ref={effectAudioRef} /> {/* 효과음용 오디오 */

      <style jsx>{`
        .sound-controls {
          position: fixed;
          top: 100px;
          right: 20px;
          display: flex;
          align-items: center;
          gap: 10px;
          background: rgba(20, 20, 40, 0.8);
          backdrop-filter: blur(10px);
          padding: 10px 15px;
          border-radius: 25px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          z-index: 1000;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .sound-toggle-btn {
          background: none;
          border: none;
          color: #e0e0e0;
          cursor: pointer;
          padding: 5px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
        }

        .sound-toggle-btn:hover {
          color: #fff59d;
          background: rgba(255, 245, 157, 0.1);
        }

        .volume-control {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .volume-slider {
          width: 80px;
          height: 4px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 2px;
          outline: none;
          cursor: pointer;
          -webkit-appearance: none;
          appearance: none;
        }

        .volume-slider::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 16px;
          height: 16px;
          background: #fff59d;
          border-radius: 50%;
          cursor: pointer;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
          transition: all 0.3s ease;
        }

        .volume-slider::-webkit-slider-thumb:hover {
          background: #ffeb3b;
          transform: scale(1.1);
        }

        .volume-slider::-moz-range-thumb {
          width: 16px;
          height: 16px;
          background: #fff59d;
          border-radius: 50%;
          cursor: pointer;
          border: none;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
          transition: all 0.3s ease;
        }

        .volume-slider::-moz-range-thumb:hover {
          background: #ffeb3b;
          transform: scale(1.1);
        }

        .volume-label {
          font-size: 0.8rem;
          color: #c0c0c0;
          min-width: 35px;
          text-align: center;
          font-family: 'Nanum Gothic', sans-serif;
        }

        /* 모바일 반응형 */
        @media (max-width: 768px) {
          .sound-controls {
            top: 90px;
            right: 15px;
            padding: 8px 12px;
          }

          .volume-slider {
            width: 60px;
          }

          .volume-label {
            font-size: 0.7rem;
            min-width: 30px;
          }
        }

        @media (max-width: 480px) {
          .sound-controls {
            top: 85px;
            right: 10px;
            padding: 6px 10px;
          }

          .volume-control {
            gap: 6px;
          }

          .volume-slider {
            width: 50px;
          }
        }
      `}</style>
    </>
  );
};

export default TarotSoundControls;
