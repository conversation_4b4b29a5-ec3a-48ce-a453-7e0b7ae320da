/* TarotTutorialPage.css - Modern Dark Theme to match HomePage */

.tarot-tutorial-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 30%, #2d4a5a 70%, #3e5b7a 100%);
  color: #f8f9fa;
  padding: 50px 5% 60px 5%; /* 상단 패딩을 줄임 - 헤더 높이는 CSS 변수로 관리됨 */
  position: relative;
  overflow-x: hidden;
  line-height: 1.7;
}

/* Floating particles background */
.tarot-tutorial-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.2), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255, 138, 128, 0.3), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(79, 195, 247, 0.2), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(240, 147, 251, 0.2), transparent);
  background-size: 250px 120px;
  animation: sparkleFloat 30s linear infinite;
}

@keyframes sparkleFloat {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  25% { transform: translateY(-8px) translateX(3px); }
  50% { transform: translateY(-15px) translateX(0px); }
  75% { transform: translateY(-8px) translateX(-3px); }
}

.tarot-tutorial-page .page-title {
  text-align: center;
  color: #f8f9fa;
  margin-bottom: 50px;
  font-size: 3.2rem;
  font-weight: 700;
  background: linear-gradient(45deg, #ff8a80, #f093fb, #4fc3f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
  position: relative;
  z-index: 1;
}

/* Introduction paragraph styling */
.tarot-tutorial-page > p {
  text-align: center;
  margin-bottom: 50px;
  font-size: 1.2rem;
  color: #d1d9e0;
  font-weight: 300;
  line-height: 1.8;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 1;
}

.tutorial-section {
  margin-bottom: 50px;
  padding: 35px 40px;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 25px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.tutorial-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.03), transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tutorial-section:hover::before {
  opacity: 1;
}

.tutorial-section:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.tutorial-section .section-heading {
  font-size: 1.8rem;
  color: #ffffff;
  margin-top: 0;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.section-heading-icon {
  margin-right: 15px;
  width: 1.4em;
  height: 1.4em;
  color: #4fc3f7;
  vertical-align: middle;
  filter: drop-shadow(0 2px 4px rgba(79, 195, 247, 0.3));
}

.tutorial-section p, .tutorial-section ul, .tutorial-section ol {
  font-size: 1.1rem;
  margin-bottom: 25px;
  line-height: 1.8;
  color: #d1d9e0;
  font-weight: 300;
}

.tutorial-section ul, .tutorial-section ol {
  margin-left: 25px;
  padding-left: 20px;
}

.tutorial-section ul li {
  margin-bottom: 18px;
  padding-left: 10px;
  position: relative;
}

.tutorial-section ul li::before {
  position: absolute;
  left: -20px;
  top: 0px;
}

/* Star icon for specific list items */
.list-item-sparkle::before {
  content: '\2728'; /* ✨ */
  position: absolute;
  left: -20px;
  top: 0px;
  color: #f093fb;
  filter: drop-shadow(0 0 4px rgba(240, 147, 251, 0.5));
}

/* Specific styles for Minor Arcana suit items */
.minor-arcana-item::before {
  position: absolute;
  left: -20px;
  top: 0px;
  font-size: 1em;
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.3));
}

.minor-arcana-wands::before {
  content: '\1F525'; /* 🔥 */
  color: #ff8a80;
}

.minor-arcana-cups::before {
  content: '\1F4A7'; /* 💧 */
  color: #4fc3f7;
}

.minor-arcana-swords::before {
  content: '\1F4A8'; /* 💨 */
  color: #b8c5d1;
}

.minor-arcana-pentacles::before {
  content: '\1F333'; /* 🌳 */
  color: #81c784;
}

/* Icons for Spread Method list items */
.spread-item-one-card::before {
  content: '\1F4CD'; /* 📍 */
  position: absolute;
  left: -20px;
  top: 0px;
  color: #ff8a80;
  font-size: 1.1em;
  filter: drop-shadow(0 0 3px rgba(255, 138, 128, 0.5));
}

.spread-item-three-card::before {
  content: '\1F570\FE0F'; /* 🕰️ */
  position: absolute;
  left: -20px;
  top: 0px;
  color: #4fc3f7;
  font-size: 1.1em;
  filter: drop-shadow(0 0 3px rgba(79, 195, 247, 0.5));
}

.tutorial-section strong {
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.tutorial-section ol li {
  margin-bottom: 15px;
  padding-left: 5px;
}

/* Key phrase and highlight styles */
.list-item-intro-phrase {
  font-weight: 600;
  color: #f093fb;
  font-size: 1.1rem;
  display: block;
  margin-bottom: 8px;
  text-shadow: 0 1px 2px rgba(240, 147, 251, 0.3);
}

.soft-highlight {
  color: #4fc3f7;
  background: rgba(79, 195, 247, 0.15);
  padding: 2px 6px;
  border-radius: 6px;
  font-weight: 500;
  border: 1px solid rgba(79, 195, 247, 0.2);
  backdrop-filter: blur(5px);
}

/* Clickable Arcana Title Style */
.clickable-arcana-title {
  color: #f093fb;
  font-weight: bold;
  cursor: pointer;
  display: inline-block;
  position: relative;
  padding-bottom: 3px;
  border-bottom: 2px dotted rgba(240, 147, 251, 0.6);
  transition: all 0.3s ease;
  text-shadow: 0 1px 2px rgba(240, 147, 251, 0.3);
}

.clickable-arcana-title::after {
  content: ' (카드 목록 보기 ←)';
  font-size: 0.8em;
  color: #4fc3f7;
  margin-left: 4px;
  font-weight: normal;
}

.clickable-arcana-title:hover {
  color: #ffffff;
  border-bottom-color: #f093fb;
  transform: translateY(-1px);
  text-shadow: 0 2px 8px rgba(240, 147, 251, 0.6);
}

.clickable-arcana-title:hover::after {
  color: #ffffff;
}

/* Clickable Card Name in Text Style */
.clickable-card-name-in-text {
  color: #ff8a80;
  font-weight: bold;
  cursor: pointer;
  border-bottom: 1px dashed rgba(255, 138, 128, 0.6);
  transition: all 0.3s ease;
  padding-bottom: 1px;
  text-shadow: 0 1px 2px rgba(255, 138, 128, 0.3);
}

.clickable-card-name-in-text:hover {
  color: #ffffff;
  border-bottom-color: #ff8a80;
  transform: translateY(-1px);
  text-shadow: 0 2px 8px rgba(255, 138, 128, 0.6);
}

/* 본문 내 작은 카드 이미지 스타일 */
.inline-tarot-card-image {
  width: 45px;
  height: auto;
  vertical-align: middle;
  margin: 0 6px;
  border-radius: 6px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
}

.inline-tarot-card-image:hover {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.5);
  filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.4));
}

/* "오늘의 첫 카드 뽑기" 체험 섹션 스타일 */
.daily-card-draw-section {
  text-align: center;
  padding: 35px;
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  margin-top: 30px;
  position: relative;
  overflow: hidden;
}

.daily-card-draw-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.05), transparent 60%);
  opacity: 0.5;
}

.daily-card-draw-section .draw-button {
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.daily-card-draw-section .draw-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.daily-card-draw-section .draw-button:hover::before {
  left: 100%;
}

.daily-card-draw-section .draw-button:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.5);
}

.daily-card-draw-section .draw-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.daily-card-draw-section .drawn-card-area {
  margin-top: 30px;
  padding: 30px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: inline-block;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  animation: magicalCardReveal 3s ease-out forwards;
  opacity: 0;
  min-width: 320px;
  position: relative;
  z-index: 1;
}

.daily-card-draw-section .drawn-card-image {
  width: 160px;
  height: auto;
  border-radius: 15px;
  margin-bottom: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.3));
}

.daily-card-draw-section .drawn-card-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.daily-card-draw-section .drawn-card-description {
  font-size: 1rem;
  color: #d1d9e0;
  line-height: 1.6;
  max-width: 280px;
  margin: 0 auto;
  font-weight: 300;
}

/* Enhanced magical animations */
@keyframes magicalCardReveal {
  0% {
    opacity: 0;
    transform: scale(0.7) translateY(30px) rotate(-10deg);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  }
  30% {
    opacity: 0.8;
    transform: scale(1.05) translateY(-10px) rotate(5deg);
    box-shadow: 0 15px 35px rgba(240, 147, 251, 0.3);
  }
  60% {
    opacity: 1;
    transform: scale(0.95) translateY(0px) rotate(-2deg);
    box-shadow: 0 12px 25px rgba(240, 147, 251, 0.4);
  }
  80% {
    transform: scale(1.02) translateY(-2px) rotate(1deg);
    box-shadow: 0 18px 40px rgba(240, 147, 251, 0.45);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0) rotate(0deg);
    box-shadow: 0 10px 30px rgba(240, 147, 251, 0.5);
  }
}

@keyframes magicalCardDisappear {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0) rotate(0deg);
    box-shadow: 0 10px 30px rgba(240, 147, 251, 0.5);
  }
  30% {
    transform: scale(1.05) rotate(5deg);
  }
  100% {
    opacity: 0;
    transform: scale(0.5) translateY(50px) rotate(15deg);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  }
}

.drawn-card-area.disappearing {
  animation: magicalCardDisappear 0.7s ease-in forwards;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tarot-tutorial-page {
    padding: 30px 3% 40px 3%; /* 우측 네비게이터 공간 확보를 위해 조정 */
  }

  .tarot-tutorial-page .page-title {
    font-size: 2.5rem;
    margin-bottom: 30px;
  }

  .tutorial-section {
    padding: 25px 20px;
    margin-bottom: 30px;
  }

  .tutorial-section .section-heading {
    font-size: 1.5rem;
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .section-heading-icon {
    margin-right: 0;
    margin-bottom: 5px;
  }

  .daily-card-draw-section {
    padding: 25px 15px;
  }

  .daily-card-draw-section .drawn-card-area {
    min-width: 280px;
    padding: 25px;
  }

  .daily-card-draw-section .drawn-card-image {
    width: 140px;
  }
}

@media (max-width: 480px) {
  .tarot-tutorial-page {
    padding: 20px 10px 40px 3%; /* 작은 화면에서 우측 네비게이터 고려 */
  }

  .tarot-tutorial-page .page-title {
    font-size: 2rem;
  }

  .tutorial-section {
    padding: 20px 15px;
  }

  .tutorial-section .section-heading {
    font-size: 1.3rem;
  }

  .tutorial-section p, .tutorial-section ul, .tutorial-section ol {
    font-size: 1rem;
  }

  .daily-card-draw-section .drawn-card-area {
    min-width: 250px;
    padding: 20px;
  }

  .daily-card-draw-section .drawn-card-image {
    width: 120px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .tutorial-section,
  .daily-card-draw-section .draw-button,
  .tarot-tutorial-page::before,
  .inline-tarot-card-image {
    animation: none;
    transition: none;
  }
  
  .tutorial-section:hover {
    transform: scale(1.01);
  }
  
  .daily-card-draw-section .draw-button:hover {
    transform: scale(1.02);
  }
}

/* Focus states for keyboard navigation */
.clickable-arcana-title:focus,
.clickable-card-name-in-text:focus,
.daily-card-draw-section .draw-button:focus {
  outline: 2px solid #4fc3f7;
  outline-offset: 2px;
} 