/* ArcanaModal.css */
.arcana-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1002;
  opacity: 0;
  animation: fadeInOverlay 0.3s forwards;
}

.arcana-modal-content {
  background-color: #fff;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  transform: translateY(-20px);
  animation: slideInModal 0.4s forwards ease-out;
}

.arcana-modal-close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  font-size: 28px;
  font-weight: bold;
  color: #888;
  cursor: pointer;
  line-height: 1;
  padding: 0;
}

.arcana-modal-close-button:hover {
  color: #333;
}

.arcana-modal-title {
  text-align: center;
  font-size: 1.8em;
  color: #7E57C2; /* 기존 테마 색상 활용 */
  margin-top: 0;
  margin-bottom: 25px;
}

.arcana-modal-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.arcana-modal-list-item {
  display: flex;
  align-items: flex-start;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
  opacity: 0;
  transform: translateX(-20px);
  animation: listItemSlideIn 0.4s forwards ease-out;
}

.arcana-modal-list-item:last-child {
  border-bottom: none;
}

.arcana-modal-card-image {
  width: 50px;
  height: auto;
  margin-right: 20px;
  border-radius: 4px;
  border: 1px solid #ddd;
  flex-shrink: 0;
}

.arcana-modal-card-details {
  display: flex;
  flex-direction: column;
}

.arcana-modal-card-name {
  font-size: 1.1em;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.arcana-modal-card-description {
  font-size: 0.9em;
  color: #555;
  line-height: 1.5;
  margin: 0;
}

/* Animations */
@keyframes fadeInOverlay {
  to {
    opacity: 1;
  }
}

@keyframes slideInModal {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes listItemSlideIn {
  to {
    opacity: 1;
    transform: translateX(0);
  }
} 