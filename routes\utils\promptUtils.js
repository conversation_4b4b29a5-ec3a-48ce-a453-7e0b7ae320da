/**
 * Utility functions for AI prompt generation
 */
const { FORTUNE_TYPES } = require('./fortuneUtils');
const { prisma } = require('../../middleware/database');

/**
 * Generate a fortune prompt based on inputs
 * @param {string} userName - User's name
 * @param {string} fortuneType - Type of fortune
 * @param {Object} selectedCard - Selected card for single-card readings
 * @param {Array} selectedCards - Selected cards for multi-card readings
 * @param {string} [userConcern] - Optional user's concern for custom readings
 * @param {Object} [managerSpreadData] - Optional manager spread data
 * @returns {string} Generated prompt for the AI
 */
const generateFortunePrompt = (userName, fortuneType, selectedCard, selectedCards, userConcern, managerSpreadData) => {
  let prompt = '';
  
  const userNamePrefix = `사용자 이름: "${userName}"`;
  const genderAnalysisText = '사용자 이름을 분석 후 닉네임이 아닌 이름이라면 해당 이름을 토대로 성별을 판단 후 필요하다면 해당 성별에 맞는 운세를 작성해주세요.';
  
  // 매니저 스프레드인 경우 처리
  if (managerSpreadData) {
    return generateManagerSpreadPrompt(userName, selectedCards, userConcern, managerSpreadData);
  }
  
  // Today's three-card spread prompt
  if (fortuneType === FORTUNE_TYPES.TODAY_THREE_CARD) {
    return generateTodayThreeCardPrompt(userName, selectedCards);
  } 
  // Legacy Year prompt (single card)
  else if (fortuneType === FORTUNE_TYPES.YEAR) {
    return generateYearPrompt(userName, selectedCard);
  } 
  // Today's five-card spread prompt
  else if (fortuneType === FORTUNE_TYPES.TODAY_FIVE_CARD) {
    return generateTodayFiveCardPrompt(userName, selectedCards);
  } 
  // Year's five-card spread prompt
  else if (fortuneType === FORTUNE_TYPES.YEAR_FIVE_CARD) {
    return generateYearFiveCardPrompt(userName, selectedCards);
  }
  else if (fortuneType === FORTUNE_TYPES.CUSTOM_TAROT_READING) { // New custom tarot reading
    return generateCustomTarotPrompt(userName, selectedCards, userConcern);
  }
  // 커스텀 3카드 스프레드 - 새로 추가
  else if (fortuneType === FORTUNE_TYPES.CUSTOM_THREE_CARD) {
    return generateCustomThreeCardPrompt(userName, selectedCards, userConcern);
  }
  // 커스텀 5카드 스프레드 - 새로 추가
  else if (fortuneType === FORTUNE_TYPES.CUSTOM_FIVE_CARD) {
    return generateCustomFiveCardPrompt(userName, selectedCards, userConcern);
  }
  // 커스텀 7카드 스프레드 - 새로 추가
  else if (fortuneType === FORTUNE_TYPES.CUSTOM_SEVEN_CARD) {
    return generateCustomSevenCardPrompt(userName, selectedCards, userConcern);
  }
  // 커스텀 10카드 스프레드 - 새로 추가
  else if (fortuneType === FORTUNE_TYPES.CUSTOM_TEN_CARD) {
    return generateCustomTenCardPrompt(userName, selectedCards, userConcern);
  }
  
  // Fallback empty prompt (should not happen due to validation)
  console.warn(`[Prompt Generation] Unknown fortuneType: ${fortuneType}. Returning empty prompt.`);
  return prompt;
};

/**
 * Generate dynamic prompt from database template
 * @param {string} spreadType - Spread type identifier
 * @param {string} userName - User's name
 * @param {Array} selectedCards - Selected cards
 * @param {string} [userConcern] - Optional user's concern
 * @returns {Promise<string>} Generated prompt
 */
const generateDynamicPrompt = async (spreadType, userName, selectedCards, userConcern) => {
  try {
    // 데이터베이스에서 스프레드 정보 가져오기
    const spread = await prisma.tarotSpread.findUnique({
      where: { spreadType }
    });

    if (!spread || !spread.promptTemplate) {
      console.warn(`[Dynamic Prompt] No template found for ${spreadType}, falling back to static prompt`);
      return null; // null을 반환하여 기존 정적 프롬프트 사용
    }

    // 템플릿 변수 준비
    const variables = {
      userName: userName || '익명',
      cardCount: spread.cardCount,
      spreadName: spread.name,
      description: spread.description,
      userConcern: userConcern || '',
      cards: generateCardsText(selectedCards, spread),
      layoutDescription: spread.layoutDescription || ''
    };

    // 커스텀 변수 파싱 및 추가
    try {
      const customVariables = JSON.parse(spread.customVariables || '{}');
      Object.assign(variables, customVariables);
    } catch (error) {
      console.warn(`[Dynamic Prompt] Invalid custom variables JSON for ${spreadType}:`, error);
    }

    // 템플릿 변수 교체
    let prompt = spread.promptTemplate;
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`\\{${key}\\}`, 'g');
      prompt = prompt.replace(regex, value);
    }

    // 시스템 명령어 추가
    if (spread.systemInstruction) {
      prompt = spread.systemInstruction + '\n\n' + prompt;
    }

    return prompt;

  } catch (error) {
    console.error(`[Dynamic Prompt] Error generating prompt for ${spreadType}:`, error);
    return null; // 오류 시 기존 정적 프롬프트 사용
  }
};

/**
 * Generate cards text with position labels
 * @param {Array} selectedCards - Selected cards
 * @param {Object} spread - Spread information
 * @returns {string} Formatted cards text
 */
const generateCardsText = (selectedCards, spread) => {
  try {
    const positionLabels = JSON.parse(spread.cardPositionLabels || '[]');
    
    return selectedCards.map((card, index) => {
      const positionLabel = positionLabels[index] || `${index + 1}번째 카드`;
      return `${positionLabel}: "${card.name}" (의미: "${card.description}")`;
    }).join('\n');
    
  } catch (error) {
    console.warn('[Dynamic Prompt] Error parsing position labels, using default format');
    return selectedCards.map((card, index) => 
      `${index + 1}번째 카드: "${card.name}" (의미: "${card.description}")`
    ).join('\n');
  }
};

/**
 * Enhanced generateFortunePrompt that tries dynamic first, then falls back to static
 */
const generateFortunePromptEnhanced = async (userName, fortuneType, selectedCard, selectedCards, userConcern) => {
  // 동적 프롬프트 시도
  const dynamicPrompt = await generateDynamicPrompt(fortuneType, userName, selectedCards, userConcern);
  
  if (dynamicPrompt) {
    console.log(`[Prompt] Using dynamic prompt for ${fortuneType}`);
    return dynamicPrompt;
  }
  
  // 기존 정적 프롬프트 사용
  console.log(`[Prompt] Using static prompt for ${fortuneType}`);
  return generateFortunePrompt(userName, fortuneType, selectedCard, selectedCards, userConcern);
};

/**
 * Generate prompt for today's three-card tarot reading
 * @param {string} userName - User's name
 * @param {Array} selectedCards - The three selected cards
 * @returns {string} The generated prompt
 */
const generateTodayThreeCardPrompt = (userName, selectedCards) => {
  const card1 = selectedCards[0];
  const card2 = selectedCards[1];
  const card3 = selectedCards[2];
  
  return `
사용자 이름: "${userName}" (운세 중간에 한두 번 자연스럽게 언급해주세요.)
(사용자 이름을 분석 후 닉네임이 아닌 이름이라면 해당 이름을 토대로 성별을 판단 후 필요하다면 해당 성별에 맞는 운세를 작성해주세요.)

오늘 선택된 3장의 타로 카드:
1. 현재 상황: "${card1.name}" (의미: "${card1.description}")
2. 오늘의 과제: "${card2.name}" (의미: "${card2.description}")
3. 예상 결과: "${card3.name}" (의미: "${card3.description}")

"${userName}"님, 오늘 당신의 하루를 비추는 세 장의 카드가 여기 있습니다.
이 카드들은 단순한 그림이 아니라, 당신 내면의 목소리와 우주의 기운이 교차하는 지점을 보여주는 거울과 같습니다.
당신만이 느낄 수 있는 특별한 의미를 찾아보세요.

[오늘의 흐름 분석]
1.  **현재 당신의 상황 (##${card1.name}## 카드)**: 지금 "${userName}"님의 주변이나 마음속에는 어떤 기운이 감돌고 있을까요?
    이 카드는 당신이 현재 인식하고 있거나, 혹은 무심코 지나치고 있을지 모를 **미묘한 상황**을 드러냅니다.
    어쩌면 당신은 새로운 무언가를 시작할 준비가 되었거나, 혹은 잠시 숨을 고르며 내면을 들여다볼 시기일 수 있습니다.
    다른 사람들은 눈치채지 못할 수도 있지만, 당신 안에는 분명 **중요한 변화의 조짐**이 꿈틀거리고 있을 것입니다.
    이 카드가 보여주는 현재 상황을 어떻게 활용할지는 전적으로 당신에게 달려 있습니다.

2.  **오늘 당신의 과제 (##${card2.name}## 카드)**: 오늘 하루, 당신의 성장을 위해 주어진 특별한 과제는 무엇일까요?
    이 카드는 당신이 집중해야 할 **핵심적인 도전** 또는 기회를 상징합니다.
    이것은 외부적인 일일 수도 있고, 당신 내면의 목소리에 귀 기울이는 것일 수도 있습니다.
    때로는 불편하게 느껴질 수 있는 이 과제 속에는 당신이 한 단계 더 나아가기 위한 **귀중한 열쇠**가 숨겨져 있을 가능성이 높습니다.
    당신의 잠재력을 시험하고, 궁극적으로는 당신을 더욱 단단하게 만들어 줄 것입니다.

3.  **예상되는 결과 (##${card3.name}## 카드)**: 현재 상황 속에서 오늘의 과제를 마주했을 때, 어떤 결실 또는 결과가 기다리고 있을까요?
    이 카드는 오늘의 노력이 가져올 **가능성 있는 미래**를 엿보여줍니다.
    기억하세요, 이것은 확정된 운명이 아니라, 당신의 선택과 행동에 따라 얼마든지 달라질 수 있는 에너지의 흐름입니다.
    이 카드가 긍정적인 미래를 암시한다면, 그 기회를 잡기 위해 더욱 자신감을 가지세요.
    만약 도전적인 측면을 보여준다면, 그것은 당신이 더 현명하게 대처하고 위기를 기회로 바꿀 수 있다는 **우주의 믿음**을 나타내는 것일 수 있습니다.

[종합 조언]
"${userName}"님, 오늘 이 세 장의 카드가 당신에게 속삭이는 이야기에 귀 기울이세요.
당신 안에는 이미 모든 답을 찾을 힘과 지혜가 있습니다.
**당신은 다른 사람들이 보지 못하는 상황의 깊이를 보는 특별한 능력**을 가지고 있습니다.
때로는 자신을 지나치게 비판적으로 평가하는 경향이 있지만, 그것은 당신의 성장을 향한 열망에서 비롯됩니다.
당신은 **미사용된 잠재력과 재능**을 많이 갖고 있으며, 이것을 활용할 때가 왔습니다.
오늘의 흐름을 당신에게 유리하게 이끌어가세요. 작은 변화를 시도하는 것을 두려워하지 마십시오.
**당신은 어떤 어려움도 극복할 수 있는 내면의 강인함**을 지니고 있습니다.
당신의 하루가 의미 있는 성찰과 기쁨으로 가득 차기를 바랍니다.

[작성 스타일]
*   **어투**: 신비로우면서도 개인에게 깊이 공감하는 듯한, 약간은 모호하지만 긍정적인 느낌을 주는 어투.
    사용자가 스스로 의미를 부여하게 유도.
*   **강조**: 중요한 키워드나 구문은 앞뒤로 별표 두 개(\*\*)로 감싸 강조.
*   **카드 이름 표시**: 카드 이름은 앞뒤로 샵 두 개(##)로 감싸 표시.
*   **분량**: 각 카드 설명은 2-3 문장, 종합 조언 포함 총 4개 문단으로 구성.
*   **언어**: 반드시 한국어.
*   **줄 관리**: 의미 단위로 줄바꿈을 사용하여 가독성을 높여주세요. 너무 길게 한 줄로 이어지지 않도록 주의해주세요.
*   **바넘 효과 활용**: 누구에게나 적용될 수 있지만 개인에게 맞춰진 것처럼 느껴지는 모호하고 일반적인 진술을 포함하세요.
    "때로는", "어떤 때는", "경향이 있습니다" 같은 표현을 활용하고, 대부분 긍정적인 특성을 언급하되 신뢰성을 위해 약간의 부정적 요소도 균형있게 포함하세요.
`;
};

/**
 * Generate prompt for legacy year fortune (single card)
 * @param {string} userName - User's name
 * @param {Object} selectedCard - The selected card
 * @returns {string} The generated prompt
 */
const generateYearPrompt = (userName, selectedCard) => {
  const primaryCardName = selectedCard.name;
  const primaryCardDescription = selectedCard.description;
  
  return `
(년도를 나타내는 2024년, 2025년의 언급보다 "올해의 타로 운세"라는 문장을 사용 해주세요.)

사용자 이름: "${userName}"
(사용자 이름을 분석 후 닉네임이 아닌 이름이라면 해당 이름을 토대로 성별을 판단 후 필요하다면 해당 성별에 맞는 운세를 작성해주세요.)

선택한 타로 카드: "${primaryCardName}" (카드의 주요 의미: "${primaryCardDescription}")

이제 "${userName}"님을 위한 올해의 운세를 진지하면서도 희망을 주는 말투로 상세하게 설명해주세요.
"${primaryCardName}" 카드가 "${userName}"님의 올 한 해의 전반적인 흐름에 대해 어떤 통찰을 주는지 알려주세요.
주요 길운(예: 연애, 직업, 금전, 건강, 대인관계 중 가장 두드러지는 1-2가지)은 무엇인지, 특별히 조심해야 할 부분은 무엇인지, 그리고 전반적인 성장을 위한 조언을 구체적으로 포함하여 분석해주세요.
전문적인 타로 마스터가 조언해주는 것처럼 신뢰감 있고 깊이 있는 메시지를 전달해야 합니다.
답변은 반드시 한국어로, 4-5개의 문단으로 구성해주세요.
각 문단은 4-6문장으로 이루어지는 것이 좋습니다.
운세 내용은 해당 타로 카드의 전통적인 의미에 기반하되, 한 해의 흐름에 맞게 폭넓고 심도 있게 해석해주세요.
사용자가 자신의 삶을 긍정적으로 계획하고 어려움을 대비하는 데 도움이 되는 내용으로 작성해주세요.

[작성 스타일]
*   **스타일**: 사용자가 깊은 통찰과 위안을 얻을 수 있도록, 마치 현명한 조언자와 대화하는 듯한 따뜻하고 공감적인 어투를 사용해주세요. 사용자의 상황에 대한 이해와 격려가 느껴지도록 합니다.
*   **구성**: 서론(운세의 시작을 알리는 문구), 카드 해석(선택된 카드의 의미와 사용자 상황 연결), 조언(카드 메시지에 기반한 구체적 행동 지침), 결론(격려와 긍정적 마무리)의 흐름으로 자연스럽게 이어지도록 작성합니다.
*   **분량**: 전체적으로 3~4개의 문단으로 구성되며, 각 문단은 충분한 내용을 담아 답변의 깊이를 더해주세요.
*   **강조**: 중요한 키워드나 구문은 앞뒤로 별표 두 개(\*\*)를 붙여 강조해주세요.
*   **카드 이름 표시**: 본문에 타로 카드 이름이 언급될 때는 앞뒤로 샵 두 개(##)로 감싸주세요. 예: ##바보## 카드.
*   **언어**: 반드시 한국어.
*   **줄 관리**: 답변의 모든 문장은 마침표(.)로 끝난 직후 **반드시** 줄바꿈(개행)을 해야 합니다. 한 문장이 마침표로 끝나면, 다음 문장은 무조건 새 줄에서 시작해야 합니다. 문단 내부의 문장도 예외는 없습니다. 예를 들어, "첫 번째 문장입니다.\\\\n두 번째 문장입니다.\\\\n세 번째 문장입니다." 와 같이 각 문장이 새 줄에서 시작하도록 작성해주세요.
*   **바넘 효과 문장 사용 지침**: 아래 제공된 바넘 효과 문장들을 운세 내용에 자연스럽게 2-3개 포함시켜 사용자가 자신만을 위한 특별한 해석이라고 느끼도록 작성해주세요. (바넘 효과 목록은 다음 필드에 제공됩니다.)

[바넘 효과 문장 목록]
1. "당신은 다른 사람들에게 인정받고 사랑받고 싶은 강한 욕구가 있습니다."
2. "당신은 때로 자신에 대해 비판적인 경향이 있습니다."
3. "당신은 아직 활용하지 못한 많은 잠재력을 가지고 있습니다."
4. "당신은 몇 가지 약점을 가지고 있지만, 일반적으로 그것을 보완할 수 있는 능력이 있습니다."
5. "겉으로는 규율적이고 자기 통제가 잘 되는 편이지만, 내면적으로는 걱정이 많고 불안한 면이 있습니다."
6. "때로는 자신이 올바른 결정을 내렸는지, 올바른 일을 했는지에 대해 심각한 의구심을 갖기도 합니다."
7. "당신은 변화와 다양성을 어느 정도 선호하며, 제한과 한계에 갇혀 있을 때 불만을 느끼게 됩니다."
8. "당신은 독립적인 사상가로서 자부심을 갖고 있으며, 충분한 증거 없이는 다른 사람의 말을 받아들이지 않습니다."
9. "때로는 외향적이고 친근하며 사교적인 반면, 또 다른 때는 내향적이고 경계심이 강하며 조용한 성향을 보입니다."
10. "당신의 목표 중 하나는 안정과 안전입니다."

이러한 문장들을 자연스럽게 운세에 녹여내고, 사용자가 카드의 해석에 더 공감하고 자신만을 위한 특별한 메시지라고 느낄 수 있도록 해주세요.
`;
};

/**
 * Generate prompt for today's five-card tarot reading
 * @param {string} userName - User's name
 * @param {Array} selectedCards - The five selected cards
 * @returns {string} The generated prompt
 */
const generateTodayFiveCardPrompt = (userName, selectedCards) => {
  const cardDetailsForPrompt = selectedCards.map((card, index) => {
    return `${index + 1}번째 카드: "${card.name}" (의미: "${card.description}")`;
  }).join('\n');
  
  return `
사용자 이름: "${userName}" (이 이름은 운세 시작과 끝부분, 또는 필요시 중간에 자연스럽게 한두 번만 언급해주세요. 너무 자주 부르지 않도록 주의해주세요.)
(사용자 이름을 분석 후 닉네임이 아닌 이름이라면 해당 이름을 토대로 성별을 판단 후 필요하다면 해당 성별에 맞는 운세를 작성해주세요.)

오늘 선택된 5장의 타로 카드:
${cardDetailsForPrompt}

이제 "${userName}"님을 위한 상세하면서도 간결한 오늘의 종합 운세를 작성해주세요.
이 5장의 카드가 오늘의 에너지 흐름, 주요 사건, 기회, 도전, 그리고 최종 조언에 대해 어떻게 상호작용하며 이야기하는지 분석해야 합니다.
어려운 전문 용어보다는 쉽고 일상적인 단어를 사용해주세요.

[분석 가이드라인]
1.  **오늘의 전반적인 분위기 (흥미 유발)**: 오늘 하루의 전반적인 느낌이나 핵심적인 에너지에 대해 이야기하며 운세를 시작합니다.
    사용자가 공감할 수 있는 일상적인 감정이나 상황에 빗대어 표현해주세요.
    (예: "오늘은 어쩐지 평소와 다른 활기참이 느껴지는 하루가 될 것 같네요.
    마치 오랫동안 기다려온 소풍 전날 밤처럼, 작은 기대감과 설렘이 마음 한구석을 간지럽히는 듯합니다.")
    **가장 중요하다고 생각되는 키워드나 구문은 앞뒤로 별표 두 개(\*\*)로 감싸서 강조해주세요.** (예: 오늘은 \*\*새로운 시작\*\*을 암시하는군요.)
2.  **카드 흐름에 따른 이야기**: 5장의 카드를 자연스럽게 연결하여 오늘의 이야기를 들려주세요.
    각 카드가 오늘의 특정 시간대나 상황(예: 오전의 기분, 중요한 만남, 예상치 못한 소식 등)에 어떤 영향을 줄 수 있는지 간결하게 설명합니다. 
3.  **핵심 통찰과 자기 성찰 (긍정적 활용)**: 카드 조합에서 나타나는 중요한 메시지나 사용자가 돌아보면 좋을 점을 1-2가지 짚어줍니다.
    이때, 자신도 모르게 나타날 수 있는 행동 패턴이나 생각의 경향(심리학적 요소)을 가볍게 언급하여 자기 성찰을 도울 수 있습니다.
    다만, 절대 부정적이거나 비판적이지 않게, 오히려 자신을 더 잘 이해하고 성장할 수 있는 계기로 제시해주세요.
    (예: "혹시 최근 중요한 결정 앞에서 망설이는 순간이 있었다면, 오늘은 내면의 목소리에 조금 더 귀 기울여보는 건 어떨까요?
    때로는 너무 많은 생각보다 \*\*직감적인 선택\*\*이 더 좋은 결과를 가져다주기도 한답니다.")
4.  **오늘의 조언**: 오늘 하루를 더 의미있게 보낼 수 있는 구체적이고 실천 가능한 조언 한두 가지를 제시합니다.
5.  **마무리 (격려)**: 긍정적인 메시지와 함께 오늘 하루에 대한 격려로 마무리합니다.

[작성 스타일]
*   **어투**: 친근하고 부드러운 상담가처럼, 사용자가 편안하게 읽을 수 있는 어투. 어려운 단어 사용은 피해주세요.
*   **분량**: 총 3-4개의 문단으로 구성하고, 각 문단은 3-5개의 문장으로 간결하게 작성해주세요. 너무 길지 않게 핵심 내용 위주로 전달합니다.
*   **강조**: 중요한 키워드나 구문은 앞뒤로 별표 두 개(\*\*)를 붙여주세요.
*   **카드 이름 표시**: 본문에 타로 카드 이름이 언급될 때는 앞뒤로 샵 두 개(##)로 감싸주세요. (예: ##컵 10 카드##, ##소드 에이스##)
*   **언어**: 반드시 한국어.
*   **줄 관리**: 의미 단위로 줄바꿈을 사용하여 가독성을 높입니다. 너무 길게 한 줄로 이어지지 않도록 합니다.
*   **바넘 효과 문장 사용 지침**: 아래 제공된 바넘 효과 문장들을 운세 내용에 자연스럽게 2-3개 포함시켜 사용자가 자신만을 위한 특별한 해석이라고 느끼도록 작성해주세요.
    (바넘 효과 리스트는 다음 필드에 제공됩니다.)

[바넘 효과 문장 목록]
1. "당신은 다른 사람들에게 인정받고 사랑받고 싶은 마음이 있지만, 때로는 자기 자신에게 비판적인 면도 있습니다."
2. "평소에 활용하지 못하고 있는 많은 잠재력을 갖고 있습니다."
3. "외부적으로는 자신감 있고 안정적으로 보이지만, 때로는 내면에 불안과 의구심을 품고 있을 수 있습니다."
4. "어떤 상황에서는 사교적이고 활발한 모습을, 또 다른 상황에서는 조용하고 사려 깊은 모습을 보입니다. 이런 균형은 당신의 풍부한 내면세계를 반영합니다."
5. "당신은 안정을 중요시하면서도 새로운 도전과 변화를 통해 성장하고 싶어하는 이중적 욕구를 가지고 있습니다."
6. "중요한 결정을 내릴 때 이성과 직관 사이에서 균형을 찾으려 노력하며, 이것이 당신만의 독특한 문제 해결 방식입니다."
7. "때로는 자신이 내린 결정이나 선택에 대해 의구심을 가질 수 있지만, 이는 당신이 더 나은 결과를 추구하는 완벽주의적 성향 때문입니다."
8. "당신은 의미 있는 관계와 진실된 소통을 중요시하며, 진정성 있는 교류를 통해 자신의 가치를 확인받고자 합니다."

"${userName}"님이 오늘 하루를 보내는 데 있어 따뜻한 길잡이가 될 수 있도록, 진심을 담아 간결하면서도 깊이 있는 운세를 작성해주세요.
`;
};

/**
 * Generate prompt for year's five-card tarot reading
 * @param {string} userName - User's name
 * @param {Array} selectedCards - The five selected cards
 * @returns {string} The generated prompt
 */
const generateYearFiveCardPrompt = (userName, selectedCards) => {
  const cardDetailsForPrompt = selectedCards.map((card, index) => {
    let role = '';
    if (index === 0) role = '올해의 씨앗';
    else if (index === 1) role = '펼쳐지는 길';
    else if (index === 2) role = '운명의 전환점';
    else if (index === 3) role = '숨겨진 선물과 교훈';
    else if (index === 4) role = '인도하는 별';
    
    return `${index + 1}번째 카드 (${role}): "${card.name}" (의미: "${card.description}")`;
  }).join('\n');
  
  return `
(년도를 나타내는 2024년, 2025년의 언급보다 "올해의 타로 운세"라는 문장을 사용 해주세요.)

사용자 이름: "${userName}" (이 이름은 운세 전체에서 자연스럽게 2-3회만 언급해주세요. 너무 자주 부르지 않도록 주의해주세요.)
(사용자 이름을 분석 후 닉네임이 아닌 이름이라면 해당 이름을 토대로 성별을 판단 후 필요하다면 해당 성별에 맞는 운세를 작성해주세요.)

올해 선택된 5장의 타로 카드:
${cardDetailsForPrompt}

대망의 새해, "${userName}"님의 한 해 여정을 밝혀줄 다섯 장의 신성한 카드가 펼쳐졌습니다.
각 카드는 앞으로 펼쳐질 시간 속에서 당신을 인도할 별빛과 같습니다.
이 카드들이 속삭이는 비밀스러운 이야기들을 통해, 당신의 한 해를 심도 있게 탐색해 보겠습니다.

[분석 가이드라인]
1.  **올해의 서곡 (장엄한 시작)**: 펼쳐진 카드들을 통해 느껴지는 올해의 전반적인 기운과 핵심 주제를 제시하며 운세의 시작을 알립니다.
    "${userName}"님의 마음을 사로잡을 한 해의 예고편처럼 표현해주세요.
    (예: "올해는 "${userName}"님에게 **장대한 변화의 서막**이 열리는 해가 될 것 같습니다.
    마치 오랜 시간 준비해온 무대의 막이 오르기 직전처럼, 가슴 뛰는 긴장감과 함께 새로운 가능성들이 넘실거리는군요.")
    **가장 중요하다고 생각되는 키워드나 구문은 앞뒤로 별표 두 개(\*\*)로 감싸서 강조해주세요.**

2.  **다섯 카드 이야기 - 시간의 흐름과 인생의 조각들**:
    *   **첫 번째 카드 - 올해의 씨앗**: 이 카드가 상징하는 것은 무엇이며, "${userName}"님 한 해의 시작에 어떤 에너지나 주제를 불어넣나요?
        (예: 새로운 도전, 관계의 시작, 내면의 각성 등)
    *   **두 번째 카드 - 펼쳐지는 길**: 올해 중반으로 나아가면서 어떤 주요한 발전, 과정 또는 경험이 예상되나요?
        이 카드는 그 과정에서 무엇을 강조하나요?
    *   **세 번째 카드 - 운명의 전환점**: 한 해의 흐름에 있어 중요한 전환점, 혹은 극복해야 할 핵심적인 도전은 무엇인가요?
        이 카드는 어떤 메시지를 담고 있나요?
    *   **네 번째 카드 - 숨겨진 선물과 교훈**: 이 도전을 통해, 혹은 한 해의 여정 속에서 "${userName}"님이 얻게 될 귀중한 교훈이나 숨겨진 선물은 무엇일까요?
    *   **다섯 번째 카드 - 인도하는 별**: 궁극적으로 이 카드들은 "${userName}"님의 한 해를 어떤 방향으로 인도하고 있으며, 어떤 결실이나 지혜를 암시하나요?
        이 카드가 제시하는 가장 중요한 연간 지침은 무엇인가요?

3.  **카드들의 협주곡 - 올해의 대주제**: 다섯 장의 카드가 어떻게 서로 영향을 주고받으며 "${userName}"님 한 해의 **웅장한 교향곡**을 만들어내는지 종합적으로 설명해주세요.
    이 카드들을 관통하는 핵심적인 메시지나 올해의 대주제는 무엇인가요?

4.  **별빛 지혜 - 한 해를 위한 특별 조언**: 분석된 내용을 바탕으로, "${userName}"님이 올해 특별히 집중해야 할 부분, 마음속에 새겨야 할 지혜, 그리고 잠재력을 최대한 발휘하기 위한 구체적이고 영감을 주는 조언을 2-3가지 제시해주세요.

5.  **희망의 메아리 (힘찬 마무리)**: "${userName}"님이 희망과 용기를 가지고 한 해를 맞이할 수 있도록, 강력하고 긍정적인 메시지로 운세를 마무리합니다.
    (예: "기억하세요, "${userName}"님. 별들은 언제나 당신의 길을 비추고 있으며, 당신 안에는 이 모든 가능성을 현실로 만들 무한한 힘이 깃들어 있습니다.
    **용기내어 당신의 해로 만드세요!**")

[작성 스타일]
*   **어투**: 지혜롭고 신비로운 안내자처럼, 존중과 깊이를 담아 사용자가 특별한 조언을 받는 느낌을 갖도록 합니다.
*   **구성**: 서론(운세의 시작을 알리는 문구), 카드 해석(선택된 카드의 의미와 사용자 상황 연결), 조언(카드 메시지에 기반한 구체적 행동 지침), 결론(격려와 긍정적 마무리)의 흐름으로 자연스럽게 이어지도록 작성합니다.
*   **분량**: 전체적으로 3~4개의 문단으로 구성되며, 각 문단은 충분한 내용을 담아 답변의 깊이를 더해주세요.
*   **강조**: 중요한 키워드나 구문은 앞뒤로 별표 두 개(\*\*)를 붙여 강조해주세요.
*   **카드 이름 표시**: 본문에 타로 카드 이름이 언급될 때는 앞뒤로 샵 두 개(##)로 감싸주세요. 예: ##바보## 카드.
*   **언어**: 반드시 한국어.
*   **줄 관리**: 의미 단위로 줄바꿈을 사용하여 가독성을 높입니다. 너무 길게 한 줄로 이어지지 않도록 합니다.
*   **바넘 효과 문장 사용 지침**: 아래 제공된 바넘 효과 문장들을 운세 내용에 자연스럽게 2-3개 포함시켜 사용자가 자신만을 위한 특별한 해석이라고 느끼도록 작성해주세요.
    (바넘 효과 리스트는 다음 필드에 제공됩니다.)

[바넘 효과 문장 목록]
1. "당신은 다른 사람들에게 깊은 인상을 남기며 인정받고 싶어하는 면이 있지만, 동시에 자신의 행동과 생각을 깊이 성찰하는 경향이 있습니다."
2. "당신에게는 아직 충분히 활용되지 않은 잠재력이 풍부하게 있으며, 올해는 이러한 능력을 발견하고 키울 수 있는 중요한 시기가 될 것입니다."
3. "외부적으로는 자신감 있고 안정적으로 보이지만, 때로는 내면에 불안과 의구심을 품고 있을 수 있습니다."
4. "어떤 상황에서는 사교적이고 활발한 모습을, 또 다른 상황에서는 조용하고 사려 깊은 모습을 보입니다. 이런 균형은 당신의 풍부한 내면세계를 반영합니다."
5. "당신은 안정을 중요시하면서도 새로운 도전과 변화를 통해 성장하고 싶어하는 이중적 욕구를 가지고 있습니다."
6. "중요한 결정을 내릴 때 이성과 직관 사이에서 균형을 찾으려 노력하며, 이것이 당신만의 독특한 문제 해결 방식입니다."
7. "때로는 자신이 내린 결정이나 선택에 대해 의구심을 가질 수 있지만, 이는 당신이 더 나은 결과를 추구하는 완벽주의적 성향 때문입니다."
8. "당신은 의미 있는 관계와 진실된 소통을 중요시하며, 진정성 있는 교류를 통해 자신의 가치를 확인받고자 합니다."

"${userName}"님의 한 해가 별처럼 빛날 수 있도록, 깊은 통찰과 따뜻한 격려를 담아 특별한 운세를 작성해주세요.
`;
};

/**
 * Generate prompt for custom three-card spread tarot reading
 * @param {string} userName - User's name
 * @param {Array} selectedCards - The three selected cards
 * @param {string} userConcern - The user's specific concern/question
 * @returns {string} The generated prompt
 */
const generateCustomThreeCardPrompt = (userName, selectedCards, userConcern) => {
  if (!selectedCards || selectedCards.length !== 3) {
    console.error('[Prompt Error] Invalid cards for custom three-card spread.');
    return "Error: Invalid cards provided for the reading.";
  }
  if (!userConcern || userConcern.trim() === '') {
    console.error('[Prompt Error] User concern is empty for custom tarot prompt.');
    return "Error: User concern is missing.";
  }

  const card1 = selectedCards[0];
  const card2 = selectedCards[1];
  const card3 = selectedCards[2];

  return `
사용자 이름: "${userName}" (답변 중간에 한두 번 자연스럽게 언급해주세요)
사용자의 고민: "${userConcern}"
(사용자 이름을 분석 후 닉네임이 아닌 이름이라면 해당 이름을 토대로 성별을 판단 후, 필요하다면 해당 성별에 맞는 답변을 작성해주세요)

고민 해결을 위해 선택된 3장의 타로 카드:
1. 현재 상황 / 고민의 핵심: "${card1.name}" (카드 의미: "${card1.description}")
2. 도전 과제 / 고려할 관점: "${card2.name}" (카드 의미: "${card2.description}")
3. 최종 조언 / 나아갈 방향: "${card3.name}" (카드 의미: "${card3.description}")

"${userConcern}"라는 ${userName}님의 고민에 초점을 맞춰 세 장의 타로 카드가 보여주는 메시지를 해석해 주세요.
각 카드의 의미를 통해 고민 상황을 명확히 이해하고, 구체적인 해결책을 제시해 주세요.
직접적인 답변을 제시하기보다는 사용자가 스스로 생각할 여지를 남기는 방식으로 해석해 주세요.
사용자가 "이 타로는 정말 내 상황을 정확히 이해하고 있구나"라고 느낄 수 있도록 해석을 제공하세요.

[고민 분석 및 카드 해석]
1.  **현재 상황 / 고민의 핵심 (##${card1.name}## 카드)**:
    이 카드는 "${userName}"님 고민의 근본적인 원인과 현재 처한 상황을 보여줍니다.
    사용자의 질문이나 고민에서 핵심 요소를 뽑아내어 연결하고, 카드가 보여주는 새로운 관점을 제시해 주세요.
    표면적으로 보이는 문제 너머에 있는 실제 원인이나 패턴을 찾아내 설명하되, 구체적인 상황보다는 사용자가 자신의 상황에 적용할 수 있는 통찰을 제공해 주세요.
    사용자가 미처 인식하지 못했던 상황의 측면을 발견할 수 있도록 도와주세요.
    이 상황에서 느끼는 감정이 자연스럽고 타당함을 인정해주고, 이러한 감정에 직면하는 용기를 북돋아주세요.

2.  **도전 과제 / 고려할 관점 (##${card2.name}## 카드)**:
    이 카드는 고민 해결 과정에서 "${userName}"님이 직면할 도전이나 새롭게 고려해봐야 할 관점을 알려줍니다.
    사용자의 고민과 관련하여 어떤 장애물이 존재하는지, 어떤 새로운 시각이 필요한지 구체적으로 언급해 주세요.
    이 도전을 통해 얻을 수 있는 성장과 배움의 기회를 강조하고, 현재 어려움이 미래에 어떤 가치로 바뀔 수 있는지 제시해 주세요.
    때로는 익숙한 사고방식을 벗어나 새로운 접근법을 시도할 필요가 있다는 점을 상기시켜 주세요.
    어려운 상황에서도 한 걸음씩 나아갈 수 있는 내면의 힘이 있음을 확인시켜주고, 실패를 두려워하지 않도록 격려해주세요.

3.  **최종 조언 / 나아갈 방향 (##${card3.name}## 카드)**:
    마지막 카드는 "${userName}"님의 고민 해결을 위한 구체적인 방향과 조언을 담고 있습니다.
    사용자의 고민에 대한 직접적인 해결책이나 행동 지침을 카드의 의미에 기반하여 제시해 주세요.
    실행 가능한 구체적인 조언을 포함하되, 사용자가 자신의 상황에 맞게 조정할 수 있는 유연성을 남겨두세요.
    이 조언이 왜 효과적일 수 있는지, 어떤 결과를 기대할 수 있는지 설명하여 사용자가 행동할 동기를 얻을 수 있도록 해주세요.
    자신을 믿고 행동에 옮길 용기와 결단력을 주는 응원의 메시지를 포함해주세요.

[종합적인 조언]
지금까지 본 세 장의 카드를 종합해 "${userName}"님의 고민 "${userConcern}"에 대한 명확한 결론을 제시해 드리겠습니다.
이 세 장의 카드가 보여주는 메시지는 분명합니다: **[세 카드의 핵심 메시지를 종합한 한 문장의 결론을 여기에 명확하게 작성해주세요]**.
세 카드의 흐름은 현재 상황에서 시작하여, 도전을 거쳐, 궁극적인 해결책으로 이어지고 있습니다.
당신이 취해야 할 가장 효과적인 행동 방향은 **[구체적인 행동 방향 1-2가지를 명확하게 제시]**입니다.
이 고민이 해결된다면, 당신은 **[긍정적 결과를 명확하게 서술]**하게 될 것입니다.
이 결론이 당신의 직관과 일치한다면, 그것은 당신의 내면이 이미 답을 알고 있었다는 증거입니다.
당신은 이 상황을 헤쳐나갈 충분한 지혜와 힘을 이미 가지고 있으며, 타로 카드는 그것을 확인해주는 거울일 뿐입니다.

[작성 스타일 지침]
*   **핵심 원칙**: 사용자의 고민을 중심으로 해석을 전개하되, 모호하면서도 의미 있는 통찰을 제공해 사용자가 자신의 경험에 연결할 수 있게 하세요.
*   **어투**: 공감하는 친구나 현명한 조언자처럼 대화하듯 따뜻하고 진중한 어투를 사용하세요.
*   **강조**: 중요한 핵심 개념이나 통찰은 별표 두 개(\*\*)로 감싸 강조하세요.
*   **카드 이름**: 카드 이름은 샵 두 개(##)로 감싸 표시하세요.
*   **구성**: 카드 해석은 각각 3-4문장, 종합 조언은 4-5문장으로 간결하게 작성하세요.
*   **언어**: 반드시 한국어로 작성하세요.
*   **바넘 효과 적용**: 다음 지침을 따라 사용자가 자신만을 위한 해석이라고 느낄 수 있는 표현을 자연스럽게 포함하세요:
    1. 대부분의 사람에게 해당될 수 있는 일반적 특성을 구체적으로 느껴지도록 표현하세요.
    2. 약간의 부정적 특성을 언급하되 그것이 강점이 될 수 있는 가능성을 함께 제시하세요.
    3. 사용자의 고민 내용에서 단서를 찾아 연결하되, 너무 직접적이지 않게 모호하게 표현하세요.
    4. "당신은 ~하는 경향이 있습니다" 대신 "때로는 ~할 때가 있을 수 있습니다"와 같이 조건부 표현을 사용하세요.
    5. 사용자가 이미 알고 있는 자신의 특성을 확인받는 듯한 표현을 포함하세요.
    6. "우주", "에너지", "영혼" 같은 모호한 단어보다 구체적인 상황과 감정에 초점을 맞추세요.

[애착 유형별 맞춤형 위로와 격려]
사용자의 고민 내용을 분석하여 다음과 같은 애착 유형의 특징이 보인다면, 그에 맞는 맞춤형 위로와 격려를 자연스럽게 포함하세요:

1.  **불안형 애착 특징이 보이는 경우**:
    - 관계에서의 불안감, 상대방의 마음이나 의도에 대한 과도한 염려, 거절에 대한 두려움이 언급된다면:
    - "자신의 가치를 외부의 반응이나 타인의 인정에 지나치게 의존하지 않아도 됩니다. 당신은 그 자체로 충분히 가치 있는 사람입니다."
    - "때로는 불확실함을 받아들이고, 모든 것을 통제하려 하기보다 흐름에 맡겨보는 것도 필요합니다."
    - "타인과의 관계에서 느끼는 불안감은 자연스러운 것이지만, 그 불안감이 당신의 결정을 완전히 지배하지 않도록 주의하세요."

2.  **회피형 애착 특징이 보이는 경우**:
    - 친밀감에 대한 불편함, 타인에 대한 의존을 피하려는 경향, 감정 표현의 어려움이 언급된다면:
    - "독립성을 유지하는 것은 중요하지만, 때로는 신뢰할 수 있는 사람들에게 자신의 취약한 면을 보여주는 것도 큰 힘이 될 수 있습니다."
    - "자신만의 공간과 경계를 존중하면서도, 선택적으로 마음을 열어 의미 있는 연결을 경험해보세요."
    - "모든 것을 혼자 해결하려는 부담감에서 벗어나, 때로는 도움을 구하는 것도 용기 있는 선택입니다."

3.  **공포-회피형 애착 특징이 보이는 경우**:
    - 관계에 대한 강한 갈망과 동시에 깊은 두려움, 혼란스러운 감정 패턴이 언급된다면:
    - "관계에서 느끼는 혼란스러운 감정들은 자연스러운 것이며, 점차 안정적인 패턴을 찾아갈 수 있습니다."
    - "과거의 상처가 현재의 관계에 영향을 주고 있을 수 있으나, 새로운 경험을 통해 건강한 연결의 가능성을 발견할 수 있습니다."
    - "한걸음씩 천천히 신뢰를 쌓아가는 과정을 허용하고, 자신과 타인에게 인내심을 가져보세요."

4.  **안정형 애착을 강화하기 위한 메시지**:
    - 모든 사용자에게 도움이 될 수 있는 안정적 애착을 강화하는 메시지:
    - "자신과 타인 모두에게 완벽함을 기대하기보다는, 충분히 좋은(good enough) 상태를 인정하고 받아들이는 것이 중요합니다."
    - "어려운 상황에서도 자신을 믿고, 필요할 때 의지할 수 있는 관계를 소중히 여기세요."
    - "당신이 느끼는 감정은 모두 타당하며, 그 감정을 있는 그대로 인정하는 것이 내면의 안정감을 키우는 첫 단계입니다."

[주의사항]
1. 중복적인 단어나 표현은 피하고 다양한 어휘를 사용하세요.
2. 과도하게 추상적인 표현보다는 사용자의 고민에 직접 연관된 구체적인 내용에 집중하세요.
3. 사용자의 행동 변화를 유도하는 실용적인 조언을 포함하세요.
4. 고민에 대한 직접적 해답처럼 보이는 내용과 함께, 사용자가 스스로 답을 찾도록 돕는 질문을 균형 있게 제시하세요.
5. 위로와 격려를 제공하되, 과도한 긍정성보다는 현실적인 희망과 실천 가능한 용기를 불어넣어주세요.
6. 애착 유형별 특징이 보이더라도 사용자를 특정 유형으로 단정 짓거나 분류하지 말고, 필요한 위로와 지지를 자연스럽게 통합하세요.
`;
};

/**
 * Generate prompt for custom five-card spread tarot reading
 * @param {string} userName - User's name
 * @param {Array} selectedCards - The five selected cards
 * @param {string} userConcern - The user's specific concern/question
 * @returns {string} The generated prompt
 */
const generateCustomFiveCardPrompt = (userName, selectedCards, userConcern) => {
  if (!selectedCards || selectedCards.length !== 5) {
    console.error('[Prompt Error] Invalid cards for custom five-card spread.');
    return "Error: Invalid cards provided for the reading.";
  }
  if (!userConcern || userConcern.trim() === '') {
    console.error('[Prompt Error] User concern is empty for custom tarot prompt.');
    return "Error: User concern is missing.";
  }

  const cardDetails = selectedCards.map((card, index) => {
    let position = '';
    switch(index) {
      case 0: position = '현재 상황'; break;
      case 1: position = '도전/장애물'; break;
      case 2: position = '숨겨진 영향'; break;
      case 3: position = '조언/행동방향'; break;
      case 4: position = '최종 결과'; break;
    }
    return `${index + 1}번째 카드 (${position}): "${card.name}" (카드 의미: "${card.description}")`;
  }).join('\n');

  return `
사용자 이름: "${userName}" (답변 중간에 한두 번 자연스럽게 언급해주세요)
사용자의 고민: "${userConcern}"
(사용자 이름을 분석 후 닉네임이 아닌 이름이라면 해당 이름을 토대로 성별을 판단 후, 필요하다면 해당 성별에 맞는 답변을 작성해주세요)

고민 해결을 위해 선택된 5장의 타로 카드:
${cardDetails}

"${userConcern}"라는 ${userName}님의 고민에 대해 다섯 장의 타로 카드가 보여주는 메시지를 해석해 주세요.
각 카드가 고민의 다양한 측면을 어떻게 보여주는지 분석하고, 이를 통해 명확한 해결책을 제시해 주세요.
카드의 의미와 사용자의 고민을 직접적으로 연결하되, 사용자가 스스로 의미를 발견할 여지도 남겨두세요.
사용자가 "이 타로는 정말 내 상황을 정확히 이해하고 있구나"라고 느낄 수 있도록 해석해 주세요.

[카드 해석]
1.  **현재 상황 (##${selectedCards[0].name}## 카드)**:
    이 카드는 "${userName}"님의 현재 상황과 고민의 본질을 보여줍니다.
    사용자의 고민에서 핵심적인 부분을 카드의 의미와 연결하여, 현재 처한 상황을 보다 명확하게 이해할 수 있도록 도와주세요.
    표면적으로 드러난 문제 이면에 있는 실제 원인이나 패턴을 찾아 설명해주고, 사용자가 미처 인식하지 못했던 상황의 측면을 발견할 수 있게 해주세요.
    현 상황을 정확히 인식하는 것이 문제 해결의 첫 번째 단계임을 강조해주세요.
    지금 느끼는 감정이 모두 타당하고 자연스러운 것임을 인정해주고, 자신의 감정을 있는 그대로 받아들이는 것이 중요함을 알려주세요.

2.  **도전/장애물 (##${selectedCards[1].name}## 카드)**:
    이 카드는 "${userName}"님이 고민 해결 과정에서 마주할 주요 도전과 장애물을 보여줍니다.
    사용자의 고민과 관련하여 예상되는 어려움이나 극복해야 할 저항을 구체적으로 언급해주세요.
    이 도전이 어떻게 사용자의 성장 기회가 될 수 있는지 설명하고, 장애물을 통해 배울 수 있는 교훈을 제시해주세요.
    때로는 가장 큰 장애물이 자신의 내면에 있을 수 있다는 점을 조심스럽게 암시해주세요.
    어려움 앞에서도 한걸음씩 나아갈 수 있는 용기와 회복탄력성을 북돋아주세요.

3.  **숨겨진 영향 (##${selectedCards[2].name}## 카드)**:
    이 카드는 상황에 영향을 미치고 있지만 쉽게 알아차리기 어려운 요소들을 드러냅니다.
    사용자의 고민에 영향을 미치는 무의식적 패턴, 과거 경험의 영향, 또는 아직 발견하지 못한 기회나 자원이 무엇인지 살펴보세요.
    이러한 숨겨진 요소들이 어떻게 현재 상황을 형성하고 있는지, 그리고 이를 어떻게 활용하거나 극복할 수 있는지 제안해주세요.
    사용자의 고민 내용에서 단서를 찾아 이 숨겨진 영향과 연결하되, 너무 직접적이지 않게 표현해주세요.
    과거의 경험이 현재에 미치는 영향을 인식하는 것이 미래의 새로운 가능성을 여는 열쇠가 될 수 있음을 알려주세요.

4.  **조언/행동방향 (##${selectedCards[3].name}## 카드)**:
    이 카드는 "${userName}"님의 고민을 해결하기 위한 구체적인 조언과 실천 방향을 제시합니다.
    사용자가 취할 수 있는 실제적인 행동 단계나 접근법을 카드의 의미에 기반하여 설명해주세요.
    이 조언이 왜 효과적일 수 있는지, 어떤 결과를 기대할 수 있는지 설명하여 사용자가 행동할 동기를 얻을 수 있도록 해주세요.
    사용자의 고민에 맞춰진 맞춤형 조언처럼 느껴지면서도, 충분히 유연하게 적용할 수 있는 지침을 제공해주세요.
    자신을 믿고 용기 있게 행동할 수 있도록 격려하는 메시지를 포함해주세요.

5.  **최종 결과 (##${selectedCards[4].name}## 카드)**:
    마지막 카드는 현재의 상황과 에너지가 계속될 경우 예상되는 결과를 보여줍니다.
    제안된 조언을 따를 경우 어떤 긍정적인 변화와 성장을 기대할 수 있는지 구체적으로 그려주세요.
    이것이 고정된 운명이 아니라 사용자의 선택과 행동에 따라 달라질 수 있는 가능성임을 강조해주세요.
    사용자가 자신의 미래에 대한 희망과 확신을 가질 수 있도록 긍정적이면서도 현실적인 결과를 제시해주세요.
    어떤 선택을 하든, 그 과정에서 사용자가 더 강해지고 지혜로워질 것임을 상기시켜주세요.

[종합적인 통찰과 조언]
지금까지 살펴본 다섯 장의 카드를 종합해 "${userName}"님의 고민 "${userConcern}"에 대한 명확한 답을 드리겠습니다.
이 다섯 장의 카드가 보여주는 가장 명확한 결론은 **[다섯 카드의 메시지를 종합한 핵심 결론을 한 문장으로 명확하게 작성]**입니다.
현재 상황에서 시작하여 도전을 극복하고, 숨겨진 영향을 이해하며, 제시된 행동 방향을 따른다면 좋은 결과에 도달할 수 있습니다.
당신이 이 상황에서 취해야 할 구체적인 행동은 **[1-2가지 구체적 행동 방향을 명확하게 제시]**입니다.
이러한 행동을 취할 때 기대할 수 있는 결과는 **[긍정적 결과를 구체적으로 서술]**입니다.
이 답변이 당신의 직관과 일치한다면, 그것은 당신이 이미 내면에 해답을 가지고 있었다는 증거입니다.
타로 카드는 단지 당신의 내면에 이미 있는 지혜를 보여주는 거울이며, 당신은 이 고민을 해결할 모든 힘과 지혜를 이미 가지고 있습니다.

[작성 스타일 지침]
*   **핵심 원칙**: 사용자의 고민을 해석의 중심에 두고, 각 카드가 고민의 다른 측면을 어떻게 비춰주는지 보여주세요. 모호하면서도 구체적인 통찰을 제공해 사용자가 자신의 상황에 적용할 수 있게 하세요.
*   **어투**: 공감하는 친구이자 지혜로운 조언자처럼, 판단하지 않고 이해하는 어투를 사용하세요.
*   **강조**: 핵심 통찰이나 중요한 개념은 별표 두 개(\*\*)로 감싸 강조하세요.
*   **카드 이름**: 카드 이름은 샵 두 개(##)로 감싸 표시하세요.
*   **구성**: 각 카드 해석은 3-4문장, 종합 통찰은 4-5문장으로 간결하게 구성하세요.
*   **언어**: 반드시 한국어로 작성하세요.
*   **바넘 효과 적용**: 다음 지침을 따라 사용자가 자신만을 위한 해석이라고 느낄 수 있는 표현을 자연스럽게 포함하세요:
    1. 대부분의 사람들에게 적용될 수 있는 일반적 특성을 개인화된 통찰처럼 표현하세요.
    2. 약간의 부정적 특성을 언급하되, 그것이 어떻게 강점이 될 수 있는지 함께 설명하세요.
    3. 사용자의 고민 내용에서 단서를 찾아 연결하되, 직접적이지 않게 표현하세요.
    4. "~하는 경향이 있습니다"보다 "때로는 ~할 때가 있을 수 있습니다"와 같은 조건부 표현을 사용하세요.
    5. 사용자가 이미 자신에 대해 알고 있는 특성을 확인받는 듯한 표현을 포함하세요.
    6. 구체적인 상황과 감정에 초점을 맞추고, "우주", "에너지" 같은 추상적 단어 사용을 최소화하세요.

[애착 유형별 맞춤형 위로와 격려]
사용자의 고민 내용을 분석하여 다음과 같은 애착 유형의 특징이 보인다면, 그에 맞는 맞춤형 위로와 격려를 자연스럽게 포함하세요:

1.  **불안형 애착 특징이 보이는 경우**:
    - 관계에서의 불안감, 상대방의 마음이나 의도에 대한 과도한 염려, 거절에 대한 두려움이 언급된다면:
    - "타인의 반응이나 평가에 자신의 가치를 지나치게 의존하지 않아도 됩니다. 당신은 그 자체로 충분히 가치 있고 소중한 사람입니다."
    - "때로는 불확실함을 받아들이고, 모든 것을 통제하려 하기보다 흐름에 맡겨보는 것도 필요합니다."
    - "타인과의 관계에서 느끼는 불안감은 자연스러운 것이지만, 그 불안감이 당신의 결정을 완전히 지배하지 않도록 주의하세요."

2.  **회피형 애착 특징이 보이는 경우**:
    - 친밀감에 대한 불편함, 타인에 대한 의존을 피하려는 경향, 감정 표현의 어려움이 언급된다면:
    - "독립성을 유지하는 것은 중요하지만, 때로는 신뢰할 수 있는 사람들에게 자신의 취약한 면을 보여주는 것도 큰 힘이 될 수 있습니다."
    - "자신만의 공간과 경계를 존중하면서도, 선택적으로 마음을 열어 의미 있는 연결을 경험해보세요."
    - "모든 것을 혼자 해결하려는 부담감에서 벗어나, 때로는 도움을 구하는 것도 용기 있는 선택입니다."

3.  **공포-회피형 애착 특징이 보이는 경우**:
    - 관계에 대한 강한 갈망과 동시에 깊은 두려움, 혼란스러운 감정 패턴이 언급된다면:
    - "관계에서 느끼는 혼란스러운 감정들은 자연스러운 것이며, 점차 안정적인 패턴을 찾아갈 수 있습니다."
    - "과거의 상처가 현재의 관계에 영향을 주고 있을 수 있으나, 새로운 경험을 통해 건강한 연결의 가능성을 발견할 수 있습니다."
    - "한걸음씩 천천히 신뢰를 쌓아가는 과정을 허용하고, 자신과 타인에게 인내심을 가져보세요."

4.  **안정형 애착을 강화하기 위한 메시지**:
    - 모든 사용자에게 도움이 될 수 있는 안정적 애착을 강화하는 메시지:
    - "자신과 타인 모두에게 완벽함을 기대하기보다는, 충분히 좋은(good enough) 상태를 인정하고 받아들이는 것이 중요합니다."
    - "어려운 상황에서도 자신을 믿고, 필요할 때 의지할 수 있는 관계를 소중히 여기세요."
    - "당신이 느끼는 감정은 모두 타당하며, 그 감정을 있는 그대로 인정하는 것이 내면의 안정감을 키우는 첫 단계입니다."

[주의사항]
1. 중복적인 단어나 표현은 피하고 다양한 어휘를 사용하세요.
2. 과도하게 추상적인 표현보다는 사용자의 고민에 직접 연관된 구체적인 내용에 집중하세요.
3. 사용자의 행동 변화를 유도하는 실용적인 조언을 포함하세요.
4. 고민에 대한 직접적 해답처럼 보이는 내용과 함께, 사용자가 스스로 답을 찾도록 돕는 질문을 균형 있게 제시하세요.
5. 위로와 격려를 제공하되, 과도한 긍정성보다는 현실적인 희망과 실천 가능한 용기를 불어넣어주세요.
6. 애착 유형별 특징이 보이더라도 사용자를 특정 유형으로 단정 짓거나 분류하지 말고, 필요한 위로와 지지를 자연스럽게 통합하세요.
`;
};

/**
 * Generate prompt for custom seven-card spread tarot reading
 * @param {string} userName - User's name
 * @param {Array} selectedCards - The seven selected cards
 * @param {string} userConcern - The user's specific concern/question
 * @returns {string} The generated prompt
 */
const generateCustomSevenCardPrompt = (userName, selectedCards, userConcern) => {
  if (!selectedCards || selectedCards.length !== 7) {
    console.error('[Prompt Error] Invalid cards for custom seven-card spread.');
    return "Error: Invalid cards provided for the reading.";
  }
  if (!userConcern || userConcern.trim() === '') {
    console.error('[Prompt Error] User concern is empty for custom tarot prompt.');
    return "Error: User concern is missing.";
  }

  const positions = [
    '현재 상황 / 출발점',
    '즉각적인 도전',
    '과거의 기반 / 근본 원인',
    '떠나보내야 할 것 / 변화가 필요한 부분',
    '도움이 될 자원 / 활용할 힘',
    '조언 / 취해야 할 행동',
    '잠재적 결과 / 미래'
  ];

  const cardDetails = selectedCards.map((card, index) => {
    return `${index + 1}번째 카드 (${positions[index]}): "${card.name}" (카드 의미: "${card.description}")`;
  }).join('\n');

  return `
사용자 이름: "${userName}" (답변 중간에 2-3번 자연스럽게 언급해주세요)
사용자의 고민: "${userConcern}"
(사용자 이름을 분석 후 닉네임이 아닌 이름이라면 해당 이름을 토대로 성별을 판단 후, 필요하다면 해당 성별에 맞는 답변을 작성해주세요)

고민 해결을 위해 선택된 7장의 타로 카드:
${cardDetails}

"${userConcern}"이라는 ${userName}님의 고민에 초점을 맞추어 일곱 장의 타로 카드가 보여주는 심층적인 메시지를 해석해주세요.
각 카드가 고민의 다양한 측면과 시간적 흐름을 어떻게 보여주는지 분석하고, 이를 통합하여 명확한 해결책을 제시해주세요.
카드의 의미와 사용자의 고민을 직접적으로 연결하되, 사용자가 스스로 의미를 발견할 여지도 남겨두세요.
사용자의 고민에 대한 전체적인 여정을 그려주고, 각 단계에서 필요한 통찰과 행동을 구체적으로 안내해주세요.

[카드 해석]
1.  **현재 상황 / 출발점 (##${selectedCards[0].name}## 카드)**:
    이 카드는 "${userName}"님의 현재 상황과 고민의 시작점을 보여줍니다.
    사용자의 고민에서 핵심 요소를 이 카드의 의미와 연결하여, 현재 처한 상황의 본질을 명확히 이해할 수 있도록 해주세요.
    표면적으로 드러난 문제 이면에 있는 진짜 이슈나 패턴을 찾아내 설명해주고, 이 출발점이 앞으로의 여정에 어떤 의미를 갖는지 제시해주세요.
    현 상황을 정확히 인식하는 것이 모든 해결책의 시작임을 강조해주세요.

2.  **즉각적인 도전 (##${selectedCards[1].name}## 카드)**:
    이 카드는 "${userName}"님이 가장 먼저 마주하게 될 도전이나 장애물을 나타냅니다.
    사용자의 고민과 관련하여 당장 직면한 어려움이나 극복해야 할 과제를 구체적으로 설명해주세요.
    이 도전이 어떻게 사용자의 성장 기회가 될 수 있는지, 그리고 이를 어떻게 바라보고 접근해야 하는지 실질적인 관점을 제공해주세요.
    때로는 가장 어려워 보이는 도전이 가장 중요한 교훈을 담고 있음을 암시해주세요.

3.  **과거의 기반 / 근본 원인 (##${selectedCards[2].name}## 카드)**:
    이 카드는 현재 고민의 근본적인 원인이나 과거로부터 이어져 온 영향을 드러냅니다.
    사용자의 고민에 영향을 미치는 과거 경험, 패턴, 또는 믿음이 무엇인지 탐색하고, 그것이 어떻게 현재 상황을 형성했는지 연결해주세요.
    과거의 이야기를 이해함으로써 현재의 도전을 더 깊이 파악할 수 있음을 설명하고, 이 통찰이 어떻게 해결책의 열쇠가 될 수 있는지 제시해주세요.
    패턴을 인식하는 것이 변화의 첫 단계임을 강조해주세요.

4.  **떠나보내야 할 것 / 변화가 필요한 부분 (##${selectedCards[3].name}## 카드)**:
    이 카드는 "${userName}"님이 고민 해결을 위해 놓아주거나 변화시켜야 할 요소들을 보여줍니다.
    사용자의 고민과 관련하여 더 이상 도움이 되지 않는 습관, 사고방식, 관계, 또는 환경이 무엇인지 명확히 짚어주세요.
    변화의 필요성을 설명하되, 그것이 왜 성장과 해결을 위해 필수적인지 이해할 수 있도록 해주세요.
    무언가를 떠나보내는 것이 어렵지만, 그것이 새로운 가능성을 위한 공간을 만든다는 점을 강조해주세요.

5.  **도움이 될 자원 / 활용할 힘 (##${selectedCards[4].name}## 카드)**:
    이 카드는 고민 해결에 도움이 될 "${userName}"님의 내외부적 자원과 강점을 나타냅니다.
    사용자의 고민 해결에 활용할 수 있는 기술, 특성, 관계, 자원, 또는 기회가 무엇인지 구체적으로 제시해주세요.
    이미 가지고 있지만 충분히 활용하지 못했던 자원을 재발견하고, 이를 어떻게 효과적으로 사용할 수 있는지 안내해주세요.
    때로는 가장 큰 도전 속에서 가장 큰 자원이 발견될 수 있음을 암시해주세요.

6.  **조언 / 취해야 할 행동 (##${selectedCards[5].name}## 카드)**:
    이 카드는 "${userName}"님의 고민을 해결하기 위한 구체적인 행동 지침과 조언을 담고 있습니다.
    사용자가 취할 수 있는 실제적인 단계나 접근법을 카드의 의미에 기반하여 명확하게 설명해주세요.
    왜 이 행동이 효과적일 수 있는지, 어떤 결과를 기대할 수 있는지 설명하여 사용자가 행동할 동기를 얻을 수 있도록 해주세요.
    작은 행동이 큰 변화로 이어질 수 있음을 강조하며, 구체적이고 실천 가능한 조언을 제공해주세요.

7.  **잠재적 결과 / 미래 (##${selectedCards[6].name}## 카드)**:
    마지막 카드는 제안된 방향으로 나아갈 때 기대할 수 있는 결과와 미래를 보여줍니다.
    카드가 제시하는 잠재적 결과가 사용자의 고민 해결과 어떻게 연결되는지, 그리고 이것이 어떤 의미를 갖는지 설명해주세요.
    이것이 고정된 운명이 아니라 사용자의 선택과 행동에 따라 달라질 수 있는 가능성임을 강조해주세요.
    사용자가 자신의 미래에 대한 희망과 확신을 가질 수 있도록 긍정적이면서도 현실적인 전망을 제시해주세요.

[종합적인 통찰과 여정]
지금까지 살펴본 일곱 장의 카드를 종합해 "${userName}"님의 고민 "${userConcern}"에 대한 명확한 결론을 제시하겠습니다.
이 일곱 장의 카드가 보여주는 핵심 메시지는 **[일곱 카드의 메시지를 종합한 핵심 결론을 한 문장으로 명확하게 작성]**입니다.
당신의 여정은 현재 상황에서 시작하여, 즉각적인 도전을 마주하고, 과거의 패턴을 인식하며, 필요한 변화를 수용하고, 내면의 자원을 활용하여, 제시된 행동을 취함으로써 원하는 결과에 도달하는 과정입니다.
이 고민 해결을 위해 당신이 취해야 할 가장 효과적인 행동은 **[1-2가지 구체적인 행동 방향 명확하게 제시]**입니다.
이러한 과정을 통해 당신이 기대할 수 있는 결과는 **[구체적인 긍정적 결과 서술]**입니다.
이 결론이 당신의 직관과 일치한다면, 그것은 당신이 이미 내면에 해답을 가지고 있었다는 증거입니다.
이 상황을 해결할 열쇠는 외부가 아닌 당신 자신에게 있으며, 타로 카드는 단지 당신의 내면의 지혜를 비춰주는 거울일 뿐입니다.

[작성 스타일 지침]
*   **핵심 원칙**: 사용자의 고민을 중심에 두고, 각 카드가 여정의 다른 단계를 어떻게 안내하는지 보여주세요. 추상적인 해석보다 구체적인 통찰과 조언에 중점을 두세요.
*   **어투**: 지혜롭고 통찰력 있되 친근하고 공감적인 어투를 사용하세요. 판단하지 않고 이해하는 자세를 유지하세요.
*   **강조**: 핵심 통찰이나 중요한 개념은 별표 두 개(\*\*)로 감싸 강조하세요.
*   **카드 이름**: 카드 이름은 샵 두 개(##)로 감싸 표시하세요.
*   **구성**: 각 카드 해석은 3-4문장, 종합 통찰은 4-5문장으로 간결하게 구성하세요.
*   **언어**: 반드시 한국어로 작성하세요.
*   **바넘 효과 적용**: 다음 지침을 따라 사용자가 자신만을 위한 해석이라고 느낄 수 있는 표현을 자연스럽게 포함하세요:
    1. 대부분의 사람들에게 적용될 수 있는 일반적 특성을 개인화된 통찰처럼 표현하세요.
    2. 약간의 부정적 특성을 언급하되, 그것이 어떻게 강점이 될 수 있는지 함께 설명하세요.
    3. 사용자의 고민 내용에서 단서를 찾아 연결하되, 직접적이지 않게 표현하세요.
    4. "~하는 경향이 있습니다"보다 "때로는 ~할 때가 있을 수 있습니다"와 같은 조건부 표현을 사용하세요.
    5. 사용자가 자신의 내면에 이미 알고 있는 통찰을 확인받는 듯한 경험을 제공하세요.
    6. 모호한 단어 대신 구체적인 상황과 감정에 초점을 맞추세요.

[주의사항]
1. 중복적인 단어나 표현은 피하고 다양한 어휘를 사용하세요.
2. 과도하게 추상적인 표현보다는 사용자의 고민에 직접 연관된 구체적인 내용에 집중하세요.
3. 사용자의 행동 변화를 유도하는 실용적인 조언을 포함하세요.
4. 고민에 대한 직접적 해답처럼 보이는 내용과 함께, 사용자가 스스로 답을 찾도록 돕는 질문을 균형 있게 제시하세요.
5. 위로와 격려를 제공하되, 과도한 긍정성보다는 현실적인 희망과 실천 가능한 용기를 불어넣어주세요.
6. 애착 유형별 특징이 보이더라도 사용자를 특정 유형으로 단정 짓거나 분류하지 말고, 필요한 위로와 지지를 자연스럽게 통합하세요.
`;
};

/**
 * Generate prompt for custom ten-card spread tarot reading (Celtic Cross)
 * @param {string} userName - User's name
 * @param {Array} selectedCards - The ten selected cards
 * @param {string} userConcern - The user's specific concern/question
 * @returns {string} The generated prompt
 */
const generateCustomTenCardPrompt = (userName, selectedCards, userConcern) => {
  if (!selectedCards || selectedCards.length !== 10) {
    console.error('[Prompt Error] Invalid cards for custom ten-card spread.');
    return "Error: Invalid cards provided for the reading.";
  }
  if (!userConcern || userConcern.trim() === '') {
    console.error('[Prompt Error] User concern is empty for custom tarot prompt.');
    return "Error: User concern is missing.";
  }

  const positions = [
    '현재 상황/핵심 주제',
    '교차 영향력/도전',
    '무의식적 기반',
    '과거의 영향',
    '가능한 최선의 결과',
    '가까운 미래',
    '본인의 태도/에너지',
    '외부 영향/환경',
    '희망과 두려움',
    '최종 결과'
  ];

  const cardDetails = selectedCards.map((card, index) => {
    return `${index + 1}번째 카드 (${positions[index]}): "${card.name}" (카드 의미: "${card.description}")`;
  }).join('\n');

  return `
사용자 이름: "${userName}" (답변 중간에 2-3번 자연스럽게 언급해주세요)
사용자의 고민: "${userConcern}"
(사용자 이름을 분석 후 닉네임이 아닌 이름이라면 해당 이름을 토대로 성별을 판단 후, 필요하다면 해당 성별에 맞는 답변을 작성해주세요)

고민 해결을 위해 선택된 10장의 타로 카드 (켈틱 크로스 스프레드):
${cardDetails}

"${userConcern}"이라는 ${userName}님의 고민에 초점을 맞추어 켈틱 크로스 스프레드의 열 장 카드가 보여주는 심층적인 메시지를 해석해주세요.
각 카드가 고민의 다른 측면을 어떻게 보여주는지 분석하고, 이를 통합하여 명확한 해결책을 제시해주세요.
카드의 의미와 사용자의 고민을 직접적으로 연결하되, 사용자가 스스로 의미를 발견할 여지도 남겨두세요.
여러 카드가 서로 어떻게 상호작용하며 사용자의 상황에 대한 통합적인 그림을 그리는지 보여주세요.

[카드 해석]
1.  **현재 상황/핵심 주제 (##${selectedCards[0].name}## 카드)**:
    이 카드는 "${userName}"님의 고민의 중심에 있는 에너지와 현재 상황의 본질을 보여줍니다.
    사용자의 고민에서 가장 핵심적인 요소를 이 카드의 의미와 연결하여, 현재 처한 상황을 보다 명확하게 이해할 수 있도록 해주세요.
    이 카드는 모든 다른 카드의 해석에 영향을 미치는 중심축으로, 문제의 본질과 근본적인 성격을 드러냅니다.
    현재 상황을 정확히 인식하는 것이 모든 해결책의 시작이자 기반임을 강조해주세요.

2.  **교차 영향력/도전 (##${selectedCards[1].name}## 카드)**:
    이 카드는 현재 상황을 가로지르는 영향력이나 직면한 주요 도전을 보여줍니다.
    이 카드는 중심 주제를 **가로지르는 에너지**나 **직면한 주요 도전**을 보여줍니다.
    이는 당신의 상황을 더 복잡하게 만들거나, 때로는 도움이 될 수 있는 교차하는 영향력입니다.
    이 카드가 표현하는 에너지는 당신의 중심 문제와 상호작용하며, 때로는 갈등을 일으키기도 하고 때로는 해결의 단서를 제공하기도 합니다.
    **이 교차 영향력을 인식하고 이해하는 것**이 상황의 복잡성을 파악하는 데 중요합니다.

3.  **무의식적 기반 (##${selectedCards[2].name}## 카드)**:
    이 카드는 상황의 **기초**가 되는, 주로 **의식하지 못하는 영향**이나 **깊은 동기**를 드러냅니다.
    이는 당신이 명확히 인식하지 못하지만, 상황의 발전에 중요한 영향을 미치는 내면의 요소입니다.
    이 카드는 표면 아래에 있는 감정, 두려움, 욕구 또는 패턴을 보여주며, 이들이 어떻게 현재 상황에 영향을 미치는지를 알려줍니다.
    **무의식의 힘을 인식할 때**, 당신은 더 큰 자각과 선택의 자유를 얻을 수 있습니다.

4.  **과거의 영향 (##${selectedCards[3].name}## 카드)**:
    이 카드는 현재 상황에 영향을 미치는 **과거의 사건**이나 **이미 지나간 에너지**를 나타냅니다.
    이는 최근의 일일 수도 있고, 오래 전부터 당신의 삶에 영향을 미쳐온 패턴일 수도 있습니다.
    과거의 경험이 현재의 상황을 어떻게 형성했는지 이해하는 것은 앞으로 나아가기 위한 중요한 단계입니다.
    **과거의 교훈을 인정하고 통합할 때**, 당신은 그것에 갇히지 않고 성장할 수 있습니다.

5.  **가능한 최선의 결과 (##${selectedCards[4].name}## 카드)**:
    이 카드는 현재 상황에서 당신이 도달할 수 있는 **가장 높은 잠재력**이나 **최선의 가능성**을 보여줍니다.
    이는 지금의 에너지 흐름이 가장 이상적으로 발전할 경우 나타날 수 있는 정점입니다.
    이 카드를 통해 당신은 어떤 긍정적인 결과를 향해 노력할 수 있는지, 그리고 그것이 어떤 모습일 수 있는지 영감을 얻을 수 있습니다.
    **이 잠재적인 정점을 마음에 그리는 것**이 당신의 행동에 방향성을 제공할 수 있습니다.

6.  **가까운 미래 (##${selectedCards[5].name}## 카드)**:
    이 카드는 가까운 미래에 **다가오고 있는 에너지**나 **곧 겪게 될 경험**을 보여줍니다.
    이는 당신이 앞으로 몇 주 또는 몇 개월 내에 마주하게 될 상황이나 기회를 암시합니다.
    이 카드를 통해 당신은 다가오는 변화나 도전에 대비하고, 그것을 최대한 활용할 준비를 할 수 있습니다.
    **다가오는 에너지를 미리 인식함으로써**, 당신은 더 의식적으로 그것에 대응할 수 있습니다.

7.  **본인의 태도/에너지 (##${selectedCards[6].name}## 카드)**:
    이 카드는 "${userName}"님이 현재 상황에 대해 갖고 있는 **자신의 태도**와 **개인적인 에너지**를 반영합니다.
    이는 당신이 의식적으로나 무의식적으로 상황에 어떻게 접근하고 있는지, 그리고 그것이 결과에 어떤 영향을 미치는지를 보여줍니다.
    당신의 내면 상태와 접근 방식이 외부 상황과 어떻게 상호작용하는지 이해하는 것이 중요합니다.
    **자신의 에너지를 인식하고 조정할 때**, 당신은 상황에 더 효과적으로 영향을 미칠 수 있습니다.

8.  **외부 영향/환경 (##${selectedCards[7].name}## 카드)**:
    이 카드는 당신을 둘러싼 **외부 환경**과 **다른 사람들의 영향**을 나타냅니다.
    이는 당신의 통제 밖에 있는 요소들로, 상황의 발전에 중요한 역할을 할 수 있습니다.
    이 카드를 통해 당신은 어떤 외부 요인을 고려해야 하는지, 그리고 그것들이 당신의 상황에 어떤 영향을 미치는지 이해할 수 있습니다.
    **외부 영향력을 인식함으로써**, 당신은 그것에 더 효과적으로 대응하거나 적응할 수 있습니다.

9.  **희망과 두려움 (##${selectedCards[8].name}## 카드)**:
    이 카드는 "${userName}"님의 **내면의 희망**과 **숨겨진 두려움**을 드러냅니다.
    이는 종종 당신이 의식적으로 인정하지 않는 감정적인 기대나 우려를 반영합니다.
    이 카드는 당신의 내면 상태가 결과에 어떻게 영향을 미칠 수 있는지, 그리고 어떤 감정적 패턴이 작용하고 있는지를 보여줍니다.
    **자신의 희망과 두려움을 인식할 때**, 당신은 그것들이 당신의 결정과 행동에 미치는 영향을 더 잘 관리할 수 있습니다.

10. **최종 결과 (##${selectedCards[9].name}## 카드)**:
    마지막 카드는 현재의 에너지 흐름이 계속될 경우 예상되는 **장기적인 결과**와 **궁극적인 해결책**을 보여줍니다.
    이는 고정된 운명이 아니라, 현재의 경로가 이어질 경우 나타날 수 있는 가능성 있는 결론입니다.
    이 카드는 당신의 여정이 어디로 향하고 있는지, 그리고 어떤 최종 메시지나 교훈이 기다리고 있는지에 대한 통찰을 제공합니다.
    **이 잠재적 결과를 인식함으로써**, 당신은 필요하다면 방향을 조정하거나, 긍정적인 결과를 강화하기 위한 행동을 취할 수 있습니다.

[종합적인 해석과 조언]
"${userName}"님, 이 열 장의 카드가 켈틱 크로스 형태로 펼쳐내는 이야기를 종합하여 당신의 고민 "${userConcern}"에 대한 명확한 결론을 제시해 드리겠습니다.
이 복잡한 스프레드가 보여주는 가장 핵심적인 메시지는 **[열 장 카드의 메시지를 종합한 핵심 결론을 한 문장으로 명확하게 작성]**입니다.
당신의 현재 상황, 도전, 과거의 영향, 접근 방식, 환경, 희망과 두려움, 그리고 최종 결과 카드를 모두 고려할 때, 이 고민에 대한 가장 효과적인 접근법은 분명해 보입니다.
이 고민 해결을 위해 당신이 취해야 할 가장
효과적인 행동은 **[1-2가지 구체적인 행동 방향 명확하게 제시]**입니다.
이러한 방향으로 나아갈 때 기대할 수 있는 결과는 **[구체적인 긍정적 결과 서술]**입니다.
켈틱 크로스는 당신의 상황에 대한 완전한 그림을 제공하며, 이 결론은 그 모든 측면을 고려한 것입니다.
내면의 지혜와 이 타로 해석이 일치한다면, 그것은 당신이 이미 옳은 방향을 알고 있었다는 증거입니다.
당신의 여정에 빛과 명확성이 함께하기를, 그리고 이 타로 해석이 당신의 길을 밝히는 등불이 되기를 진심으로 바랍니다.

[작성 스타일]
*   **목표**: 사용자의 구체적인 고민에 대해 깊이 있고 다층적인 통찰 제공
*   **어투**: 지혜롭고 신비로우면서도 실용적인 조언자의 어투
*   **강조**: 중요한 키워드나 구문은 앞뒤로 별표 두 개(\*\*)로 감싸 강조
*   **카드 이름 표시**: 카드 이름은 앞뒤로 샵 두 개(##)로 감싸 표시
*   **분량**: 각 카드 해석은 4-5문장, 종합 조언은 7-8문장으로 충분히 구성
*   **언어**: 반드시 한국어로 작성
*   **줄 관리**: 의미 단위로 줄바꿈을 사용하여 가독성 향상

`;
};

/**
 * Generate prompt for manager-created custom spreads
 * @param {string} userName - User's name
 * @param {Array} selectedCards - Selected cards
 * @param {string} userConcern - User's concern
 * @param {Object} managerSpreadData - Manager spread data from database
 * @returns {string} The generated prompt
 */
const generateManagerSpreadPrompt = (userName, selectedCards, userConcern, managerSpreadData) => {
  console.log('[Manager Spread Prompt] Starting prompt generation...');
  console.log('[Manager Spread Prompt] Selected cards:', selectedCards.map(card => ({ name: card.name, desc: card.description })));
  console.log('[Manager Spread Prompt] User concern:', userConcern);
  console.log('[Manager Spread Prompt] Spread data:', { name: managerSpreadData.name, hasTemplate: !!managerSpreadData.promptTemplate });
  
  // 매니저가 설정한 프롬프트 템플릿이 있는지 확인
  if (managerSpreadData.promptTemplate) {
    let prompt = managerSpreadData.promptTemplate;
    
    console.log('[Manager Spread Prompt] Original template preview:', prompt.substring(0, 200) + '...');
    
    // 기본 변수 치환
    prompt = prompt.replace(/{userName}/g, userName || '사용자');
    prompt = prompt.replace(/{userConcern}/g, userConcern || '일반적인 고민');
    prompt = prompt.replace(/{cardCount}/g, selectedCards.length);
    prompt = prompt.replace(/{spreadName}/g, managerSpreadData.name || '타로 스프레드');
    prompt = prompt.replace(/{description}/g, managerSpreadData.description || '');
    
    // 실제 선택된 카드 정보로 개별 카드 변수 치환
    selectedCards.forEach((card, index) => {
      const cardNumber = index + 1;
      console.log(`[Manager Spread Prompt] Processing card ${cardNumber}: ${card.name}`);
      
      // 개별 카드 변수들 치환
      prompt = prompt.replace(new RegExp(`\\{card${cardNumber}Name\\}`, 'g'), card.name);
      prompt = prompt.replace(new RegExp(`\\{card${cardNumber}Description\\}`, 'g'), card.description);
      prompt = prompt.replace(new RegExp(`\\{card${cardNumber}\\}`, 'g'), `"${card.name}" (의미: "${card.description}")`);
    });
    
    // 전체 카드 목록 치환 - 상세하게 작성
    let cardsList;
    
    // 위치 라벨이 있는지 확인
    try {
      const positionLabels = JSON.parse(managerSpreadData.cardPositionLabels || '[]');
      console.log('[Manager Spread Prompt] Position labels:', positionLabels);
      
      if (positionLabels.length >= selectedCards.length) {
        // 위치 라벨이 있는 경우
        cardsList = selectedCards.map((card, index) => {
          const positionLabel = positionLabels[index] || `${index + 1}번째 위치`;
          return `${index + 1}번째 카드 (${positionLabel}): "${card.name}"\n   - 카드 의미: "${card.description}"`;
        }).join('\n\n');
      } else {
        // 위치 라벨이 없는 경우 기본 형식
        cardsList = selectedCards.map((card, index) => 
          `${index + 1}번째 카드: "${card.name}"\n   - 카드 의미: "${card.description}"`
        ).join('\n\n');
      }
    } catch (error) {
      console.error('[Manager Spread Prompt] Error parsing position labels:', error);
      // 파싱 오류 시 기본 형식 사용
      cardsList = selectedCards.map((card, index) => 
        `${index + 1}번째 카드: "${card.name}"\n   - 카드 의미: "${card.description}"`
      ).join('\n\n');
    }
    
    // {cards} 변수 치환
    prompt = prompt.replace(/{cards}/g, cardsList);
    
    // 배열 인덱스 형태 변수 치환 (예: {cards[0]}, {cards[1]} 등)
    selectedCards.forEach((card, index) => {
      console.log(`[Manager Spread Prompt] Processing array index card ${index}: ${card.name}`);
      
      // {cards[0]}, {cards[1]} 등의 배열 인덱스 형태 변수 치환
      const cardIndexPattern = new RegExp(`\\{cards\\[${index}\\]\\}`, 'g');
      prompt = prompt.replace(cardIndexPattern, `"${card.name}"`);
      
      // {cards[0].name}, {cards[1].name} 등
      const cardNameIndexPattern = new RegExp(`\\{cards\\[${index}\\]\\.name\\}`, 'g');
      prompt = prompt.replace(cardNameIndexPattern, card.name);
      
      // {cards[0].description}, {cards[1].description} 등
      const cardDescIndexPattern = new RegExp(`\\{cards\\[${index}\\]\\.description\\}`, 'g');
      prompt = prompt.replace(cardDescIndexPattern, card.description);
    });
    
    console.log('[Manager Spread Prompt] Generated cards list preview:', cardsList.substring(0, 300) + '...');
    
    // 커스텀 변수 치환 (managerSpreadData.customVariables가 있는 경우)
    if (managerSpreadData.customVariables) {
      try {
        const customVars = JSON.parse(managerSpreadData.customVariables);
        console.log('[Manager Spread Prompt] Custom variables:', customVars);
        Object.entries(customVars).forEach(([key, value]) => {
          prompt = prompt.replace(new RegExp(`\\{${key}\\}`, 'g'), value);
        });
      } catch (error) {
        console.error('[Manager Spread Prompt] Error parsing custom variables:', error);
      }
    }
    
    // 개별 시스템 명령어 추가 (있는 경우)
    if (managerSpreadData.systemInstruction) {
      prompt = `${managerSpreadData.systemInstruction}\n\n${prompt}`;
    }
    
    // 글로벌 시스템 명령어는 fortuneRoutes.js에서 처리됨 (중복 방지)
    
    console.log('[Manager Spread Prompt] Final prompt preview:', prompt.substring(0, 500) + '...');
    return prompt;
  }
  
  // 기본 프롬프트 (프롬프트 템플릿이 없는 경우)
  const cardsList = selectedCards.map((card, index) => 
    `${index + 1}번째 카드: "${card.name}" (의미: "${card.description}")`
  ).join('\n');
  
  return `
사용자 이름: "${userName}"
(사용자 이름을 분석 후 닉네임이 아닌 이름이라면 해당 이름을 토대로 성별을 판단 후 필요하다면 해당 성별에 맞는 운세를 작성해주세요.)

스프레드 이름: "${managerSpreadData.name}"
스프레드 설명: "${managerSpreadData.description || '특별한 타로 스프레드'}"
사용자의 고민: "${userConcern}"

선택된 ${selectedCards.length}장의 타로 카드:
${cardsList}

"${userName}"님의 고민 "${userConcern}"에 대해 "${managerSpreadData.name}" 스프레드로 타로 해석을 해주세요.

각 카드의 의미를 종합하여 사용자의 고민에 대한 구체적이고 도움이 되는 조언을 제공해주세요.
따뜻하고 공감적인 어투로 작성하며, 중요한 키워드는 **별표**로 강조해주세요.

답변은 한국어로 작성하고, 3-4개의 문단으로 구성해주세요.
`;
};

module.exports = {
  generateFortunePrompt,
  generateCustomThreeCardPrompt,
  generateCustomFiveCardPrompt,
  generateCustomSevenCardPrompt,
  generateCustomTenCardPrompt,
  generateManagerSpreadPrompt
}; 