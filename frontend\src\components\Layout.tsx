import React, { useState, useEffect } from 'react';
import Header from './Header'; // 나중에 Header 컴포넌트 임포트
import Footer from './Footer'; // 나중에 Footer 컴포넌트 임포트
import { useAuth } from '../contexts/AuthContext'; // useAuth 훅 임포트

interface LayoutProps {
  children: React.ReactNode; // Layout 컴포넌트가 감싸는 자식 요소들을 위한 타입
}

// 헤더 상태를 감지하고 메인 콘텐츠 조정을 위한 커스텀 훅
const useHeaderState = () => {
  const [isHeaderCollapsed, setIsHeaderCollapsed] = useState(false);

  useEffect(() => {
    // MutationObserver를 사용하여 헤더의 클래스 변화를 감지
    const headerElement = document.querySelector('header.main-header');
    if (!headerElement) return;

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          const target = mutation.target as HTMLElement;
          setIsHeaderCollapsed(target.classList.contains('collapsed'));
        }
      });
    });

    observer.observe(headerElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    // 초기 상태 확인
    setIsHeaderCollapsed(headerElement.classList.contains('collapsed'));

    return () => observer.disconnect();
  }, []);

  return isHeaderCollapsed;
};

const BACKEND_URL = 'http://125.189.172.142:3000'; // 백엔드 서버 주소

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const [isMyInfoModalOpen, setIsMyInfoModalOpen] = useState(false);
  const isHeaderCollapsed = useHeaderState();
  const [loginEmail, setLoginEmail] = useState('');
  const [loginPassword, setLoginPassword] = useState('');
  const [registerName, setRegisterName] = useState('');
  const [registerEmail, setRegisterEmail] = useState('');
  const [registerPassword, setRegisterPassword] = useState('');
  const [registerPasswordConfirm, setRegisterPasswordConfirm] = useState('');
  const [myInfoName, setMyInfoName] = useState('');

  // 비밀번호 변경을 위한 상태 추가
  const [myInfoCurrentPassword, setMyInfoCurrentPassword] = useState('');
  const [myInfoNewPassword, setMyInfoNewPassword] = useState('');
  const [myInfoNewPasswordConfirm, setMyInfoNewPasswordConfirm] = useState('');

  const auth = useAuth();

  useEffect(() => {
    if (auth.user && isMyInfoModalOpen) {
      setMyInfoName(auth.user.name);
      // 모달이 열릴 때 비밀번호 필드는 항상 비워둠
      setMyInfoCurrentPassword('');
      setMyInfoNewPassword('');
      setMyInfoNewPasswordConfirm('');
    }
  }, [auth.user, isMyInfoModalOpen]);

  const openLoginModal = () => {
    setIsLoginModalOpen(true);
    setIsRegisterModalOpen(false);
    setIsMyInfoModalOpen(false);
  };
  const closeLoginModal = () => setIsLoginModalOpen(false);

  const openRegisterModal = () => {
    setIsRegisterModalOpen(true);
    setIsLoginModalOpen(false);
    setIsMyInfoModalOpen(false);
  };
  const closeRegisterModal = () => setIsRegisterModalOpen(false);

  const openMyInfoModal = () => {
    if (auth.user) {
      setMyInfoName(auth.user.name);
      setMyInfoCurrentPassword('');
      setMyInfoNewPassword('');
      setMyInfoNewPasswordConfirm('');
      setIsMyInfoModalOpen(true);
      setIsLoginModalOpen(false);
      setIsRegisterModalOpen(false);
    }
  };
  const closeMyInfoModal = () => setIsMyInfoModalOpen(false);

  const handleLoginSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch(`${BACKEND_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: loginEmail, password: loginPassword }),
      });
      const data = await response.json();
      if (response.ok && data.token && data.user) {
        auth.login(data.token, data.user);
        alert('로그인 성공!');
        setLoginEmail('');
        setLoginPassword('');
        closeLoginModal();
      } else {
        alert(data.message || '로그인 실패 또는 응답 데이터 오류');
      }
    } catch (error) {
      alert('로그인 중 오류 발생');
    }
  };

  const handleRegisterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (registerPassword !== registerPasswordConfirm) {
      alert('비밀번호가 일치하지 않습니다.');
      return;
    }
    try {
      const response = await fetch(`${BACKEND_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: registerName,
          email: registerEmail,
          password: registerPassword,
        }),
      });
      const data = await response.json();
      if (response.ok) {
        alert(data.message || '회원가입에 성공했습니다! 이제 로그인해주세요.');
        closeRegisterModal();
        openLoginModal(); // Automatically open login modal after successful registration
      } else {
        alert(data.message || '회원가입에 실패했습니다. 다시 시도해주세요.');
      }
    } catch (error) {
      alert('회원가입 중 오류가 발생했습니다. 네트워크 연결을 확인해주세요.');
    }
  };

  const handleMyInfoSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const trimmedMyInfoName = myInfoName.trim();
    let nicknameChanged = false;
    let passwordChangedAttempted = false;

    // 사용자가 닉네임 변경을 시도했는지 확인 (기존 닉네임과 다르고, 입력값이 있을 때)
    const isNicknameChangeAttempted = auth.user && trimmedMyInfoName && trimmedMyInfoName !== auth.user.name;
    // 사용자가 비밀번호 변경을 시도했는지 확인 (관련 필드가 하나라도 채워져 있을 때)
    const isPasswordChangeAttempted = !!(myInfoCurrentPassword || myInfoNewPassword || myInfoNewPasswordConfirm);

    if (!auth.user) {
      alert('로그인이 필요합니다.');
      closeMyInfoModal();
      openLoginModal();
      return;
    }

    // 닉네임 변경도 없고, 비밀번호 변경 시도도 없을 경우
    if (!isNicknameChangeAttempted && !isPasswordChangeAttempted) {
      alert('변경된 내용이 없습니다.');
      closeMyInfoModal();
      return;
    }

    const token = auth.token;
    if (!token) {
      alert('로그인이 필요합니다. 다시 로그인해주세요.');
      closeMyInfoModal();
      openLoginModal();
      return;
    }

    // 1. 닉네임 변경 처리
    if (isNicknameChangeAttempted) {
      if (!trimmedMyInfoName) { // 이 조건은 isNicknameChangeAttempted에서 이미 걸러지지만, 명확성을 위해 추가
        alert('닉네임을 입력해주세요.');
        return;
      }
      try {
        const response = await fetch(`${BACKEND_URL}/auth/me/nickname`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ name: trimmedMyInfoName }),
        });

        const data = await response.json();

        if (response.ok) {
          auth.updateUserName(trimmedMyInfoName);
          alert('닉네임이 성공적으로 변경되었습니다.');
          nicknameChanged = true;
        } else {
          alert(data.message || '닉네임 변경에 실패했습니다. 다시 시도해주세요.');
          // 닉네임 변경 실패 시, 비밀번호 변경도 진행하지 않으려면 여기서 return 할 수 있습니다.
          // 혹은 비밀번호 변경은 계속 진행하도록 둘 수도 있습니다. 현재는 계속 진행합니다.
        }
      } catch (error) {
        console.error('Error updating nickname:', error);
        alert('닉네임 변경 중 오류가 발생했습니다. 네트워크 또는 서버 연결을 확인해주세요.');
      }
    }

    // 2. 비밀번호 변경 처리
    if (isPasswordChangeAttempted) {
      passwordChangedAttempted = true;
      if (!myInfoCurrentPassword || !myInfoNewPassword || !myInfoNewPasswordConfirm) {
        alert('비밀번호 변경을 원하시면 현재 비밀번호, 새 비밀번호, 새 비밀번호 확인을 모두 입력해주세요.');
        // 닉네임만 성공적으로 변경되었고 비밀번호 필드가 불완전하면 모달을 닫을 수 있음
        if (nicknameChanged && !isPasswordChangeAttempted) closeMyInfoModal();
        return;
      }
      if (myInfoNewPassword !== myInfoNewPasswordConfirm) {
        alert('새 비밀번호와 새 비밀번호 확인이 일치하지 않습니다.');
        return;
      }
      if (myInfoNewPassword.length < 8) {
        alert('새 비밀번호는 8자 이상이어야 합니다.');
        return;
      }

      try {
        const response = await fetch(`${BACKEND_URL}/auth/me/password`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
          body: JSON.stringify({
            currentPassword: myInfoCurrentPassword,
            newPassword: myInfoNewPassword,
          }),
        });
        const data = await response.json();
        if (response.ok) {
          alert('비밀번호가 성공적으로 변경되었습니다. 다시 로그인해주세요.');
          auth.logout();
          closeMyInfoModal();
          openLoginModal();
          return; // 성공 시 함수 종료
        } else {
          alert(data.message || '비밀번호 변경에 실패했습니다. 현재 비밀번호를 확인해주세요.');
        }
      } catch (error) {
        alert('비밀번호 변경 중 오류 발생');
      }
    }

    // 모든 작업 완료 후 (닉네임 변경만 성공했거나, 아무 작업도 성공하지 못했을 때 등)
    // 비밀번호 변경이 성공적으로 끝나면 위에서 return 되므로, 여기는 그 외의 경우에 도달합니다.
    if (nicknameChanged && !passwordChangedAttempted) { // 닉네임 변경만 성공하고 비밀번호 변경 시도가 없었으면
      closeMyInfoModal();
    } else if (nicknameChanged && passwordChangedAttempted) {
        // 닉네임 변경은 성공했으나 비밀번호 변경은 실패한 경우, 모달은 열어둠
    } else if (!nicknameChanged && !passwordChangedAttempted) {
        // 아무것도 변경되지 않았고, 아무 시도도 없었으면 이미 위에서 처리됨.
        // 이 경우는 닉네임 변경 시도 실패 + 비밀번호 변경 시도 없음 또는
        // 닉네임 변경 시도 없음 + 비밀번호 변경 시도 실패
        // 이런 경우엔 특정 액션 없이 모달이 열린 상태를 유지할 수 있음.
    }

  };

  return (
    <>
      <Header openLoginModal={openLoginModal} openRegisterModal={openRegisterModal} openMyInfoModal={openMyInfoModal} />
      {/* <header> 임시 헤더 - 나중에 Header 컴포넌트로 대체 </header> */}
      {/* <div className="logo">
        <h1><a href="#">별과 타로</a></h1>
      </div>
      <nav>
        <ul>
          <li><a href="#"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="icon" fill="currentColor" width="1em" height="1em"><path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/></svg> 타로점 보기</a></li>
          <li><a href="#"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="icon" fill="currentColor" width="1em" height="1em"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg> 오늘/올해의 운세</a></li>
          <li><a href="#"> 고민 해결</a></li>
          <li><a href="#"> 타로 첫걸음</a></li>
        </ul>
      </nav>
      <div className="user-menu">
        <a href="#" className="login-btn"> 로그인</a>
        <a href="#" className="signup-btn"> 가입하기</a>
      </div> */}

      <main className={isHeaderCollapsed ? 'main-collapsed' : ''}>{children}</main>

      <Footer />
      {/* <footer> 임시 푸터 - 나중에 Footer 컴포넌트로 대체 */}
      {/* <div className="footer-content">
            <nav className="footer-nav">
                <a href="#">이용약관</a> |
                <a href="#">개인정보처리방침</a> |
                <a href="#">FAQ</a> |
                <a href="#">문의하기</a>
            </nav>
            <p className="copyright">&copy; 2025 별과 타로 All Rights Reserved. ✨</p>
        </div> */}

      {/* Login Modal */}
      <div id="loginModal" className={`modal ${isLoginModalOpen ? 'modal-open' : ''}`}>
        {/* Mystical background effects */}
        <div className="modal-cosmic-background">
          {/* Floating stars effect */}
          {Array.from({ length: 15 }).map((_, i) => {
            const size = Math.random() * 3 + 1;
            const top = Math.random() * 100;
            const left = Math.random() * 100;
            const animationDuration = Math.random() * 8 + 8;
            return (
              <div key={i} style={{
                position: 'absolute',
                width: `${size}px`,
                height: `${size}px`,
                borderRadius: '50%',
                backgroundColor: 'rgba(255, 235, 59, 0.8)',
                boxShadow: '0 0 8px rgba(255, 235, 59, 0.6)',
                top: `${top}%`,
                left: `${left}%`,
                animation: `celestialPulse ${animationDuration}s infinite ease-in-out ${Math.random() * 3}s`,
                pointerEvents: 'none'
              }} />
            );
          })}
          
          {/* Nebula effects */}
          <div style={{
            position: 'absolute',
            top: '20%',
            right: '10%',
            width: '200px',
            height: '200px',
            background: 'radial-gradient(circle, rgba(255, 235, 59, 0.15) 0%, rgba(255, 180, 60, 0.05) 50%, transparent 70%)',
            borderRadius: '50%',
            filter: 'blur(20px)',
            animation: 'celestialPulse 15s infinite ease-in-out',
            pointerEvents: 'none'
          }} />
          
          <div style={{
            position: 'absolute',
            bottom: '25%',
            left: '15%',
            width: '150px',
            height: '150px',
            background: 'radial-gradient(circle, rgba(160, 90, 255, 0.12) 0%, rgba(120, 60, 200, 0.04) 50%, transparent 70%)',
            borderRadius: '50%',
            filter: 'blur(15px)',
            animation: 'celestialPulse 18s infinite ease-in-out 2s',
            pointerEvents: 'none'
          }} />
        </div>

        <div className="modal-content">
          <span className="close-btn" onClick={closeLoginModal}>&times;</span>
          <h3>별과 타로 로그인 🔮</h3>
          <form id="loginForm" onSubmit={handleLoginSubmit}>
            <div className="form-group">
              <label htmlFor="loginEmail">이메일</label>
              <input 
                type="email" 
                id="loginEmail" 
                name="loginEmail" 
                placeholder="이메일 주소를 입력해주세요" 
                required 
                value={loginEmail}
                onChange={(e) => setLoginEmail(e.target.value)}
              />
            </div>
            <div className="form-group">
              <label htmlFor="loginPassword">비밀번호</label>
              <input 
                type="password" 
                id="loginPassword" 
                name="loginPassword" 
                placeholder="비밀번호를 입력해주세요" 
                required 
                value={loginPassword}
                onChange={(e) => setLoginPassword(e.target.value)}
              />
            </div>
            <button type="submit" className="cta-button auth-button">로그인하기</button>
            <div className="social-login">
              <p>SNS 계정으로 간편하게 로그인하세요!</p>
              <button type="button" className="social-btn google-btn">
                {/* <img src="images/icons/google_logo.svg" alt="Google"> */}
                <span>구글 계정으로 로그인</span>
              </button>
              <button type="button" className="social-btn kakao-btn">
                {/* <img src="images/icons/kakao_logo.svg" alt="Kakao"> */}
                <span>카카오 계정으로 로그인</span>
              </button>
            </div>
            <p className="switch-form">아직 회원이 아니신가요? <a href="#" onClick={(e) => { e.preventDefault(); openRegisterModal(); }}>회원가입하기</a></p>
          </form>
        </div>
      </div>

      {/* Register Modal */}
      <div id="registerModal" className={`modal ${isRegisterModalOpen ? 'modal-open' : ''}`}>
        {/* Mystical background effects */}
        <div className="modal-cosmic-background">
          {/* Floating stars effect */}
          {Array.from({ length: 20 }).map((_, i) => {
            const size = Math.random() * 3 + 1;
            const top = Math.random() * 100;
            const left = Math.random() * 100;
            const animationDuration = Math.random() * 10 + 10;
            return (
              <div key={i} style={{
                position: 'absolute',
                width: `${size}px`,
                height: `${size}px`,
                borderRadius: '50%',
                backgroundColor: 'rgba(255, 235, 59, 0.7)',
                boxShadow: '0 0 8px rgba(255, 235, 59, 0.5)',
                top: `${top}%`,
                left: `${left}%`,
                animation: `celestialPulse ${animationDuration}s infinite ease-in-out ${Math.random() * 4}s`,
                pointerEvents: 'none'
              }} />
            );
          })}
          
          {/* Cosmic symbols */}
          {['✧', '⚝', '⚹', '◯'].map((symbol, i) => {
            const size = Math.random() * 25 + 15;
            const top = Math.random() * 80 + 10;
            const left = Math.random() * 80 + 10;
            const rotation = Math.random() * 360;
            const opacity = Math.random() * 0.1 + 0.05;
            return (
              <div key={`symbol-${i}`} style={{
                position: 'absolute',
                fontSize: `${size}px`,
                top: `${top}%`,
                left: `${left}%`,
                color: 'rgba(255, 235, 59, 0.6)',
                opacity: opacity,
                transform: `rotate(${rotation}deg)`,
                textShadow: '0 0 10px rgba(255, 235, 59, 0.2)',
                pointerEvents: 'none',
                animation: `celestialPulse ${12 + i * 2}s infinite ease-in-out ${i}s`
              }}>
                {symbol}
              </div>
            );
          })}
          
          {/* Nebula effects */}
          <div style={{
            position: 'absolute',
            top: '10%',
            left: '5%',
            width: '180px',
            height: '180px',
            background: 'radial-gradient(circle, rgba(160, 90, 255, 0.2) 0%, rgba(120, 60, 200, 0.08) 50%, transparent 70%)',
            borderRadius: '50%',
            filter: 'blur(25px)',
            animation: 'celestialPulse 20s infinite ease-in-out 1s',
            pointerEvents: 'none'
          }} />
        </div>

        <div className="modal-content">
          <span className="close-btn" onClick={closeRegisterModal}>&times;</span>
          <h3>별과 타로 회원가입 ✨</h3>
          <form id="registerForm" onSubmit={handleRegisterSubmit}>
            <div className="form-group">
              <label htmlFor="registerName">닉네임</label>
              <input 
                type="text" 
                id="registerName" 
                name="registerName" 
                placeholder="사용하실 닉네임을 입력해주세요" 
                required 
                value={registerName}
                onChange={(e) => setRegisterName(e.target.value)}
              />
            </div>
            <div className="form-group">
              <label htmlFor="registerEmail">이메일</label>
              <input 
                type="email" 
                id="registerEmail" 
                name="registerEmail" 
                placeholder="사용하실 이메일 주소" 
                required 
                value={registerEmail}
                onChange={(e) => setRegisterEmail(e.target.value)}
              />
            </div>
            <div className="form-group">
              <label htmlFor="registerPassword">비밀번호</label>
              <input 
                type="password" 
                id="registerPassword" 
                name="registerPassword" 
                placeholder="8자 이상, 영문/숫자/특수문자 포함" 
                required 
                value={registerPassword}
                onChange={(e) => setRegisterPassword(e.target.value)}
              />
            </div>
            <div className="form-group">
              <label htmlFor="registerPasswordConfirm">비밀번호 확인</label>
              <input 
                type="password" 
                id="registerPasswordConfirm" 
                name="registerPasswordConfirm" 
                placeholder="비밀번호를 다시 한번 입력해주세요" 
                required 
                value={registerPasswordConfirm}
                onChange={(e) => setRegisterPasswordConfirm(e.target.value)}
              />
            </div>
            <button type="submit" className="cta-button auth-button">가입 완료!</button>
            <div className="social-login">
              <p>SNS 계정으로 간편하게 가입하세요!</p>
              <button type="button" className="social-btn google-btn">
                {/* <img src="images/icons/google_logo.svg" alt="Google"> */}
                <span>구글 계정으로 가입하기</span>
              </button>
              <button type="button" className="social-btn kakao-btn">
                {/* <img src="images/icons/kakao_logo.svg" alt="Kakao"> */}
                <span>카카오 계정으로 가입하기</span>
              </button>
            </div>
            <p className="switch-form">이미 회원이신가요? <a href="#" onClick={(e) => { e.preventDefault(); openLoginModal(); }}>로그인하기</a></p>
          </form>
        </div>
      </div>

      {/* My Info Modal */}
      {auth.user && (
        <div id="myInfoModal" className={`modal ${isMyInfoModalOpen ? 'modal-open' : ''}`}>
          {/* Mystical background effects */}
          <div className="modal-cosmic-background">
            {/* Floating stars effect */}
            {Array.from({ length: 12 }).map((_, i) => {
              const size = Math.random() * 4 + 2;
              const top = Math.random() * 100;
              const left = Math.random() * 100;
              const animationDuration = Math.random() * 12 + 8;
              return (
                <div key={i} style={{
                  position: 'absolute',
                  width: `${size}px`,
                  height: `${size}px`,
                  borderRadius: '50%',
                  backgroundColor: 'rgba(255, 215, 120, 0.8)',
                  boxShadow: '0 0 10px rgba(255, 215, 120, 0.6)',
                  top: `${top}%`,
                  left: `${left}%`,
                  animation: `celestialPulse ${animationDuration}s infinite ease-in-out ${Math.random() * 2}s`,
                  pointerEvents: 'none'
                }} />
              );
            })}
            
            {/* Special cosmic elements for user info */}
            <div style={{
              position: 'absolute',
              top: '15%',
              right: '20%',
              width: '100px',
              height: '100px',
              background: 'radial-gradient(circle, rgba(255, 215, 120, 0.25) 0%, rgba(255, 180, 60, 0.1) 50%, transparent 70%)',
              borderRadius: '50%',
              filter: 'blur(15px)',
              animation: 'celestialPulse 14s infinite ease-in-out',
              pointerEvents: 'none'
            }} />
            
            <div style={{
              position: 'absolute',
              bottom: '20%',
              left: '10%',
              width: '120px',
              height: '120px',
              background: 'radial-gradient(circle, rgba(120, 160, 255, 0.2) 0%, rgba(80, 120, 200, 0.08) 50%, transparent 70%)',
              borderRadius: '50%',
              filter: 'blur(18px)',
              animation: 'celestialPulse 16s infinite ease-in-out 1.5s',
              pointerEvents: 'none'
            }} />
          </div>

          <div className="modal-content">
            <span className="close-btn" onClick={closeMyInfoModal}>&times;</span>
            <h3>내 정보 수정 ✨</h3>
            <form id="myInfoForm" onSubmit={handleMyInfoSubmit}>
              <div className="form-group">
                <label htmlFor="myInfoEmail">이메일 (변경 불가)</label>
                <input type="email" id="myInfoEmail" name="myInfoEmail" value={auth.user.email} readOnly />
              </div>
              <div className="form-group">
                <label htmlFor="myInfoName">닉네임</label>
                <input 
                  type="text" 
                  id="myInfoName" 
                  name="myInfoName" 
                  value={myInfoName} 
                  onChange={(e) => setMyInfoName(e.target.value)} 
                  required 
                />
              </div>
              <div className="form-group">
                <label htmlFor="myInfoCredits">보유 크레딧</label>
                <input type="text" id="myInfoCredits" name="myInfoCredits" value={auth.user.credits} readOnly />
              </div>
              <hr style={{ margin: '20px 0' }} /> {/* 구분선 */}

              <h4>비밀번호 변경 (선택)</h4>
              <div className="form-group">
                <label htmlFor="myInfoCurrentPassword">현재 비밀번호</label>
                <input 
                  type="password" 
                  id="myInfoCurrentPassword" 
                  name="myInfoCurrentPassword" 
                  placeholder="현재 비밀번호 입력"
                  value={myInfoCurrentPassword}
                  onChange={(e) => setMyInfoCurrentPassword(e.target.value)}
                />
              </div>
              <div className="form-group">
                <label htmlFor="myInfoNewPassword">새 비밀번호</label>
                <input 
                  type="password" 
                  id="myInfoNewPassword" 
                  name="myInfoNewPassword" 
                  placeholder="8자 이상, 영문/숫자/특수문자 포함"
                  value={myInfoNewPassword}
                  onChange={(e) => setMyInfoNewPassword(e.target.value)}
                />
              </div>
              <div className="form-group">
                <label htmlFor="myInfoNewPasswordConfirm">새 비밀번호 확인</label>
                <input 
                  type="password" 
                  id="myInfoNewPasswordConfirm" 
                  name="myInfoNewPasswordConfirm" 
                  placeholder="새 비밀번호를 다시 한번 입력해주세요"
                  value={myInfoNewPasswordConfirm}
                  onChange={(e) => setMyInfoNewPasswordConfirm(e.target.value)}
                />
              </div>
              <button type="submit" className="cta-button auth-button">정보 수정하기</button>
            </form>
          </div>
        </div>
      )}
    </>
  );
};

export default Layout; 