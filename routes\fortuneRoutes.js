const express = require('express');
const router = express.Router();
const { GoogleGenAI } = require('@google/genai');
const jwt = require('jsonwebtoken');
const authenticateToken = require('../middleware/authenticateToken');
const { prisma } = require('../middleware/database');
const { logTarotReading, extractTokenUsage, READING_TYPES } = require('./services/readingLogger');
const questionHistoryService = require('./services/questionHistoryService');

// Import utility modules
const { 
  FORTUNE_TYPES, 
  handleError, 
  YEAR_FORTUNE_COST, 
  CUSTOM_TAROT_COST_3_CARDS,
  CUSTOM_TAROT_COST_5_CARDS,
  CUSTOM_TAROT_COST_7_CARDS,
  CUSTOM_TAROT_COST_10_CARDS,
  NUM_CARDS_THREE_SPREAD, 
  NUM_CARDS_FIVE_SPREAD,
  NUM_CARDS_SEVEN_SPREAD,
  NUM_CARDS_TEN_SPREAD
} = require('./utils/fortuneUtils');
const { generateFortunePrompt } = require('./utils/promptUtils');
const fortuneService = require('./services/fortuneService');

// Initialize Google Gemini API
const genAI = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY });

// Middleware to make authentication optional
const authenticateToken_optional = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (token == null) {
    req.user = null; // No token, no user
    return next();
  }

  jwt.verify(token, process.env.JWT_SECRET || 'your-very-secure-secret-key', (err, userPayload) => {
    if (err) {
      req.user = null; // Invalid token, no user
      return next();
    }
    req.user = userPayload; // userPayload contains { userId, email, iat, exp }
    next();
  });
};

// GET /api/fortune/pre-check - Check credits and cooldown before starting a paid fortune
router.get('/pre-check', authenticateToken_optional, async (req, res) => {
  const { fortuneType } = req.query;
  const userId = req.user ? req.user.userId : null;
  const ipAddress = req.ip;

  console.log(`[Request Log / IP: ${ipAddress}] GET /api/fortune/pre-check?fortuneType=${fortuneType}`);
  console.log(`[Auth Info] User ID: ${userId || 'Not logged in'}`);

  if (!fortuneType) {
    return res.status(400).json({ error: 'Missing fortuneType query parameter' });
  }

  // Check if it's a static fortune type
  const staticFortuneTypes = [
    FORTUNE_TYPES.YEAR, 
    FORTUNE_TYPES.YEAR_FIVE_CARD, 
    FORTUNE_TYPES.TODAY_THREE_CARD, 
    FORTUNE_TYPES.TODAY_FIVE_CARD, 
    FORTUNE_TYPES.CUSTOM_THREE_CARD,
    FORTUNE_TYPES.CUSTOM_FIVE_CARD,
    FORTUNE_TYPES.CUSTOM_SEVEN_CARD,
    FORTUNE_TYPES.CUSTOM_TEN_CARD
  ];
  
  const isStaticType = staticFortuneTypes.includes(fortuneType);
  const isManagerSpread = fortuneType.startsWith('custom') && !isStaticType;
  
  if (!isStaticType && !isManagerSpread) {
    return res.status(400).json({ error: `Invalid fortuneType for pre-check. Must be a known static type or start with 'custom'` });
  }
  
  // For manager spreads, verify they exist and are active in database
  if (isManagerSpread) {
    try {
      
      const spread = await prisma.tarotSpread.findFirst({
        where: {
          spreadType: fortuneType,
          isActive: true
        }
      });
      
      if (!spread) {
        return res.status(400).json({ error: `Manager spread '${fortuneType}' not found or not active` });
      }
    } catch (dbError) {
      console.error('[Pre-check Error] Failed to verify manager spread:', dbError);
      return res.status(500).json({ error: 'Failed to verify spread configuration' });
    }
  }

  // 유료 운세 또는 커스텀 타로인 경우 로그인 확인
  const isPaidOrCustom = [
    FORTUNE_TYPES.YEAR, 
    FORTUNE_TYPES.YEAR_FIVE_CARD, 
    FORTUNE_TYPES.CUSTOM_THREE_CARD,
    FORTUNE_TYPES.CUSTOM_FIVE_CARD,
    FORTUNE_TYPES.CUSTOM_SEVEN_CARD,
    FORTUNE_TYPES.CUSTOM_TEN_CARD
  ].includes(fortuneType);

  if (isPaidOrCustom && !userId) {
    console.log(`[Pre-Check Error] 유료/커스텀 운세(${fortuneType}) 요청에 로그인 정보 없음`);
    const reason = fortuneType.startsWith('custom') ? 'login_required' : 'login_required_for_paid_fortune';
    return res.status(401).json({
          isAllowed: false,
      reason: reason,
      error: '로그인이 필요한 서비스입니다.'
    });
  }

  try {
    const checkResult = await fortuneService.checkCreditsAndCooldown(userId, ipAddress, fortuneType);
    console.log(`[Pre-Check Result] isAllowed: ${checkResult.isAllowed}, reason: ${checkResult.reason || 'none'}`);

    if (!checkResult.isAllowed) {
      // Return with appropriate status code and reason
      const responseData = {
          isAllowed: false,
        reason: checkResult.reason,
        ...checkResult.data,
        error: checkResult.error
      };
      console.log(`[Pre-Check Response] Failed Response:`, responseData);
      
      // 항상 200 상태 코드를 반환하되, 상세 정보는 응답 데이터에 포함
      // 이렇게 하면 프론트엔드에서 isAllowed 플래그로 일관되게 처리 가능
      let statusCode = 200;
      
      // user_not_found 경우만 404로 처리 (로그인 페이지로 리디렉션 필요)
      if (checkResult.reason === 'user_not_found') {
        statusCode = 404;
      } 
      // login_required_for_paid_fortune은 401로 처리 (로그인 필요 메시지)
      else if (checkResult.reason === 'login_required_for_paid_fortune') {
        statusCode = 401;
      }
      // 다른 모든 경우(insufficient_credits, cooldown_active 등)는 200으로 처리하고
      // 프론트엔드에서 responseData.isAllowed와 responseData.reason으로 구분
      
      return res.status(statusCode).json(responseData);
    }
      
    // All checks passed
    const responseData = {
      isAllowed: true,
      currentCredits: checkResult.data.currentCredits,
    };
    console.log(`[Pre-Check Response] Sending success:`, responseData);
    return res.status(200).json(responseData);
  } catch (error) {
    console.error('[Pre-check Error]:', error);
    return res.status(500).json({ error: '사전 검사 중 서버 오류가 발생했습니다.' });
  }
});

// POST /api/fortune/generate - Generate fortune interpretation
router.post('/generate', authenticateToken_optional, async (req, res) => {
  const { userName, selectedCard, selectedCards, fortuneType, userConcern, spreadId } = req.body;
  
  // Track if credit has already been checked and verified
  let creditAlreadyVerified = false;
  let updatedCredits = 0;
  let costForThisFortune = 0;
  
  // 요청 ID 생성 (중복 요청 추적용)
  const requestId = Date.now().toString() + Math.random().toString(36).substring(2, 10);
  
  // Get user ID and IP address for authentication, cooldown, and credit checks
  const userId = req.user ? req.user.userId : null;
  const ipAddress = req.ip || req.socket.remoteAddress || 'unknown';

  console.log(`[Generate Request ${requestId}] New ${fortuneType} fortune request from ${userId || 'Guest'} (${ipAddress})`);
  console.log(`[Generate Request ${requestId}] userName: ${userName}, cards: ${selectedCards ? selectedCards.length : 'none'}`);

  if (!userName || !fortuneType) {
    return res.status(400).json({ error: 'Missing required fields: userName, fortuneType' });
  }
  
  const isCustomTarotType = fortuneType.startsWith('custom');

  // Validate userConcern if it's a custom tarot reading
  if (isCustomTarotType && (!userConcern || typeof userConcern !== 'string' || userConcern.trim() === '')) {
    return res.status(400).json({ error: 'Missing or invalid userConcern for custom tarot reading.' });
  }
  
  // Valid fortune types update
  const validFortuneTypes = Object.values(FORTUNE_TYPES);
  const isManagerSpreadType = fortuneType.startsWith('custom') && !validFortuneTypes.includes(fortuneType);
  
  // 매니저 스프레드가 아닌 경우에만 기존 fortuneType 검증
  if (!isManagerSpreadType && !validFortuneTypes.includes(fortuneType)) {
    return res.status(400).json({ error: 'Invalid fortuneType specified.' });
  }

  // 유료 운세 또는 커스텀 타로인 경우 반드시 로그인 필요
  const paidOrCustomRequiresLogin = [
    FORTUNE_TYPES.YEAR_FIVE_CARD, 
    FORTUNE_TYPES.CUSTOM_THREE_CARD,
    FORTUNE_TYPES.CUSTOM_FIVE_CARD,
    FORTUNE_TYPES.CUSTOM_SEVEN_CARD,
    FORTUNE_TYPES.CUSTOM_TEN_CARD
    // FORTUNE_TYPES.YEAR is also paid but might be handled by a different flow or pre-check logic.
    // For generate, it seems YEAR_FIVE_CARD is the primary paid year fortune.
  ].includes(fortuneType) || isManagerSpreadType; // 매니저 스프레드도 로그인 필요

  if (paidOrCustomRequiresLogin && !userId) {
    console.log(`[Generate Error] 유료/커스텀 운세(${fortuneType}) 요청에 로그인 정보 없음`);
    let reason = (fortuneType.startsWith('custom') || isManagerSpreadType) ? 'login_required' : 'login_required_for_paid_fortune';
    return res.status(401).json({ 
      error: '로그인이 필요한 서비스입니다.', 
      reason: reason 
    });
  }

  // Determine required number of cards based on fortune type
  let requiredCards;
  let managerSpreadData = null;
  
  // Use the isManagerSpreadType we already defined above
  const isManagerSpread = isManagerSpreadType;
  
  if (isManagerSpread) {
    try {
      
      // spreadId가 있으면 해당 ID로 찾고, 없으면 spreadType으로 찾기
      if (spreadId) {
        managerSpreadData = await prisma.tarotSpread.findFirst({
          where: {
            id: spreadId,
            isActive: true
          }
        });
      } else {
        managerSpreadData = await prisma.tarotSpread.findFirst({
          where: {
            spreadType: fortuneType,
            isActive: true
          }
        });
      }
      
      if (!managerSpreadData) {
        return res.status(400).json({ error: `Manager spread '${fortuneType}' not found or not active` });
      }
      
      // Get card count from positions array
      const positions = JSON.parse(managerSpreadData.positions || '[]');
      requiredCards = positions.length;
      
    } catch (dbError) {
      console.error('[Generate Error] Failed to get manager spread data:', dbError);
      return res.status(500).json({ error: 'Failed to get spread configuration' });
    }
  } else {
    requiredCards = getRequiredCardCount(fortuneType);
  }

  // Validate card selections
  if (!validateCardSelections(selectedCard, selectedCards, requiredCards)) {
    return res.status(400).json({ 
      error: `Invalid card selection for ${fortuneType}. Required: ${requiredCards === 1 ? 'selectedCard object' : `selectedCards array with ${requiredCards} cards`}` 
    });
  }

  try {
    // Pre-API Checks (Auth, Cooldown, Credits)
    const checkResult = await fortuneService.checkCreditsAndCooldown(userId, ipAddress, fortuneType);
    console.log(`[Generate Pre-Check ${requestId}] isAllowed: ${checkResult.isAllowed}, reason: ${checkResult.reason || 'none'}`);
    
    if (!checkResult.isAllowed) {
      let statusCode = 200;
      let responseData = { 
        error: checkResult.error || '요청을 처리할 수 없습니다.', 
        reason: checkResult.reason 
      };
      
      if (checkResult.reason === 'login_required_for_paid_fortune') {
        statusCode = 401;
      } else if (checkResult.reason === 'user_not_found') {
        statusCode = 404;
        responseData = { 
          error: checkResult.error || '사용자 정보를 찾을 수 없습니다. 다시 로그인해주세요.', 
          reason: 'user_not_found' 
        };
      } else if (checkResult.reason === 'insufficient_credits') {
        statusCode = 402;
        responseData = {
          error: '크레딧이 부족합니다.',
          reason: checkResult.reason,
          currentCredits: checkResult.data.currentCredits,
          neededCredits: checkResult.data.neededCredits
        };
      } else if (checkResult.reason === 'cooldown_active') {
        statusCode = 429;
        responseData = {
          error: '쿨타임 중입니다. 나중에 다시 시도해주세요.',
          reason: checkResult.reason,
          fortuneType,
          cooldownExpiresAt: checkResult.data.cooldownExpiresAt,
          remainingTime: checkResult.data.remainingTime,
          cooldownSource: checkResult.data.cooldownSource
        };
      } else if (checkResult.reason === 'potential_duplicate_request') {
        statusCode = 429; // Too Many Requests 
        responseData = {
          error: '같은 요청이 처리 중입니다. 잠시 후 다시 시도해주세요.',
          reason: checkResult.reason,
          fortuneType
        };
      }
      
      console.log(`[Generate Response ${requestId}] Sending error(${statusCode}):`, responseData);
      return res.status(statusCode).json(responseData);
    }

    // At this point, user is allowed by pre-check (credits were sufficient for attempt, cooldown passed if applicable)
    let currentCreditsBeforeDeduction = checkResult.data.currentCredits;
    creditAlreadyVerified = true;

    // Generate the prompt
    let prompt = generateFortunePrompt(userName, fortuneType, selectedCard, selectedCards, userConcern, managerSpreadData);

    // 질문 히스토리 조회 및 심리적 컨텍스트 추가 (타로 리딩에만 적용)
    if (isCustomTarotType || isManagerSpread) {
      let questionHistoryContext = '';
      try {
        const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
        const recentHistory = await questionHistoryService.getRecentHistory(userId, ipAddress, 5);
        
        questionHistoryContext = questionHistoryService.generatePsychologicalContext(recentHistory, currentDate);
        
        // 심리적 컨텍스트를 프롬프트에 자연스럽게 통합
        if (questionHistoryContext.trim()) {
          prompt = questionHistoryContext + '\n\n' + prompt;
          console.log(`[Question History ${requestId}] Applied psychological context based on ${recentHistory.length} previous questions`);
        }
      } catch (historyError) {
        console.error(`[Question History Error ${requestId}]:`, historyError);
        // 히스토리 조회 실패가 메인 기능을 방해하지 않도록 계속 진행
      }
    }

    // 글로벌 시스템 명령어 추가
    try {
      const globalSettings = await prisma.tarotGlobalSettings.findMany();
      const globalSystemInstruction = globalSettings.find(s => s.settingKey === 'global_system_instruction')?.settingValue;
      
      if (globalSystemInstruction?.trim()) {
        prompt = `**[중요 글로벌 시스템 지침]**\n${globalSystemInstruction}\n\n${prompt}`;
        console.log(`[Global System Instruction ${requestId}] Applied to prompt`);
      }
    } catch (globalSettingsError) {
      console.error(`[Global Settings Error ${requestId}]:`, globalSettingsError);
      // 글로벌 설정 오류가 있어도 계속 진행
    }

    // Log the prompt for debugging (consider removing in production)
    console.log(`[Gemini Prompt ${requestId}] For ${fortuneType}, user ${userId || ipAddress}:\n${prompt.substring(0, 500)}...`);
    
    // Call the Gemini API with timeout
    let geminiResult;
    try {
      const geminiPromise = genAI.models.generateContent({ 
        model: "gemini-2.0-flash",
        contents: [{ role: "user", parts: [{text: prompt}] }] 
    });
    
      // 30초 타임아웃 설정
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Gemini API request timed out')), 30000)
      );
      
      geminiResult = await Promise.race([geminiPromise, timeoutPromise]);
      
    } catch (apiError) {
      console.error("[Gemini API Error]:", apiError);
      if (apiError.message.includes('timed out')) {
        throw new Error('Gemini API request timed out. 요청 시간이 초과되었습니다.');
      } else if (apiError.name === 'AbortError' || apiError.message.includes('network')) {
        throw new Error('Network error when calling Gemini API');
      } else if (apiError.message.includes('API key')) {
        throw new Error('API key not valid');
      } else if (apiError.response && apiError.response.promptFeedback) {
        throw apiError; // Handle blockReasons in handleError
      } else {
        throw new Error(`Gemini API error: ${apiError.message}`);
      }
    }
    
    // Extract the interpretation from the response
    let interpretation;
    if (geminiResult && 
        geminiResult.candidates && 
        geminiResult.candidates[0] && 
        geminiResult.candidates[0].content && 
        geminiResult.candidates[0].content.parts && 
        geminiResult.candidates[0].content.parts[0] && 
        geminiResult.candidates[0].content.parts[0].text) {
      interpretation = geminiResult.candidates[0].content.parts[0].text;
      
      // 빈 응답이나 너무 짧은 응답 확인
      if (!interpretation || interpretation.trim().length < 20) {
        console.error("[Gemini Empty Response]:", interpretation);
        throw new Error('Gemini API returned empty or too short response');
      }
    } else {
      console.error("Unexpected response structure from Gemini API:", geminiResult);
      throw new Error("Failed to parse fortune interpretation from API response.");
    }
    
    console.log(`[Gemini Response ${requestId}] Text Length:`, interpretation.length);

    // 질문 히스토리에 저장 (타로 리딩에만 적용, 비동기로 처리하여 응답 속도에 영향 없도록)
    if (isCustomTarotType || isManagerSpread) {
      setImmediate(async () => {
        try {
          // 질문 텍스트 생성 (커스텀 타로인 경우 userConcern 사용)
          const questionText = isCustomTarotType && userConcern 
            ? userConcern 
            : `${fortuneType} 타로 리딩 요청`;
            
          await questionHistoryService.saveQuestionHistory(
            userId,
            ipAddress,
            questionText,
            fortuneType,
            isManagerSpread ? fortuneType : null,
            interpretation,
            req.get('User-Agent') || 'Unknown',
            userName  // 상담받은 이름 추가
          );
          
          console.log(`[Question History ${requestId}] Saved tarot reading question and response to history`);
        } catch (saveError) {
          console.error(`[Question History Save Error ${requestId}]:`, saveError);
          // 히스토리 저장 실패는 에러를 던지지 않음
        }
      });
    }

    // 리딩 기록 로깅
    try {
      const tokenUsage = extractTokenUsage(geminiResult);
      const cardCount = selectedCards ? selectedCards.length : (selectedCard ? 1 : 0);
      
      // 리딩 타입 결정
      let readingType = READING_TYPES.FORTUNE_GENERAL;
      if (fortuneType.includes('today')) {
        readingType = READING_TYPES.FORTUNE_TODAY;
      } else if (fortuneType.includes('year')) {
        readingType = READING_TYPES.FORTUNE_YEAR;
      } else if (fortuneType.startsWith('custom')) {
        readingType = READING_TYPES.TAROT_SPREAD;
      }
      
      await logTarotReading({
        userId: userId,
        readingType: readingType,
        spreadType: isManagerSpread ? fortuneType : null,
        cardCount: cardCount,
        promptTokens: tokenUsage.promptTokens,
        completionTokens: tokenUsage.completionTokens,
        totalTokens: tokenUsage.totalTokens,
        ipAddress: ipAddress,
        userAgent: req.get('User-Agent')
      });
      
      console.log(`[Reading Logger ${requestId}] Logged reading: ${readingType}, Cards: ${cardCount}, Tokens: ${tokenUsage.totalTokens}`);
    } catch (loggingError) {
      console.error(`[Reading Logger Error ${requestId}]:`, loggingError);
      // 로깅 실패가 메인 기능을 방해하지 않도록 계속 진행
    }

    updatedCredits = currentCreditsBeforeDeduction; // Initialize with credits before potential deduction
      
    // Handle credit deduction and cooldown recording based on fortune type
    // This happens AFTER successful Gemini API call
    if (fortuneType === FORTUNE_TYPES.YEAR_FIVE_CARD) {
      costForThisFortune = YEAR_FORTUNE_COST;
      if (userId && creditAlreadyVerified) { // Should always be true due to earlier checks
        updatedCredits = await fortuneService.deductCredits(userId, costForThisFortune);
        await fortuneService.recordCooldown(userId, ipAddress, fortuneType);
        console.log(`[Credit Deduction ${requestId}] Deducted ${costForThisFortune} credits for ${fortuneType}. New balance: ${updatedCredits}`);
      }
    } else if (fortuneType === FORTUNE_TYPES.CUSTOM_THREE_CARD) {
      costForThisFortune = CUSTOM_TAROT_COST_3_CARDS;
      if (userId && creditAlreadyVerified) { 
        updatedCredits = await fortuneService.deductCredits(userId, costForThisFortune); 
        console.log(`[Credit Deduction ${requestId}] Deducted ${costForThisFortune} credits for ${fortuneType}. New balance: ${updatedCredits}`);
      }
      // No cooldown for custom tarot
    } else if (fortuneType === FORTUNE_TYPES.CUSTOM_FIVE_CARD) {
      costForThisFortune = CUSTOM_TAROT_COST_5_CARDS;
      if (userId && creditAlreadyVerified) { 
        updatedCredits = await fortuneService.deductCredits(userId, costForThisFortune); 
        console.log(`[Credit Deduction ${requestId}] Deducted ${costForThisFortune} credits for ${fortuneType}. New balance: ${updatedCredits}`);
      }
      // No cooldown
    } else if (fortuneType === FORTUNE_TYPES.CUSTOM_SEVEN_CARD) {
      costForThisFortune = CUSTOM_TAROT_COST_7_CARDS;
      if (userId && creditAlreadyVerified) { 
        updatedCredits = await fortuneService.deductCredits(userId, costForThisFortune); 
        console.log(`[Credit Deduction ${requestId}] Deducted ${costForThisFortune} credits for ${fortuneType}. New balance: ${updatedCredits}`);
      }
      // No cooldown
    } else if (fortuneType === FORTUNE_TYPES.CUSTOM_TEN_CARD) {
      costForThisFortune = CUSTOM_TAROT_COST_10_CARDS;
      if (userId && creditAlreadyVerified) { 
        updatedCredits = await fortuneService.deductCredits(userId, costForThisFortune); 
        console.log(`[Credit Deduction ${requestId}] Deducted ${costForThisFortune} credits for ${fortuneType}. New balance: ${updatedCredits}`);
      }
      // No cooldown
    } else if (isManagerSpread && managerSpreadData) {
      // Use the same calculation logic as in fortuneService.js for consistency
      const baseCost = managerSpreadData.cost || 0;
      const discountRate = managerSpreadData.discountRate || managerSpreadData.discount || 0;
      
      // Apply discount
      costForThisFortune = Math.max(1, Math.round(baseCost * (100 - discountRate) / 100));
      
      if (userId && creditAlreadyVerified) { 
        updatedCredits = await fortuneService.deductCredits(userId, costForThisFortune); 
        console.log(`[Credit Deduction ${requestId}] Deducted ${costForThisFortune} credits for manager spread ${fortuneType} (base: ${baseCost}, discount: ${discountRate}%). New balance: ${updatedCredits}`);
      }
      // No cooldown for manager spreads
    } else if (fortuneType === FORTUNE_TYPES.TODAY_THREE_CARD || fortuneType === FORTUNE_TYPES.TODAY_FIVE_CARD) { 
      // Only record cooldown for free fortunes
      await fortuneService.recordCooldown(userId, ipAddress, fortuneType);
    }

    console.log(`[Generate Response ${requestId}] Success for ${fortuneType}. User: ${userId || 'Guest'}. Cost: ${costForThisFortune}. Updated Credits: ${updatedCredits}`);
    res.json({ interpretation, updatedCredits });

  } catch (error) {
    return handleError(error, res);
  }
});

/**
 * Get the required number of cards for a fortune type
 * @param {string} fortuneType - The type of fortune
 * @returns {number} The required number of cards
 */
function getRequiredCardCount(fortuneType) {
  if (fortuneType === FORTUNE_TYPES.TODAY_THREE_CARD) return NUM_CARDS_THREE_SPREAD;
  if (fortuneType === FORTUNE_TYPES.YEAR_FIVE_CARD) return NUM_CARDS_FIVE_SPREAD;
  if (fortuneType === FORTUNE_TYPES.YEAR) return 1; // Legacy single card year
  if (fortuneType === FORTUNE_TYPES.TODAY_FIVE_CARD) return NUM_CARDS_FIVE_SPREAD;
  if (fortuneType === FORTUNE_TYPES.CUSTOM_THREE_CARD) return NUM_CARDS_THREE_SPREAD;
  if (fortuneType === FORTUNE_TYPES.CUSTOM_FIVE_CARD) return NUM_CARDS_FIVE_SPREAD;
  if (fortuneType === FORTUNE_TYPES.CUSTOM_SEVEN_CARD) return NUM_CARDS_SEVEN_SPREAD;
  if (fortuneType === FORTUNE_TYPES.CUSTOM_TEN_CARD) return NUM_CARDS_TEN_SPREAD;
  
  // For manager spreads, this function shouldn't be called - they should be handled dynamically
  // But if called, return a reasonable default
  if (fortuneType.startsWith('custom')) {
    console.warn(`[Card Count] Manager spread ${fortuneType} should be handled dynamically, not through getRequiredCardCount.`);
    return 5; // Default fallback
  }
  
  console.warn(`[Card Count] Unknown fortuneType: ${fortuneType} in getRequiredCardCount. Defaulting to 0.`);
  return 0;
}

/**
 * Validate card selections based on required count
 * @param {Object} selectedCard - Selected card for single-card readings
 * @param {Array} selectedCards - Selected cards for multi-card readings
 * @param {number} requiredCards - Required number of cards
 * @returns {boolean} Whether the selection is valid
 */
function validateCardSelections(selectedCard, selectedCards, requiredCards) {
  if (requiredCards > 1) {
    return selectedCards && 
           Array.isArray(selectedCards) && 
           selectedCards.length === requiredCards && 
           selectedCards.every(card => card && card.name && card.description);
  } else if (requiredCards === 1) {
    return selectedCard && selectedCard.name && selectedCard.description;
    }
    
  return false;
  }

module.exports = router; 