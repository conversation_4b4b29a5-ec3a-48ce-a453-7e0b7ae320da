/**
 * Utility functions for fortune routes
 */

// Constants
const YEAR_FORTUNE_COST = 5;
const CUSTOM_TAROT_COST_3_CARDS = 3;
const CUSTOM_TAROT_COST_5_CARDS = 5;
const CUSTOM_TAROT_COST_7_CARDS = 7;
const CUSTOM_TAROT_COST_10_CARDS = 10;
const CUSTOM_CONCERN_TAROT_COST = 5; // New constant for concern-based tarot
const COOLDOWN_DURATION_MS = 24 * 60 * 60 * 1000; // 24 hours (deprecated - use getNextSeoul6AM instead)
const NUM_CARDS_TO_SELECT = 5;
const NUM_CARDS_THREE_SPREAD = 3;
const NUM_CARDS_FIVE_SPREAD = 5;
const NUM_CARDS_SEVEN_SPREAD = 7; // For custom tarot
const NUM_CARDS_TEN_SPREAD = 10;   // For custom tarot

// Fortune types enum for consistency
const FORTUNE_TYPES = {
  TODAY_THREE_CARD: 'todayThreeCard',
  TODAY_FIVE_CARD: 'todayFiveCard',
  YEAR: 'year',
  YEAR_FIVE_CARD: 'yearFiveCard',
  CUSTOM_TAROT_READING: 'customTarotReading', // Value changed for user concern based tarot
  CUSTOM_THREE_CARD: 'customThreeCard',
  CUSTOM_FIVE_CARD: 'customFiveCard',
  CUSTOM_SEVEN_CARD: 'customSevenCard',
  CUSTOM_TEN_CARD: 'customTenCard',
  // Note: Manager spread types are handled dynamically and don't need to be defined here
  TODAY: 'today', // Legacy
};

/**
 * Get next 6 AM Seoul time from current time
 * @param {Date} currentTime - Current time (optional, defaults to now)
 * @returns {Date} Next 6 AM Seoul time
 */
const getNextSeoul6AM = (currentTime = new Date()) => {
  // Seoul timezone offset: UTC+9
  const SEOUL_OFFSET_HOURS = 9;
  const COOLDOWN_RESET_HOUR = 6; // 6 AM
  
  // Convert current time to Seoul time
  const seoulTime = new Date(currentTime.getTime() + (SEOUL_OFFSET_HOURS * 60 * 60 * 1000));
  
  // Create next 6 AM Seoul time
  const next6AM = new Date(seoulTime);
  next6AM.setUTCHours(COOLDOWN_RESET_HOUR, 0, 0, 0); // Set to 6:00:00.000 AM Seoul time
  
  // If current Seoul time is already past 6 AM today, move to next day
  if (seoulTime.getUTCHours() >= COOLDOWN_RESET_HOUR) {
    next6AM.setUTCDate(next6AM.getUTCDate() + 1);
  }
  
  // Convert back to UTC for storage
  const next6AM_UTC = new Date(next6AM.getTime() - (SEOUL_OFFSET_HOURS * 60 * 60 * 1000));
  
  return next6AM_UTC;
};

/**
 * Calculate remaining time from a cooldown expiration date
 * @param {Date} cooldownExpiresAt - The expiration date
 * @returns {Object|null} Object containing hours, minutes, and total milliseconds, or null if expired
 */
const getRemainingCooldownTime = (cooldownExpiresAt) => {
  const now = new Date();
  const remaining = cooldownExpiresAt.getTime() - now.getTime();
  if (remaining <= 0) return null;

  const hours = Math.floor(remaining / (1000 * 60 * 60));
  const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
  return { hours, minutes, totalMilliseconds: remaining };
};

/**
 * Get Seoul time string for display
 * @param {Date} date - Date to convert (optional, defaults to now)
 * @returns {string} Seoul time string
 */
const getSeoulTimeString = (date = new Date()) => {
  const SEOUL_OFFSET_HOURS = 9;
  const seoulTime = new Date(date.getTime() + (SEOUL_OFFSET_HOURS * 60 * 60 * 1000));
  return seoulTime.toISOString().replace('T', ' ').substring(0, 19) + ' (서울시간)';
};

/**
 * Log credit check information for debugging
 * @param {string} context - The context of the check
 * @param {string} userId - The user ID
 * @param {Object} userData - User data from database
 * @param {number} cost - The required credit cost
 */
const logCreditCheck = (context, userId, userData, cost) => {
  console.log(`[Credit Check - ${context}] User ID: ${userId}`);
  if (userData) {
    console.log(`[Credit Check - ${context}] User Data:`, JSON.stringify(userData));
    console.log(`[Credit Check - ${context}] User Credits: ${userData.credits}, Type: ${typeof userData.credits}`);
  } else {
    console.log(`[Credit Check - ${context}] User Data: Not found or null`);
  }
  console.log(`[Credit Check - ${context}] Required Cost: ${cost}`);
};

/**
 * Handle errors in fortune routes consistently
 * @param {Error} error - The error object
 * @param {Response} res - Express response object
 * @returns {Response} HTTP response with appropriate status and error message
 */
const handleError = (error, res) => {
  console.error('Error in fortune route:', error);
  
  let errorMessage = 'Failed to generate fortune interpretation.';
  let statusCode = 500;
  let contactAdmin = true;
  
  if (error.message && error.message.includes('API key not valid')) {
    errorMessage = 'Google API Key is not valid. Please check your .env file.';
  } else if (error.response && error.response.promptFeedback && error.response.promptFeedback.blockReason) {
    errorMessage = `Content generation blocked. Reason: ${error.response.promptFeedback.blockReason}`;
    if (error.response.promptFeedback.blockReasonMessage) {
      errorMessage += ` Message: ${error.response.promptFeedback.blockReasonMessage}`;
    }
  } else if (error.message && error.message.includes("Unexpected response structure from Gemini API")) {
    errorMessage = "운세 생성 중 일시적인 오류가 발생했습니다. 잠시 후 다시 시도해주세요. 문제가 계속되면 관리자에게 연락해주세요.";
  } else if (error.message && error.message.includes("Failed to parse fortune interpretation")) {
    errorMessage = "운세 해석 처리 중 오류가 발생했습니다. 잠시 후 다시 시도해주세요. 문제가 계속되면 관리자에게 연락해주세요.";
  } else if (error.message && (error.message.includes("User not found") || error.message.includes("user_not_found"))) {
    errorMessage = "사용자 정보를 찾을 수 없습니다. 다시 로그인해주세요.";
    statusCode = 404;
    contactAdmin = false;
    console.error('[Critical Error] 사용자 데이터 찾기 실패:', error.message);
    return res.status(statusCode).json({ 
      error: errorMessage, 
      reason: 'user_not_found_during_op',
      contactAdmin: contactAdmin
    });
  } else if (error.message && error.message.includes("User not found during cooldown recording")) {
    errorMessage = "사용자 정보를 찾을 수 없습니다. 다시 로그인해주세요.";
    statusCode = 404;
    contactAdmin = true;
    console.error('[Critical Error] 쿨다운 기록 중 사용자 데이터 찾기 실패:', error.message);
    return res.status(statusCode).json({ 
      error: errorMessage, 
      reason: 'user_not_found_during_cooldown',
      contactAdmin: contactAdmin
    });
  } else if (error.message && error.message.includes("User not found during credit deduction")) {
    errorMessage = "사용자 정보를 찾을 수 없습니다. 다시 로그인해주세요.";
    statusCode = 404;
    contactAdmin = true;
    console.error('[Critical Error] 크레딧 차감 중 사용자 데이터 찾기 실패:', error.message);
    return res.status(statusCode).json({ 
      error: errorMessage, 
      reason: 'user_not_found_during_deduction',
      contactAdmin: contactAdmin
    });
  } else if (error.message && error.message.includes("Insufficient credits")) {
    errorMessage = "크레딧이 부족합니다. 충전 후 다시 시도해주세요.";
    statusCode = 402;
    contactAdmin = false;
    return res.status(statusCode).json({ 
      error: errorMessage, 
      reason: 'insufficient_credits_fallback',
      contactAdmin: contactAdmin
    });
  } else if (error.message && error.message.includes("User context lost")) {
    errorMessage = "로그인 정보가 유실되었습니다. 다시 로그인해주세요.";
    statusCode = 401;
    contactAdmin = true;
    console.error('[Critical Error] 사용자 컨텍스트 손실:', error.message);
  } else {
    // 기타 Gemini API 관련 오류
    if (error.name === 'AbortError' || (error.message && error.message.includes('network'))) {
      errorMessage = "네트워크 연결 문제로 운세를 생성할 수 없습니다. 인터넷 연결을 확인하고 다시 시도해주세요.";
      contactAdmin = false;
    } else {
      errorMessage = "운세 생성 중 예기치 않은 오류가 발생했습니다. 잠시 후 다시 시도해주세요. 문제가 계속되면 관리자에게 연락해주세요.";
    }
  }
  
  return res.status(statusCode).json({ 
    error: errorMessage, 
    details: error.message, 
    contactAdmin: contactAdmin,
    adminContact: "<EMAIL>"  // 관리자 연락처 정보 추가
  });
};

module.exports = {
  YEAR_FORTUNE_COST,
  CUSTOM_TAROT_COST_3_CARDS,
  CUSTOM_TAROT_COST_5_CARDS,
  CUSTOM_TAROT_COST_7_CARDS,
  CUSTOM_TAROT_COST_10_CARDS,
  CUSTOM_CONCERN_TAROT_COST,
  COOLDOWN_DURATION_MS,
  NUM_CARDS_TO_SELECT,
  NUM_CARDS_THREE_SPREAD,
  NUM_CARDS_FIVE_SPREAD,
  NUM_CARDS_SEVEN_SPREAD,
  NUM_CARDS_TEN_SPREAD,
  FORTUNE_TYPES,
  getNextSeoul6AM,
  getRemainingCooldownTime,
  getSeoulTimeString,
  logCreditCheck,
  handleError
}; 