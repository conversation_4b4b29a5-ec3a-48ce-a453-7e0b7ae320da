<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>별과 타로 - AI 요정과 함께하는 신비한 타로의 세계</title>
    <link rel="stylesheet" href="style.css">
    <!-- Google Fonts 예시 (실제 사용 시 폰트 라이선스 확인 및 최적화 필요) -->
    <link href="https://fonts.googleapis.com/css2?family=Poor+Story&family=Cute+Font&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <div class="logo">
            <!-- <img src="images/logo_owl.png" alt="별과 타로 로고"> -->
            <h1><a href="#">별과 타로</a></h1>
        </div>
        <nav>
            <ul>
                <li><a href="#">
                    <!-- <img src="images/icons/cc0_speech_bubble.svg" alt="문의" class="icon"> -->
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="icon" fill="currentColor" width="1em" height="1em"><path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/></svg>
                    타로점 보기
                </a></li>
                <li><a href="#">
                    <!-- <img src="images/icons/cc0_star.svg" alt="별" class="icon"> -->
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="icon" fill="currentColor" width="1em" height="1em"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                    오늘/올해의 운세
                </a></li>
                <li><a href="#">
                    <!-- <img src="images/icons/cc0_magic_wand.svg" alt="마법봉" class="icon"> -->
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="icon" fill="currentColor" width="1em" height="1em"><path d="M19.41 4.59L19.41 4.59c-.39-.39-1.02-.39-1.41 0L12 10.17l-2.83-2.83c-.39-.39-1.02-.39-1.41 0l0 0c-.39.39-.39 1.02 0 1.41l2.83 2.83-2.83 2.83c-.39.39-.39 1.02 0 1.41l0 0c.39.39 1.02.39 1.41 0l2.83-2.83 2.83 2.83c.39.39 1.02.39 1.41 0l0 0c.39-.39.39-1.02 0-1.41L13.41 12l4.59-4.59c.39-.38.39-1.02 0-1.41zM22 2l-2.83 2.83L22 7.66V2zm-4.83 2.83L14.34 2h-5.66l2.83 2.83zM5.17 4.83L8 2H2.34l2.83 2.83zM2 22l2.83-2.83L2 16.34V22zm4.83-2.83L9.66 22h5.66l-2.83-2.83zM18.83 19.17L16 22h5.66l-2.83-2.83z"/></svg>
                    고민 해결
                </a></li>
                <li><a href="#">
                    <!-- <img src="images/icons/cc0_book.svg" alt="책" class="icon"> -->
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="icon" fill="currentColor" width="1em" height="1em"><path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/></svg>
                    타로 첫걸음
                </a></li>
            </ul>
        </nav>
        <div class="user-menu">
            <a href="#" class="login-btn">
                <!-- <img src="images/icons/cc0_door.svg" alt="문" class="icon"> -->
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="icon" fill="currentColor" width="1em" height="1em"><path d="M10 12H4V4h6v8zm2-10H2v12h10V2zm10 6h-8v10H2v2h10v-4h8v4h2v-8h-2v-2z"/></svg>
                로그인
            </a>
            <a href="#" class="signup-btn">
                <!-- <img src="images/icons/cc0_sprout.svg" alt="새싹" class="icon"> -->
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="icon" fill="currentColor" width="1em" height="1em"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-8H11v5.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V12h4.5c.83 0 1.5-.67 1.5-1.5S19.33 9 18.5 9H14V4.5c0-.83-.67-1.5-1.5-1.5S11 2.67 11 3.5V9H6.5c-.83 0-1.5.67-1.5 1.5S5.67 12 6.5 12z"/></svg>
                가입하기
            </a>
        </div>
    </header>

    <main>
        <section class="hero">
            <!-- 배경 일러스트는 CSS background-image로 처리 -->
            <!-- <img src="images/hero_mascot.png" alt="AI 타로 마스코트" class="hero-mascot"> -->
            <h2>AI 요정이 알려주는<br>신비한 타로의 세계!!</h2>
            <p>궁금한 모든 것을 귀여운 AI 타로 친구에게 물어보세요! 무엇이든 도와줄게요 😊</p>
            <a href="#" class="cta-button primary-cta">타로점 보러가기 뿅! 🚀</a>
        </section>

        <section class="services">
            <h3 class="section-title">AI 타로 친구들이 준비한 특별한 마법들! ✨</h3>
            <div class="service-cards-container">
                <div class="service-card">
                    <!-- <img src="images/service_ai_consult.png" alt="AI 맞춤 타로 일러스트" class="service-icon"> -->
                    <h4>너만을 위한 AI 비밀상담 🤫</h4>
                    <p>AI 친구가 너의 고민을 듣고 딱 맞는 타로 카드를 골라줄 거야!</p>
                    <a href="#" class="cta-button">상담 시작!</a>
                </div>
                <div class="service-card">
                    <!-- <img src="images/service_today_tarot.png" alt="오늘의 타로 일러스트" class="service-icon"> -->
                    <h4>오늘의 행운 카드 뽑기! 🌟</h4>
                    <p>매일매일 새로운 카드로 오늘의 기분을 점쳐보자!</p>
                    <a href="#" class="cta-button">카드 뽑기!</a>
                </div>
                <div class="service-card">
                    <!-- <img src="images/service_ai_chat.png" alt="AI 타로챗 일러스트" class="service-icon"> -->
                    <h4>AI 친구와 수다 떨기 💬</h4>
                    <p>언제든 AI 타로 친구에게 궁금한 걸 물어보고 위로도 받아봐!</p>
                    <a href="#" class="cta-button">대화하기!</a>
                </div>
            </div>
        </section>

        <section class="ai-masters">
            <h3 class="section-title">너의 이야기를 들어줄 AI 타로 친구들을 소개할게! 💖</h3>
            <div class="masters-container">
                <!-- AI 마스터 카드 예시 (실제로는 여러 개 반복) -->
                <div class="master-card">
                    <!-- <img src="images/master_pinky_rabbit.png" alt="사랑둥이 핑키 토끼" class="master-avatar"> -->
                    <h5>사랑둥이 핑키</h5>
                    <p class="master-intro">너의 연애 고민을 들어줄게! 
                        <!-- <img src="images/icons/cc0_heart.svg" alt="하트" class="icon master-specialty-icon"> -->
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="icon master-specialty-icon" fill="currentColor" width="1em" height="1em"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>
                    </p>
                    <a href="#" class="cta-button small-cta">핑키랑 상담하기</a>
                </div>
                <div class="master-card">
                    <!-- <img src="images/master_wise_owl.png" alt="지혜로운 부엉샘" class="master-avatar"> -->
                    <h5>지혜로운 부엉샘</h5>
                    <p class="master-intro">학업, 진로 걱정은 나에게! 
                        <!-- <img src="images/icons/cc0_owl_simple.svg" alt="부엉이" class="icon master-specialty-icon"> -->
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="icon master-specialty-icon" fill="currentColor" width="1em" height="1em"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 16c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6zm-1-10h2v4h-2zm0 6h2v2h-2zM4.93 4.93l1.41 1.41C7.58 5.12 9.69 4.5 12 4.5s4.42.62 5.66 1.84l1.41-1.41C17.32 3.08 14.78 2 12 2s-5.32 1.08-7.07 2.93zm14.14 14.14l-1.41-1.41C16.42 18.88 14.31 19.5 12 19.5s-4.42-.62-5.66-1.84l-1.41 1.41C6.68 20.92 9.22 22 12 22s5.32-1.08 7.07-2.93z"/></svg>
                    </p>
                    <a href="#" class="cta-button small-cta">부엉샘이랑 상담하기</a>
                </div>
                <!-- // 더 많은 AI 마스터 카드들 -->
            </div>
        </section>

        <section class="daily-preview">
            <h3 class="section-title">매일매일 찾아오는 작은 행운! 🎁</h3>
            <div class="daily-tarot-box">
                <!-- <img src="images/giftbox_closed.png" alt="선물상자" id="giftBoxIcon"> -->
                <p id="dailyCardMessage">"오늘의 카드 열어보기!" 버튼을 눌러봐!</p>
                <!-- 오늘의 카드 영역 (클릭 시 JS로 내용 변경) -->
                <div id="todayCardArea" style="display:none;">
                    <!-- <img src="images/tarot_sun_cute.png" alt="오늘의 타로카드 - 해맑은 해님" id="todayCardImage"> -->
                    <p id="todayCardInterpretation">오늘 하루 해맑은 웃음 가득! ☀️</p>
                </div>
                <button id="openTodayCardBtn" class="cta-button">오늘의 카드 열어보기!</button>
            </div>
        </section>

        <section class="testimonials">
            <h3 class="section-title">AI 타로 친구들과 함께한 이야기들 💌</h3>
            <div class="testimonial-bubbles">
                <div class="bubble">
                    <p>"AI 핑키 덕분에 썸남이랑 잘 됐어요! 고마워요~ 🥰" - 별빛소녀</p>
                </div>
                <div class="bubble">
                    <p>"오늘의 타로 매일 보는데 짧고 귀여워서 좋아요! 👍" - 타로조아</p>
                </div>
                <!-- // 더 많은 후기 말풍선들 -->
            </div>
        </section>
    </main>

    <footer>
        <div class="footer-content">
            <nav class="footer-nav">
                <a href="#">이용약관</a> |
                <a href="#">개인정보처리방침</a> |
                <a href="#">FAQ</a> |
                <a href="#">문의하기</a>
            </nav>
            <p class="copyright">&copy; 2025 별과 타로 All Rights Reserved. ✨</p>
            <div class="social-links">
                <!-- <a href="#"><img src="images/icon_insta_cute.png" alt="인스타그램"></a> -->
                <!-- <a href="#"><img src="images/icon_twitter_cute.png" alt="트위터"></a> -->
            </div>
        </div>
        <!-- <img src="images/footer_character.png" alt="푸터 캐릭터" class="footer-mascot"> -->
    </footer>

    <!-- Login Modal -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('loginModal')">&times;</span>
            <h3>별과 타로 로그인 🔮</h3>
            <form id="loginForm">
                <div class="form-group">
                    <label for="loginEmail">이메일</label>
                    <input type="email" id="loginEmail" name="loginEmail" placeholder="이메일 주소를 입력해주세요" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">비밀번호</label>
                    <input type="password" id="loginPassword" name="loginPassword" placeholder="비밀번호를 입력해주세요" required>
                </div>
                <button type="submit" class="cta-button auth-button">로그인하기</button>
                <div class="social-login">
                    <p>SNS 계정으로 간편하게 로그인하세요!</p>
                    <button type="button" class="social-btn google-btn">
                        <!-- <img src="images/icons/google_logo.svg" alt="Google"> -->
                        <span>구글 계정으로 로그인</span>
                    </button>
                    <button type="button" class="social-btn kakao-btn">
                        <!-- <img src="images/icons/kakao_logo.svg" alt="Kakao"> -->
                        <span>카카오 계정으로 로그인</span>
                    </button>
                </div>
                <p class="switch-form">아직 회원이 아니신가요? <a href="#" onclick="openModal('registerModal'); closeModal('loginModal');">회원가입하기</a></p>
            </form>
        </div>
    </div>

    <!-- Register Modal -->
    <div id="registerModal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('registerModal')">&times;</span>
            <h3>별과 타로 회원가입 ✨</h3>
            <form id="registerForm">
                <div class="form-group">
                    <label for="registerName">닉네임</label>
                    <input type="text" id="registerName" name="registerName" placeholder="사용하실 닉네임을 입력해주세요" required>
                </div>
                <div class="form-group">
                    <label for="registerEmail">이메일</label>
                    <input type="email" id="registerEmail" name="registerEmail" placeholder="사용하실 이메일 주소" required>
                </div>
                <div class="form-group">
                    <label for="registerPassword">비밀번호</label>
                    <input type="password" id="registerPassword" name="registerPassword" placeholder="8자 이상, 영문/숫자/특수문자 포함" required>
                </div>
                <div class="form-group">
                    <label for="registerPasswordConfirm">비밀번호 확인</label>
                    <input type="password" id="registerPasswordConfirm" name="registerPasswordConfirm" placeholder="비밀번호를 다시 한번 입력해주세요" required>
                </div>
                <button type="submit" class="cta-button auth-button">가입 완료!</button>
                <div class="social-login">
                    <p>SNS 계정으로 간편하게 가입하세요!</p>
                    <button type="button" class="social-btn google-btn">
                        <!-- <img src="images/icons/google_logo.svg" alt="Google"> -->
                        <span>구글 계정으로 가입하기</span>
                    </button>
                    <button type="button" class="social-btn kakao-btn">
                        <!-- <img src="images/icons/kakao_logo.svg" alt="Kakao"> -->
                        <span>카카오 계정으로 가입하기</span>
                    </button>
                </div>
                <p class="switch-form">이미 회원이신가요? <a href="#" onclick="openModal('loginModal'); closeModal('registerModal');">로그인하기</a></p>
            </form>
        </div>
    </div>

    <!-- JavaScript 파일 (간단한 인터랙션용) -->
    <script src="script.js"></script>
</body>
</html> 