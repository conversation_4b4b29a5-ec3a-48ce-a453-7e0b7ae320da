const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkHistory() {
  try {
    console.log('=== UserQuestionHistory 데이터 확인 ===');
    
    const history = await prisma.userQuestionHistory.findMany({
      orderBy: { createdAt: 'desc' },
      take: 10
    });
    
    console.log(`총 ${history.length}개의 기록이 있습니다.`);
    
    if (history.length > 0) {
      history.forEach((record, index) => {
        console.log(`\n[${index + 1}] ID: ${record.id}`);
        console.log(`사용자 ID: ${record.userId || '익명'}`);
        console.log(`질문: ${record.question.substring(0, 100)}...`);
        console.log(`질문 타입: ${record.questionType}`);
        console.log(`응답 요약: ${record.responseSummary.substring(0, 100)}...`);
        console.log(`생성일: ${record.createdAt}`);
      });
    } else {
      console.log('저장된 질문 히스토리가 없습니다.');
    }
    
    console.log('\n=== TarotReading 데이터 확인 ===');
    const readings = await prisma.tarotReading.findMany({
      orderBy: { createdAt: 'desc' },
      take: 5
    });
    
    console.log(`총 ${readings.length}개의 리딩 기록이 있습니다.`);
    readings.forEach((reading, index) => {
      console.log(`\n[${index + 1}] ${reading.readingType} - 카드 ${reading.cardCount}장 - 토큰 ${reading.totalTokens}`);
    });
    
  } catch (error) {
    console.error('에러 발생:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkHistory(); 