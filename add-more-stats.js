const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function addMoreTestReadings() {
  try {
    console.log('더 많은 테스트 리딩 데이터 추가 중...');
    
    const readingTypes = ['fortune_today', 'fortune_year', 'tarot_spread', 'fortune_general'];
    const spreadTypes = ['celtic_cross', 'three_card', 'five_card', 'horseshoe', 'relationship'];
    const ipAddresses = ['127.0.0.1', '***********', '********', '**********'];
    
    const readings = [];
    
    // 1000개의 랜덤 리딩 생성
    for (let i = 0; i < 1000; i++) {
      const readingType = readingTypes[Math.floor(Math.random() * readingTypes.length)];
      const cardCount = Math.floor(Math.random() * 10) + 1; // 1-10 카드
      const promptTokens = Math.floor(Math.random() * 200) + 50; // 50-250 토큰
      const completionTokens = Math.floor(Math.random() * 400) + 100; // 100-500 토큰
      
      const reading = {
        readingType,
        cardCount,
        promptTokens,
        completionTokens,
        totalTokens: promptTokens + completionTokens,
        ipAddress: ipAddresses[Math.floor(Math.random() * ipAddresses.length)],
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // 지난 30일 내 랜덤 날짜
      };
      
      // 타로 스프레드인 경우 spreadType 추가
      if (readingType === 'tarot_spread') {
        reading.spreadType = spreadTypes[Math.floor(Math.random() * spreadTypes.length)];
      }
      
      readings.push(reading);
    }
    
    // 배치로 추가 (성능 향상)
    await prisma.tarotReading.createMany({
      data: readings
    });
    
    console.log(`${readings.length}개의 추가 테스트 리딩이 추가되었습니다.`);
    
    // 현재 통계 확인
    const totalReadings = await prisma.tarotReading.count();
    const activeSpreadCount = await prisma.tarotSpread.count({
      where: { isActive: true }
    });
    
    console.log(`총 리딩 수: ${totalReadings}`);
    console.log(`활성 스프레드 수: ${activeSpreadCount}`);
    
    // 타입별 통계
    const readingsByType = await prisma.tarotReading.groupBy({
      by: ['readingType'],
      _count: {
        id: true
      }
    });
    
    console.log('타입별 리딩 수:');
    readingsByType.forEach(type => {
      console.log(`  ${type.readingType}: ${type._count.id}개`);
    });
    
  } catch (error) {
    console.error('테스트 데이터 추가 실패:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addMoreTestReadings(); 