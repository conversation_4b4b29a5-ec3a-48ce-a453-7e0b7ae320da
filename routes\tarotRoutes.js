const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const { GoogleGenAI } = require('@google/genai');
const { logTarotReading, extractTokenUsage, READING_TYPES } = require('./services/readingLogger');
const prisma = new PrismaClient();

// Gemini API 설정 (전역변수로 쉽게 변경 가능)
const GEMINI_MODEL = "gemini-2.0-flash";
const GEMINI_API_TIMEOUT = 60000;

// Initialize Google Gemini AI
const ai = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY });

// Gemini API 호출 함수
async function callGeminiAPI(prompt) {
  try {
    const response = await ai.models.generateContent({
      model: GEMINI_MODEL,
      contents: prompt
    });

    return {
      text: response.text,
      fullResponse: response // 토큰 사용량 추출을 위해 전체 응답 포함
    };
  } catch (error) {
    throw new Error(`Gemini API Error: ${error.message}`);
  }
}

// 프롬프트 생성 API
router.post('/generate-prompt', async (req, res) => {
  try {
    const { 
      spreadName, 
      cardCount, 
      description, 
      layoutDescription, 
      customVariables,
      aiSettings = {}
    } = req.body;

    // AI 설정 기본값
    const settings = {
      style: aiSettings.style || 'traditional',
      tone: aiSettings.tone || 'warm',
      complexity: aiSettings.complexity || 'medium',
      includeExamples: aiSettings.includeExamples || false
    };

    // 프롬프트 생성을 위한 메타 프롬프트
    const metaPrompt = `
당신은 타로 전문가이자 프롬프트 엔지니어입니다. 효과적인 타로 해석 프롬프트를 생성하는 것이 전문 분야입니다.

타로 스프레드를 위한 AI 프롬프트를 생성해주세요.

스프레드 정보:
- 이름: ${spreadName}
- 카드 수: ${cardCount}장
- 설명: ${description || '없음'}
- 레이아웃 설명: ${layoutDescription || '없음'}

AI 설정:
- 스타일: ${settings.style}
- 어조: ${settings.tone}
- 복잡도: ${settings.complexity}
- 예시 포함: ${settings.includeExamples ? '예' : '아니오'}

커스텀 변수: ${customVariables ? JSON.stringify(customVariables) : '없음'}

다음 변수들을 활용할 수 있습니다:
- {userName}: 사용자 이름
- {cardCount}: 카드 수
- {cards}: 선택된 카드 정보
- {userConcern}: 사용자 고민
- {spreadName}: 스프레드 이름
- {description}: 스프레드 설명

요구사항:
1. 한국어로 작성
2. ${settings.tone} 어조 사용
3. ${settings.style} 스타일 적용
4. ${settings.complexity} 수준의 복잡도
${settings.includeExamples ? '5. 구체적인 예시 포함' : ''}

생성할 프롬프트는 타로 리더가 사용자에게 의미 있는 해석을 제공할 수 있도록 도와야 합니다.
`;

    const generatedPrompt = await callGeminiAPI(metaPrompt);

    // 리딩 기록 로깅
    const tokenUsage = extractTokenUsage(generatedPrompt.fullResponse);
    await logTarotReading({
      userId: req.user?.id || null,
      readingType: READING_TYPES.CUSTOM_TAROT,
      spreadType: spreadName,
      cardCount: cardCount,
      promptTokens: tokenUsage.promptTokens,
      completionTokens: tokenUsage.completionTokens,
      totalTokens: tokenUsage.totalTokens,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      generatedPrompt: generatedPrompt.text,
      settings: settings
    });

  } catch (error) {
    console.error('프롬프트 생성 실패:', error);
    res.status(500).json({
      success: false,
      message: '프롬프트 생성 중 오류가 발생했습니다.',
      error: error.message
    });
  }
});

// 프롬프트 개선 API
router.post('/improve-prompt', async (req, res) => {
  try {
    const { 
      currentPrompt,
      spreadName, 
      cardCount, 
      description, 
      customVariables,
      validationIssues = [],
      validationSuggestions = [],
      aiSettings = {}
    } = req.body;

    // AI 설정 기본값
    const settings = {
      style: aiSettings.style || 'traditional',
      tone: aiSettings.tone || 'warm',
      complexity: aiSettings.complexity || 'medium',
      includeExamples: aiSettings.includeExamples || false
    };

    // 프롬프트 개선을 위한 메타 프롬프트
    const metaPrompt = `
당신은 타로 전문가이자 프롬프트 엔지니어입니다. 기존 프롬프트를 분석하고 개선하는 것이 전문 분야입니다.

다음 타로 스프레드 프롬프트를 개선해주세요.

현재 프롬프트:
${currentPrompt}

스프레드 정보:
- 이름: ${spreadName}
- 카드 수: ${cardCount}장
- 설명: ${description || '없음'}

발견된 문제점:
${validationIssues.length > 0 ? validationIssues.map(issue => `- ${issue}`).join('\n') : '없음'}

개선 제안사항:
${validationSuggestions.length > 0 ? validationSuggestions.map(suggestion => `- ${suggestion}`).join('\n') : '없음'}

AI 설정:
- 스타일: ${settings.style}
- 어조: ${settings.tone}
- 복잡도: ${settings.complexity}
- 예시 포함: ${settings.includeExamples ? '예' : '아니오'}

커스텀 변수: ${customVariables ? JSON.stringify(customVariables) : '없음'}

개선 목표:
1. 더 명확하고 구체적인 지시사항
2. ${settings.tone} 어조 강화
3. ${settings.style} 스타일 적용
4. 사용자 경험 향상
5. 타로 해석의 정확성과 깊이 증대
${settings.includeExamples ? '6. 더 나은 예시 제공' : ''}

개선된 프롬프트를 제공해주세요.
`;

    const improvedPrompt = await callGeminiAPI(metaPrompt);

    // 리딩 기록 로깅
    const tokenUsage = extractTokenUsage(improvedPrompt.fullResponse);
    await logTarotReading({
      userId: req.user?.id || null,
      readingType: READING_TYPES.CUSTOM_TAROT,
      spreadType: spreadName,
      cardCount: cardCount,
      promptTokens: tokenUsage.promptTokens,
      completionTokens: tokenUsage.completionTokens,
      totalTokens: tokenUsage.totalTokens,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      improvedPrompt: improvedPrompt.text,
      settings: settings
    });

  } catch (error) {
    console.error('프롬프트 개선 실패:', error);
    res.status(500).json({
      success: false,
      message: '프롬프트 개선 중 오류가 발생했습니다.',
      error: error.message
    });
  }
});

// 프롬프트 테스트 API
router.post('/test-prompt', async (req, res) => {
  try {
    const { 
      promptTemplate,
      systemInstruction,
      testData,
      spreadName,
      cardCount,
      customVariables = {}
    } = req.body;

    // 글로벌 설정 가져오기
    const globalSettings = await prisma.tarotGlobalSettings.findMany();
    const globalSystemInstruction = globalSettings.find(s => s.settingKey === 'global_system_instruction')?.settingValue;

    // 테스트 데이터로 프롬프트 변수 치환
    let testPrompt = promptTemplate;
    
    // 기본 변수 치환
    testPrompt = testPrompt.replace(/{userName}/g, testData.userName || '테스트사용자');
    testPrompt = testPrompt.replace(/{cardCount}/g, testData.selectedCards?.length || cardCount || 3);
    testPrompt = testPrompt.replace(/{userConcern}/g, testData.userConcern || '일반적인 고민');
    testPrompt = testPrompt.replace(/{spreadName}/g, spreadName || '테스트 스프레드');
    testPrompt = testPrompt.replace(/{description}/g, '테스트용 스프레드 설명');
    
    // 카드 정보 생성 (테스트용)
    const cardInfo = testData.selectedCards?.map((card, index) => 
      `${index + 1}번째 카드: ${card}`
    ).join('\n') || '1번째 카드: 마법사\n2번째 카드: 여제\n3번째 카드: 태양';
    
    testPrompt = testPrompt.replace(/{cards}/g, cardInfo);
    
    // 커스텀 변수 치환
    Object.entries(customVariables).forEach(([key, value]) => {
      const regex = new RegExp(`{${key}}`, 'g');
      testPrompt = testPrompt.replace(regex, value);
    });

    // 프롬프트 조합 (우선순위: 글로벌 시스템 명령어 > 개별 시스템 명령어 > 프롬프트)
    let fullPrompt = testPrompt;
    
    if (systemInstruction?.trim()) {
      fullPrompt = `${systemInstruction}\n\n${fullPrompt}`;
    }
    
    if (globalSystemInstruction?.trim()) {
      fullPrompt = `**[중요 글로벌 시스템 지침]**\n${globalSystemInstruction}\n\n${fullPrompt}`;
    }

    const testResult = await callGeminiAPI(fullPrompt);

    res.json({
      success: true,
      testResult: testResult,
      processedPrompt: testPrompt
    });

  } catch (error) {
    console.error('프롬프트 테스트 실패:', error);
    res.status(500).json({
      success: false,
      message: '프롬프트 테스트 중 오류가 발생했습니다.',
      error: error.message
    });
  }
});

// 카드 위치 라벨 생성 API
router.post('/generate-position-labels', async (req, res) => {
  try {
    const { spreadName, cardCount, description, layoutDescription } = req.body;

    const metaPrompt = `
"${spreadName}" 타로 스프레드를 위한 카드 위치 라벨을 생성해주세요.

스프레드 정보:
- 이름: ${spreadName}
- 카드 수: ${cardCount}장
- 설명: ${description || '없음'}
- 레이아웃 설명: ${layoutDescription || '없음'}

요구사항:
1. ${cardCount}개의 라벨을 JSON 배열 형태로 제공
2. 각 라벨은 해당 위치의 의미를 명확히 표현
3. 한국어로 작성
4. 간결하고 직관적인 표현 사용
5. 스프레드의 목적과 일치하는 라벨

예시 형태: ["과거", "현재", "미래"] 또는 ["상황", "장애물", "조언", "결과"]

JSON 배열만 응답해주세요.
`;

    const responseText = await callGeminiAPI(metaPrompt);

    let generatedLabels;
    try {
      generatedLabels = JSON.parse(responseText);
    } catch (parseError) {
      // JSON 파싱 실패 시 기본 라벨 생성
      generatedLabels = Array.from({ length: cardCount }, (_, i) => `위치 ${i + 1}`);
    }

    res.json({
      success: true,
      labels: generatedLabels
    });

  } catch (error) {
    console.error('카드 위치 라벨 생성 실패:', error);
    res.status(500).json({
      success: false,
      message: '카드 위치 라벨 생성 중 오류가 발생했습니다.',
      error: error.message
    });
  }
});

// 글로벌 설정 조회 API
router.get('/global-settings', async (req, res) => {
  try {
    const settings = await prisma.tarotGlobalSettings.findMany();
    
    res.json({
      success: true,
      settings: settings
    });
  } catch (error) {
    console.error('글로벌 설정 조회 실패:', error);
    res.status(500).json({
      success: false,
      message: '글로벌 설정 조회 중 오류가 발생했습니다.',
      error: error.message
    });
  }
});

// 글로벌 설정 저장 API
router.put('/global-settings', async (req, res) => {
  try {
    const { settings } = req.body;
    
    // 기존 설정 삭제 후 새로 추가
    await prisma.tarotGlobalSettings.deleteMany();
    
    // 새 설정들 추가
    const settingPromises = Object.entries(settings).map(([key, value]) =>
      prisma.tarotGlobalSettings.create({
        data: {
          settingKey: key,
          settingValue: value
        }
      })
    );
    
    await Promise.all(settingPromises);
    
    res.json({
      success: true,
      message: '글로벌 설정이 저장되었습니다.'
    });
  } catch (error) {
    console.error('글로벌 설정 저장 실패:', error);
    res.status(500).json({
      success: false,
      message: '글로벌 설정 저장 중 오류가 발생했습니다.',
      error: error.message
    });
  }
});

// 커스텀 변수 생성 API
router.post('/generate-custom-variables', async (req, res) => {
  try {
    const { spreadName, description, style, tone } = req.body;

    const metaPrompt = `
"${spreadName}" 타로 스프레드를 위한 커스텀 변수를 생성해주세요.

스프레드 정보:
- 이름: ${spreadName}
- 설명: ${description || '없음'}
- 원하는 스타일: ${style || '전통적'}
- 원하는 어조: ${tone || '따뜻한'}

요구사항:
1. 프롬프트에서 활용할 수 있는 유용한 변수들을 JSON 객체로 제공
2. 변수명은 영어, 값은 한국어로 작성
3. 스프레드의 특성과 목적에 맞는 변수들
4. 해석의 품질을 높일 수 있는 변수들

예시 형태: 
{
  "mood": "신비로운",
  "style": "공감적인", 
  "approach": "직관적인",
  "focus": "내면의 성장"
}

JSON 객체만 응답해주세요.
`;

    const responseText = await callGeminiAPI(metaPrompt);

    let generatedVariables;
    try {
      generatedVariables = JSON.parse(responseText);
    } catch (parseError) {
      // JSON 파싱 실패 시 기본 변수 생성
      generatedVariables = {
        "mood": "신비로운",
        "style": "따뜻한",
        "approach": "직관적인"
      };
    }

    res.json({
      success: true,
      variables: generatedVariables
    });

  } catch (error) {
    console.error('커스텀 변수 생성 실패:', error);
    res.status(500).json({
      success: false,
      message: '커스텀 변수 생성 중 오류가 발생했습니다.',
      error: error.message
    });
  }
});

module.exports = router; 