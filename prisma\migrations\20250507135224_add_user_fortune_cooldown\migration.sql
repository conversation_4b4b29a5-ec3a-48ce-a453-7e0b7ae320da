-- CreateTable
CREATE TABLE "UserFortuneCooldown" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "fortuneType" TEXT NOT NULL,
    "cooldownExpiresAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserFortuneCooldown_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "UserFortuneCooldown_userId_idx" ON "UserFortuneCooldown"("userId");

-- CreateIndex
CREATE INDEX "UserFortuneCooldown_fortuneType_idx" ON "UserFortuneCooldown"("fortuneType");

-- CreateIndex
CREATE UNIQUE INDEX "UserFortuneCooldown_userId_fortuneType_key" ON "UserFortuneCooldown"("userId", "fortuneType");

-- AddF<PERSON>ignKey
ALTER TABLE "UserFortuneCooldown" ADD CONSTRAINT "UserFortuneCooldown_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
