import { useState } from 'react';
import { TarotCard } from '../data/tarotCardsData';

// 운세 타입 정의
export type FortuneType = 
  | 'today' 
  | 'year' 
  | 'custom'
  | 'customTarotReading'
  | 'customThreeCard' 
  | 'customFourCard'
  | 'customFiveCard' 
  | 'customSevenCard' 
  | 'customTenCard' 
  | 'customDoubleLine'
  | 'customCelticCross'
  | 'customCrossSpread'
  | 'customPyramid'
  | 'customHorseshoe'
  | 'customAwen'
  | 'customDragonRaziel'
  | 'customBinaryChoice'
  | 'customRelationship'
  | 'customCupOfRelationship'
  | 'customYinYang'
  | 'customReadingMind'
  | 'customHoroscope'
  | null;

// 뷰 상태 정의
export type View = 
  | 'concernInput'
  | 'typeSelection' 
  | 'nameInput'
  | 'meditationIntro'
  | 'cardCountSelection'
  | 'cardDrawing'
  | 'cardRevealAndInterpretation'
  | 'finalInterpretation'
  | 'resultEnd'
  | 'cooldownMessage' 
  | 'loginRequiredMessage' 
  | 'creditsRequiredMessage';

// 채팅 메시지 인터페이스
export interface ChatMessage {
  id: string;
  sender: 'ai' | 'system' | 'user';
  text: string;
  card?: TarotCard;
  timestamp: number;
}

// 매니저 스프레드 위치 인터페이스
export interface ManagerSpreadPosition {
  id: string;
  left: string;
  top: string;
  transform?: string;
}

export const useTarotState = () => {
  // 기본 상태들
  const [selectedFortuneType, setSelectedFortuneType] = useState<FortuneType>(null);
  const [userName, setUserName] = useState<string>('');
  const [userConcern, setUserConcern] = useState<string>('');
  const [currentView, setCurrentView] = useState<View>('concernInput');
  
  // 카드 관련 상태들
  const [selectedCardCountForCustom, setSelectedCardCountForCustom] = useState<number | null>(null);
  const [currentNumCardsToSelect, setCurrentNumCardsToSelect] = useState<number>(3);
  const [currentFortuneCost, setCurrentFortuneCost] = useState<number>(0);
  const [cardsForSelection, setCardsForSelection] = useState<TarotCard[]>([]);
  const [selectedCards, setSelectedCards] = useState<TarotCard[]>([]);
  const [revealedCardsInfo, setRevealedCardsInfo] = useState<Array<{card: TarotCard, interpretation: string | null}>>([]);
  
  // 진행 상태들
  const [currentCardSelectionStep, setCurrentCardSelectionStep] = useState(0);
  const [currentCardRevealStep, setCurrentCardRevealStep] = useState(0);
  const [finalInterpretationText, setFinalInterpretationText] = useState<string | null>(null);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [fortuneInterpretation, setFortuneInterpretation] = useState<string | null>(null);
  const [isLoadingFortune, setIsLoadingFortune] = useState<boolean>(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [cooldownMessageDetail, setCooldownMessageDetail] = useState<string>('');
  
  // 애니메이션 관련 상태들
  const [isEnteringSpace, setIsEnteringSpace] = useState<boolean>(false);
  const [sceneTransition, setSceneTransition] = useState(false);
  const [autoRevealActive, setAutoRevealActive] = useState(false);
  const [currentRevealingCard, setCurrentRevealingCard] = useState<number | null>(null);
  const [cardFlipStates, setCardFlipStates] = useState<boolean[]>([]);
  const [showTransitionOverlay, setShowTransitionOverlay] = useState(false);
  const [contentOpacity, setContentOpacity] = useState(1);
  
  // 명상 및 UI 상태들
  const [showMeditationButton, setShowMeditationButton] = useState<boolean>(false);
  const [meditationTextIndex, setMeditationTextIndex] = useState(0);
  const [meditationTexts, setMeditationTexts] = useState<string[]>([]);
  const [showCardSelectionButton, setShowCardSelectionButton] = useState(false);
  
  // 카드 소개 관련 상태들
  const [isIntroducingCard, setIsIntroducingCard] = useState<boolean>(false);
  const [currentCardIntroText, setCurrentCardIntroText] = useState<string>("");
  
  // 카드 부채꼴 드래그/스와이프 관련 상태들
  const [fanHorizontalOffset, setFanHorizontalOffset] = useState<number>(0);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragStartX, setDragStartX] = useState<number>(0);
  
  // 카드 상세 정보 모달 관련 상태들
  const [showCardDetailModal, setShowCardDetailModal] = useState<boolean>(false);
  const [modalCardDetails, setModalCardDetails] = useState<TarotCard | null>(null);
  
  // 사용자 크레딧 상태
  const [userCredits, setUserCredits] = useState<number | null>(null);
  
  // 카드 Z-Index 관리 상태들
  const [cardZIndices, setCardZIndices] = useState<number[]>([]);
  const [highlightedCardIndex, setHighlightedCardIndex] = useState<number | null>(null);
  
  // 카드 배치 및 애니메이션 관련 상태들
  const [cardsInitializing, setCardsInitializing] = useState<boolean>(true);
  const [fanAnimationComplete, setFanAnimationComplete] = useState<boolean>(false);
  const [showFinalInterpretationLoading, setShowFinalInterpretationLoading] = useState<boolean>(false);
  
  // 선택된 스프레드 관련 상태들
  const [selectedSpreadId, setSelectedSpreadId] = useState<string | null>(null);
  const [apiSpreads, setApiSpreads] = useState<any[]>([]);
  const [isLoadingSpreads, setIsLoadingSpreads] = useState<boolean>(true);
  
  // 카드 애니메이션 관련 상태들
  const [cardsInStartPosition, setCardsInStartPosition] = useState<boolean>(true);
  const [cardsMovingToPosition, setCardsMovingToPosition] = useState<boolean>(false);
  const [currentMovingCardIndex, setCurrentMovingCardIndex] = useState<number | null>(null);
  const [celticCrossSecondCardRotating, setCelticCrossSecondCardRotating] = useState(false);
  
  // 매니저 스프레드 위치 데이터
  const [managerSpreadPositions, setManagerSpreadPositions] = useState<{[key: string]: ManagerSpreadPosition[]}>({});

  // 상태 초기화 함수
  const resetTarotStates = () => {
    setCardsForSelection([]);
    setSelectedCards([]);
    setRevealedCardsInfo([]);
    setCurrentCardSelectionStep(0); 
    setCurrentCardRevealStep(0);
    setFinalInterpretationText(null);
    setChatMessages([]);
    setFortuneInterpretation(null); 
    setApiError(null);
    setCooldownMessageDetail('');
    setCurrentRevealingCard(null);
    setCardFlipStates([]);
    setIsIntroducingCard(false);
    setCurrentCardIntroText('');
    setAutoRevealActive(false);
    setUserConcern('');
  };

  // 채팅 메시지 추가 함수
  const addChatMessage = (text: string, sender: 'ai' | 'system' | 'user', card?: TarotCard) => {
    const newMessage: ChatMessage = {
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      sender,
      text,
      card,
      timestamp: Date.now(),
    };
    setChatMessages(prev => [...prev, newMessage]);
  };

  return {
    // 기본 상태들
    selectedFortuneType,
    setSelectedFortuneType,
    userName,
    setUserName,
    userConcern,
    setUserConcern,
    currentView,
    setCurrentView,
    
    // 카드 관련 상태들
    selectedCardCountForCustom,
    setSelectedCardCountForCustom,
    currentNumCardsToSelect,
    setCurrentNumCardsToSelect,
    currentFortuneCost,
    setCurrentFortuneCost,
    cardsForSelection,
    setCardsForSelection,
    selectedCards,
    setSelectedCards,
    revealedCardsInfo,
    setRevealedCardsInfo,
    
    // 진행 상태들
    currentCardSelectionStep,
    setCurrentCardSelectionStep,
    currentCardRevealStep,
    setCurrentCardRevealStep,
    finalInterpretationText,
    setFinalInterpretationText,
    chatMessages,
    setChatMessages,
    fortuneInterpretation,
    setFortuneInterpretation,
    isLoadingFortune,
    setIsLoadingFortune,
    apiError,
    setApiError,
    cooldownMessageDetail,
    setCooldownMessageDetail,
    
    // 애니메이션 관련 상태들
    isEnteringSpace,
    setIsEnteringSpace,
    sceneTransition,
    setSceneTransition,
    autoRevealActive,
    setAutoRevealActive,
    currentRevealingCard,
    setCurrentRevealingCard,
    cardFlipStates,
    setCardFlipStates,
    showTransitionOverlay,
    setShowTransitionOverlay,
    contentOpacity,
    setContentOpacity,
    
    // 명상 및 UI 상태들
    showMeditationButton,
    setShowMeditationButton,
    meditationTextIndex,
    setMeditationTextIndex,
    meditationTexts,
    setMeditationTexts,
    showCardSelectionButton,
    setShowCardSelectionButton,
    
    // 카드 소개 관련 상태들
    isIntroducingCard,
    setIsIntroducingCard,
    currentCardIntroText,
    setCurrentCardIntroText,
    
    // 카드 부채꼴 드래그/스와이프 관련 상태들
    fanHorizontalOffset,
    setFanHorizontalOffset,
    isDragging,
    setIsDragging,
    dragStartX,
    setDragStartX,
    
    // 카드 상세 정보 모달 관련 상태들
    showCardDetailModal,
    setShowCardDetailModal,
    modalCardDetails,
    setModalCardDetails,
    
    // 사용자 크레딧 상태
    userCredits,
    setUserCredits,
    
    // 카드 Z-Index 관리 상태들
    cardZIndices,
    setCardZIndices,
    highlightedCardIndex,
    setHighlightedCardIndex,
    
    // 카드 배치 및 애니메이션 관련 상태들
    cardsInitializing,
    setCardsInitializing,
    fanAnimationComplete,
    setFanAnimationComplete,
    showFinalInterpretationLoading,
    setShowFinalInterpretationLoading,
    
    // 선택된 스프레드 관련 상태들
    selectedSpreadId,
    setSelectedSpreadId,
    apiSpreads,
    setApiSpreads,
    isLoadingSpreads,
    setIsLoadingSpreads,
    
    // 카드 애니메이션 관련 상태들
    cardsInStartPosition,
    setCardsInStartPosition,
    cardsMovingToPosition,
    setCardsMovingToPosition,
    currentMovingCardIndex,
    setCurrentMovingCardIndex,
    celticCrossSecondCardRotating,
    setCelticCrossSecondCardRotating,
    
    // 매니저 스프레드 위치 데이터
    managerSpreadPositions,
    setManagerSpreadPositions,
    
    // 유틸리티 함수들
    resetTarotStates,
    addChatMessage,
  };
};
