require('dotenv').config(); // .env 파일의 환경변수를 process.env로 로드
const express = require('express');
const { PrismaClient } = require('@prisma/client');
const cors = require('cors');

const prisma = new PrismaClient();
const app = express();
const PORT = process.env.PORT || 3000; // 환경변수 PORT가 없으면 3000번 사용

app.set('trust proxy', true); // 프록시 환경에서 정확한 IP 주소를 얻기 위함

// 미들웨어 설정
app.use(cors()); // 모든 도메인에서의 요청 허용 (개발 시)
app.use(express.json()); // JSON 요청 본문 파싱
app.use(express.urlencoded({ extended: true })); // URL-encoded 요청 본문 파싱

// 모든 요청 로깅 미들웨어 (디버깅용)
app.use((req, res, next) => {
  console.log(`[Request Log / IP: ${req.ip}] ${req.method} ${req.originalUrl}`);
  next();
});

// 인증 라우트
const authRoutes = require('./routes/authRoutes');
app.use('/auth', authRoutes);

// 운세 라우트 추가
const fortuneRoutes = require('./routes/fortuneRoutes');
app.use('/api/fortune', fortuneRoutes);

// 타로 스프레드 관리 라우트 추가
const tarotSpreadsRoutes = require('./routes/tarotSpreads');
app.use('/api/tarot', tarotSpreadsRoutes);

// 타로 AI 기능 라우트 추가
const tarotRoutes = require('./routes/tarotRoutes');
app.use('/api/tarot', tarotRoutes);

// 통계 라우트 추가
const statsRoutes = require('./routes/statsRoutes');
app.use('/api/stats', statsRoutes);

// 기본 라우트 (테스트용)
app.get('/', (req, res) => {
  res.send('별과 타로 API 서버입니다! 🔮✨');
});

// TODO: 다른 라우트들 (타로점 보기, 크레딧 사용 등)

// 서버 시작
app.listen(PORT, () => {
  console.log(`✨ 별과 타로 서버가 http://***************:${PORT} 에서 실행 중입니다.`);
});

// Prisma Client 연결 해제 (애플리케이션 종료 시)
process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit();
}); 