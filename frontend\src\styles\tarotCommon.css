/* 공통 타로 애니메이션 및 스타일 정의 */

/* 기본 애니메이션 정의 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes floatIn {
  0% { transform: translateY(20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes flipCard {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(90deg); }
  100% { transform: rotateY(180deg); }
}

@keyframes glowPulse {
  0% { box-shadow: 0 0 5px #ffeb3b, 0 0 10px #ffeb3b; }
  50% { box-shadow: 0 0 20px #ffeb3b, 0 0 30px #ffeb3b; }
  100% { box-shadow: 0 0 5px #ffeb3b, 0 0 10px #ffeb3b; }
}

@keyframes mysticalShine {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulseAndSpin {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: rotate(180deg) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: rotate(360deg) scale(1);
    opacity: 0.7;
  }
}

/* 천체 오브 애니메이션 */
@keyframes celestialPulse {
  0% { transform: scale(1); box-shadow: 0 0 15px rgba(255,255,255,0.2), 0 0 30px rgba(255,255,255,0.1); }
  50% { transform: scale(1.03); box-shadow: 0 0 25px rgba(255,255,255,0.3), 0 0 45px rgba(255,255,255,0.2); }
  100% { transform: scale(1); box-shadow: 0 0 15px rgba(255,255,255,0.2), 0 0 30px rgba(255,255,255,0.1); }
}

@keyframes starShimmer {
  0%, 100% { opacity: 0.7; transform: scale(0.95); }
  50% { opacity: 1; transform: scale(1.05); }
}

/* 장면 전환 스타일 */
.scene-transition {
  animation: fadeIn 1s ease-out;
}

/* 카드 관련 스타일 */
.card-reveal {
  perspective: 1000px;
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

.card-flipped .card-inner {
  transform: rotateY(180deg);
}

.card-front, .card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.card-front {
  transform: rotateY(180deg);
}

/* 신비로운 버튼 스타일 */
.mystical-button {
  background: linear-gradient(90deg, #4a148c, #7b1fa2, #4a148c);
  background-size: 200% 200%;
  animation: mysticalShine 3s ease infinite;
  color: white;
  border: none;
  margin-top: 15px;
  padding: 16px 24px;
  border-radius: 30px;
  font-size: 1.2rem;
  font-weight: bold;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(123, 31, 162, 0.5);
  transition: all 0.3s ease;
}

.mystical-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(123, 31, 162, 0.7);
}

.mystical-button:active {
  transform: translateY(1px);
}

/* 해석 패널 스타일 */
.interpretation-panel {
  background: rgba(20, 20, 40, 0.85);
  backdrop-filter: blur(12px);
  border-radius: 20px;
  padding: 25px 30px;
  border: 1px solid rgba(255, 235, 59, 0.2);
  animation: fadeIn 0.5s ease-out, floatIn 0.5s ease-out;
  box-shadow: 
    0 0 15px rgba(255, 235, 59, 0.1),
    inset 0 1px 1px rgba(255, 255, 255, 0.05),
    inset 0 -1px 1px rgba(0, 0, 0, 0.1);
}

/* 최종 해석 화면의 interpretation-panel에 대한 추가 스타일 */
.final-interpretation-container .interpretation-panel {
  padding: 35px 40px;
  background: rgba(15, 15, 30, 0.9);
  box-shadow: 
    0 0 25px rgba(255, 235, 59, 0.15),
    0 10px 30px rgba(0,0,0,0.3),
    inset 0 2px 3px rgba(255, 255, 255, 0.07), 
    inset 0 -2px 3px rgba(0, 0, 0, 0.15); 
}

.result-card-interpretation p, .result-card-interpretation {
  text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

/* 미니 카드 툴팁 스타일 */
.mini-card-tooltip {
  position: absolute;
  top: -180px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(20, 20, 40, 0.9);
  backdrop-filter: blur(10px);
  padding: 14px;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  min-width: 200px;
  z-index: 100;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.mini-card-container {
  position: relative;
}

.mini-card-container:hover .mini-card-tooltip {
  opacity: 1;
  visibility: visible;
}

.mini-card-container:hover img {
  transform: scale(1.5);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

/* 트랜지션 오버레이 스타일 */
.transition-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: black;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.8s ease-in-out;
  visibility: hidden;
}

.transition-overlay.active {
  opacity: 1;
  pointer-events: all;
  visibility: visible;
}

.background-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: black;
  z-index: 9990;
  pointer-events: auto;
}

.fortune-content {
  position: relative;
  z-index: 9995;
  transition: opacity 0.8s ease-in-out;
}

/* 스크롤바 스타일링 */
.interpretation-panel::-webkit-scrollbar {
  width: 8px;
}
.interpretation-panel::-webkit-scrollbar-track {
  background: rgba(30, 30, 60, 0.5);
  border-radius: 4px;
}
.interpretation-panel::-webkit-scrollbar-thumb {
  background-color: #ffeb3b;
  border-radius: 4px;
  border: 2px solid rgba(30, 30, 60, 0.5);
}
.interpretation-panel::-webkit-scrollbar-thumb:hover {
  background-color: #fff59d;
}

/* 하이라이트 텍스트 스타일 */
.highlighted-text {
  background-color: rgba(255, 235, 59, 0.15);
  padding: 0.1em 0.3em;
  border-radius: 4px;
  font-weight: 600;
  color: #fff59d;
}

/* 카드 이름 인터랙티브 스타일 */
.card-name-interactive {
  color: #a8d8ff;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s ease-in-out;
}
.card-name-interactive:hover {
  color: #e0f2ff;
  text-decoration: underline;
}

/* 카드 상세 모달 스타일 */
.card-detail-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease-out, visibility 0.3s ease-out;
}
.card-detail-modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.card-detail-modal-content {
  background: rgba(30, 35, 50, 0.9);
  padding: 30px;
  border-radius: 15px;
  border: 1px solid rgba(168, 216, 255, 0.3);
  box-shadow: 0 10px 30px rgba(0,0,0,0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  max-width: 90vw;
  width: auto;
  max-height: 80vh;
  animation: floatIn 0.4s ease-out;
}

.card-detail-modal-image {
  width: 150px;
  height: auto;
  border-radius: 8px;
  border: 2px solid #a8d8ff;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.card-detail-modal-info {
  text-align: left;
  color: #e0e0e0;
  max-width: 400px;
  overflow-y: auto;
  max-height: calc(80vh - 200px);
}
.card-detail-modal-info h4 {
  color: #a8d8ff;
  font-size: 1.5rem;
  margin-bottom: 10px;
}
.card-detail-modal-info p {
  font-size: 0.95rem;
  line-height: 1.6;
}

.card-detail-modal-close-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  color: #a8d8ff;
  font-size: 2rem;
  cursor: pointer;
  line-height: 1;
}
.card-detail-modal-close-btn:hover {
  color: #fff;
}

/* 천체 선택 헤더 스타일 */
.celestial-selection-header {
  text-align: center;
  margin-bottom: 60px;
  animation: fadeIn 1s ease-out 0.2s backwards;
}

.celestial-title {
  font-size: 3rem;
  font-family: 'Cute Font', 'Poor Story', cursive;
  font-weight: normal;
  color: #fff59d;
  letter-spacing: 1.5px;
  margin-bottom: 15px;
  text-shadow: 0 0 15px rgba(255, 245, 157, 0.5), 0 0 5px rgba(255, 245, 157, 0.3);
}

.celestial-prompt {
  font-size: 1.4rem;
  font-family: 'Poor Story', cursive;
  color: #e1e6ff;
  font-weight: 300;
  letter-spacing: 0.8px;
}

/* 운명 선택 컨테이너 */
.destiny-choices-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 40px;
  padding: 28px;
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

/* 운명 오브 스타일 */
.destiny-orb {
  position: relative;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #ffffff;
  cursor: pointer;
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), box-shadow 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  padding: 20px;
  overflow: hidden;
  animation: floatIn 0.8s ease-out 0.5s backwards;
}

.destiny-orb::before {
  content: '';
  position: absolute;
  top: 5%;
  left: 10%;
  width: 80%;
  height: 80%;
  border-radius: 50%;
  background: radial-gradient(circle at 50% 30%, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0) 60%);
  opacity: 0.8;
  transition: opacity 0.4s ease;
}

.destiny-orb:hover::before {
  opacity: 1;
}

/* 오늘 오브 스타일 */
.today-orb {
  background: radial-gradient(circle, #ffd54f 0%, #ff8f00 100%);
  box-shadow: 0 0 20px #ffc107, 0 0 40px #ff9800, inset 0 0 15px #fff3e0;
  animation: celestialPulse 4s infinite ease-in-out;
}
.today-orb:hover {
  transform: scale(1.05);
  box-shadow: 0 0 30px #ffc107, 0 0 60px #ff9800, 0 0 10px #ffffff, inset 0 0 20px #fff3e0;
}

/* 연간 오브 스타일 */
.year-orb {
  background: radial-gradient(circle, #64b5f6 0%, #1a237e 100%);
  box-shadow: 0 0 20px #2196f3, 0 0 40px #303f9f, inset 0 0 15px #e3f2fd;
  animation: celestialPulse 4.5s infinite ease-in-out 0.5s;
}
.year-orb:hover {
  transform: scale(1.05);
  box-shadow: 0 0 30px #2196f3, 0 0 60px #303f9f, 0 0 10px #ffffff, inset 0 0 20px #e3f2fd;
}

/* 오브 아이콘 컨테이너 */
.orb-icon-container {
  margin-bottom: 15px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.orb-icon {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}
.destiny-orb:hover .orb-icon {
  transform: scale(1.1) rotate(5deg);
}

/* 오브 텍스트 스타일 */
.orb-title {
  font-size: 1.9rem;
  font-family: 'Cute Font', 'Poor Story', cursive;
  font-weight: normal;
  margin-bottom: 8px;
  text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}

.orb-description {
  font-size: 1rem;
  font-family: 'Poor Story', cursive;
  line-height: 1.5;
  margin-bottom: 12px;
  max-width: 80%;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.orb-subtext {
  font-size: 0.8rem;
  font-family: 'Poor Story', cursive;
  color: rgba(255, 255, 255, 0.8);
  font-style: italic;
}

/* 연간 오브 별 효과 */
.year-orb::after {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background-image:
    radial-gradient(2px 2px at 20% 30%, white, transparent),
    radial-gradient(2px 2px at 80% 70%, white, transparent),
    radial-gradient(1px 1px at 50% 50%, white, transparent),
    radial-gradient(1px 1px at 30% 80%, white, transparent),
    radial-gradient(1px 1px at 70% 20%, white, transparent);
  background-repeat: no-repeat;
  animation: starShimmer 5s infinite alternate;
  border-radius: 50%;
  pointer-events: none;
}

/* 오늘 오브 텍스트 색상 조정 */
.today-orb .orb-subtext {
  color: #7a5c0f;
  text-shadow: 0px 0px 2px rgba(255, 248, 225, 0.6);
}

.today-orb .orb-title {
  color: #3E2723;
  text-shadow: 0px 1px 1px rgba(255, 224, 130, 0.7);
}

.today-orb .orb-description {
  color: #4E342E;
  text-shadow: 0px 1px 1px rgba(255, 224, 130, 0.6);
}

.today-orb .orb-subtext {
  color: #5D4037;
  text-shadow: 0px 1px 1px rgba(255, 224, 130, 0.5);
}

/* 연간 오브 아이콘 색상 */
.year-orb .orb-icon path {
  fill: #e3f2fd;
}

/* 카드 애니메이션 스타일 */
.card-reveal {
  perspective: 1000px;
  transition: all 0.5s ease-in-out;
  box-shadow: 0 4px 15px rgba(0,0,0,0.3);
  border-radius: 10px;
}

/* 카드 덱 시작 위치 스타일 */
.card-deck-position {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  transform-origin: bottom center;
  box-shadow: 0 4px 15px rgba(0,0,0,0.3);
  border-radius: 10px;
  transition: none;
}

/* 카드가 이동 중일 때 적용하는 트랜지션 */
.card-moving {
  transition: all 1.8s cubic-bezier(0.19, 1, 0.22, 1) !important;
}

/* 켈틱 크로스 교차 카드 스타일 */
.celtic-cross-crossing {
  transition: transform 0.6s ease-out !important;
}

.celtic-cross-crossing .card-inner {
  transform-origin: center center;
}

/* 뒤집히기 전 가로 방향 스타일 */
.celtic-cross-crossing:not(.card-flipped) {
  transform: rotate(90deg) !important;
}

/* 뒤집히는 효과 - 기존 세로에서 뒤집힘 */
.celtic-cross-crossing.card-flipped .card-inner {
  transform: rotateY(180deg);
}

/* 하이라이트 현재 카드 효과 */
.card-reveal.highlight-effects {
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.7);
}

/* 반응형 레이아웃 조정 */
@media (max-width: 768px) {
  .spread-layout-area {
    height: 600px !important;
  }
}

@media (max-width: 576px) {
  .spread-layout-area {
    height: 500px !important;
  }
}

/* 신비로운 피드백 포털 스타일 */
.mystical-feedback-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px;
  width: 100%;
  max-width: 600px;
  margin: auto;
  animation: fadeIn 0.7s ease-out;
}

.mystical-feedback-portal {
  background: rgba(20, 10, 35, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px 30px;
  border: 1px solid rgba(170, 120, 255, 0.5);
  box-shadow: 0 0 25px rgba(170, 120, 255, 0.3), 0 0 10px rgba(220, 180, 255, 0.2) inset;
  width: 100%;
}

.feedback-icon-area {
  margin-bottom: 25px;
}

.feedback-icon-area svg {
  width: 70px;
  height: 70px;
  fill: rgba(220, 180, 255, 0.9);
  filter: drop-shadow(0 0 10px rgba(220, 180, 255, 0.5));
}

.feedback-icon-area img {
  width: 70px;
  height: 70px;
  object-fit: contain;
  filter: drop-shadow(0 0 8px rgba(255, 223, 117, 0.7));
}

.feedback-portal-title {
  font-family: 'Cute Font', 'Poor Story', cursive;
  font-size: 2.5rem;
  color: #e6ceff;
  text-shadow: 0 0 10px rgba(220, 180, 255, 0.4);
  margin-bottom: 15px;
}

.feedback-portal-message {
  font-family: 'Poor Story', cursive;
  font-size: 1.25rem;
  color: #d1c4e9;
  line-height: 1.7;
  margin-bottom: 30px;
  white-space: pre-line;
}

.feedback-portal-button {
  padding: 14px 30px;
  font-size: 1.1rem;
  background: linear-gradient(90deg, #5e35b1, #8e24aa, #5e35b1);
}

/* 포털 애니메이션 */
@keyframes fadeInPortalSmooth {
  from { opacity: 0; transform: translateY(15px) scale(0.98); }
  to { opacity: 1; transform: translateY(0) scale(1); }
}
.mystical-feedback-portal {
  animation: fadeInPortalSmooth 0.6s ease-out;
}
