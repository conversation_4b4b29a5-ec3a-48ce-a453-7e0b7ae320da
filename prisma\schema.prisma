// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL") // 환경 변수에서 데이터베이스 URL을 가져옵니다.
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id              String    @id @default(cuid())
  email           String    @unique
  hashedPassword  String?   // 이메일/비밀번호 가입 시 사용 (암호화된 비밀번호)
  name            String?   // 선택적 사용자 이름
  credits         Int       @default(0) // 타로 크레딧
  lastCreditDeduction DateTime? // Track the last time credits were deducted to prevent duplicates
  
  // 소셜 로그인 정보
  googleId        String?   @unique
  kakaoId         String?   @unique

  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // 관계 설정
  cooldowns       UserFortuneCooldown[] // Relation to UserFortuneCooldown model
  tarotReadings   TarotReading[] // 타로 리딩 기록 관계
  questionHistory UserQuestionHistory[] // 질문 이력 관계
}

model UserFortuneCooldown {
  id                String    @id @default(cuid())
  userId            String
  user              User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  fortuneType       String    // e.g., "today", "year_general", "year_love"
  cooldownExpiresAt DateTime

  createdAt         DateTime  @default(now())

  @@unique([userId, fortuneType]) // 특정 사용자는 각 운세 종류별로 하나의 쿨타임만 가짐
  @@index([userId])
  @@index([fortuneType])
}

model IPFortuneCooldown {
  id                String    @id @default(cuid())
  ipAddress         String    // Store the IP address
  fortuneType       String    // e.g., "today"
  cooldownExpiresAt DateTime

  createdAt         DateTime  @default(now())

  @@unique([ipAddress, fortuneType]) // Unique cooldown per IP and fortune type
  @@index([ipAddress])
}

model TarotSpread {
  id                String    @id @default(cuid())
  name              String    // 스프레드 이름 (예: "3카드", "켈틱 크로스")
  description       String    // 스프레드 설명
  cardCount         Int       // 카드 수
  cost              Int       // 크레딧 비용
  iconLayout        String    @default("") // 아이콘 레이아웃 (향후 확장용)
  spreadType        String    @unique // 스프레드 타입 (고유)
  layoutDescription String    @default("") // 레이아웃 설명
  className         String    @default("") // CSS 클래스명
  positions         String    @default("[]") // JSON 형태의 카드 위치 정보
  discount          Int       @default(0) // 할인률 (0-100)
  discountStartDate DateTime? // 할인 시작일 (옵션)
  discountEndDate   DateTime? // 할인 종료일 (옵션)
  isActive          Boolean   @default(true) // 활성화 여부
  order             Int       @default(0) // 표시 순서 (낮을수록 먼저 표시)
  
  // 프롬프트 템플릿 관련 필드
  promptTemplate    String    @default("") // 기본 프롬프트 템플릿
  systemInstruction String    @default("") // 시스템 명령어/가이드라인 (개별 스프레드용, 글로벌 설정으로 이동 예정)
  cardPositionLabels String   @default("[]") // JSON 형태의 카드 위치별 라벨
  customVariables   String    @default("{}") // JSON 형태의 커스텀 변수
  
  // 개별 카드 소개 텍스트 템플릿 (새로 추가)
  cardIntroTemplates String   @default("[]") // JSON 형태의 카드별 소개 텍스트 배열
  
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  @@index([isActive])
  @@index([cardCount])
  @@index([cost])
  @@index([order])
}

// 글로벌 타로 설정 모델 (새로 추가)
model TarotGlobalSettings {
  id                String    @id @default(cuid())
  settingKey        String    @unique // 설정 키 (예: "global_system_instruction")
  settingValue      String    // 설정 값
  description       String    @default("") // 설정 설명
  isActive          Boolean   @default(true) // 활성화 여부
  
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  @@index([settingKey])
  @@index([isActive])
}

// 타로 리딩 기록 모델 - 통계 및 사용량 추적용
model TarotReading {
  id                String    @id @default(cuid())
  userId            String?   // 사용자 ID (익명 사용자의 경우 null 가능)
  user              User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  
  // 리딩 정보
  readingType       String    // 리딩 타입 (예: "tarot_spread", "fortune_today", "fortune_year")
  spreadType        String?   // 스프레드 타입 (타로 스프레드의 경우)
  cardCount         Int       @default(0) // 사용된 카드 수
  
  // AI 사용량 추적
  promptTokens      Int       @default(0) // 프롬프트 토큰 수
  completionTokens  Int       @default(0) // 완성 토큰 수
  totalTokens       Int       @default(0) // 총 토큰 수
  
  // 메타데이터
  ipAddress         String?   // 사용자 IP 주소 (선택적)
  userAgent         String?   // 사용자 에이전트 (선택적)
  
  createdAt         DateTime  @default(now())
  
  @@index([userId])
  @@index([readingType])
  @@index([spreadType])
  @@index([createdAt])
}

// 사용자 질문 이력 모델 - 최근 5회 질문과 응답 저장
model UserQuestionHistory {
  id                String    @id @default(cuid())
  userId            String?   // 사용자 ID (익명 사용자의 경우 null 가능)
  user              User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  
  // 질문 정보
  question          String    // 사용자 질문
  questionType      String    // 질문 타입 (tarot, fortune, etc.)
  spreadType        String?   // 스프레드 타입 (해당하는 경우)
  consultantName    String?   // 상담받은 이름 (타로 리딩 시 입력된 이름)
  nameType          String?   // 이름 타입 분석 결과 ('real_name', 'random_input', 'unknown')
  
  // 제미니 응답 정보
  geminiResponse    String    // 제미니 원본 응답
  responseSummary   String    // 응답 요약
  responseDate      String    // 응답 날짜 (YYYY-MM-DD 형태)
  
  // 메타데이터
  ipAddress         String?   // 사용자 IP 주소
  userAgent         String?   // 사용자 에이전트
  
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  @@index([userId])
  @@index([questionType])
  @@index([createdAt])
  @@index([consultantName])
  @@index([nameType])
} 