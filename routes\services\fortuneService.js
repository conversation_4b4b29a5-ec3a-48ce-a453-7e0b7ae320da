/**
 * Service for fortune-related database operations
 */
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { 
  YEAR_FORTUNE_COST, 
  COOLDOWN_DURATION_MS, 
  logCreditCheck, 
  getRemainingCooldownTime,
  getNextSeoul6AM,
  getSeoulTimeString,
  FORTUNE_TYPES,
  CUSTOM_TAROT_COST_3_CARDS,
  CUSTOM_TAROT_COST_5_CARDS,
  CUSTOM_TAROT_COST_7_CARDS,
  CUSTOM_TAROT_COST_10_CARDS
} = require('../utils/fortuneUtils');

// 중복 요청 방지를 위한 최근 요청 맵 (메모리에 캐시)
// Key: userId-fortuneType, Value: { timestamp, count }
const recentRequestsMap = new Map();

// 중복 요청 제거를 위한 청소 인터벌 (30분마다)
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of recentRequestsMap.entries()) {
    // 5분 이상 지난 요청은 맵에서 제거
    if (now - value.timestamp > 5 * 60 * 1000) {
      recentRequestsMap.delete(key);
    }
  }
}, 30 * 60 * 1000);

/**
 * Track request to prevent duplicates
 * @param {string} userId - The user ID
 * @param {string} fortuneType - The type of fortune
 * @returns {boolean} true if this might be a duplicate request
 */
const trackRequest = (userId, fortuneType) => {
  if (!userId) return false; // 익명 사용자는 추적하지 않음
  
  const key = `${userId}-${fortuneType}`;
  const now = Date.now();
  const entry = recentRequestsMap.get(key);
  
  if (entry) {
    // 최근 5초 이내에 같은 요청이 있으면 중복으로 간주
    if (now - entry.timestamp < 5000) {
      entry.count += 1;
      console.log(`[Request Tracking] Potential duplicate request detected: ${key}, count: ${entry.count}, last request: ${new Date(entry.timestamp).toISOString()}`);
      recentRequestsMap.set(key, { timestamp: now, count: entry.count });
      return true;
    } else {
      // 5초 이상 지났으면 새 요청으로 간주하고 카운트 초기화
      recentRequestsMap.set(key, { timestamp: now, count: 1 });
      return false;
    }
  } else {
    // 첫 요청인 경우
    recentRequestsMap.set(key, { timestamp: now, count: 1 });
    return false;
  }
};

/**
 * Check if a user has sufficient credits and is not in cooldown
 * @param {string} userId - The user ID
 * @param {string} ipAddress - The IP address
 * @param {string} fortuneType - The type of fortune
 * @returns {Promise<Object>} Result object with isAllowed flag and other data
 */
const checkCreditsAndCooldown = async (userId, ipAddress, fortuneType) => {
  const now = new Date();
  const result = { isAllowed: false, reason: null, data: {} };
  
  // 중복 요청인지 확인 (5초 이내에 동일한 사용자, 동일한 타입의 요청)
  if (userId && trackRequest(userId, fortuneType)) {
    result.reason = 'potential_duplicate_request';
    result.error = '같은 요청이 처리 중입니다. 잠시 후 다시 시도해주세요.';
    result.data = {
      isPotentialDuplicate: true,
      userId,
      fortuneType
    };
    return result;
  }
  
  // 커스텀 타로는 쿨다운 없음 - 먼저 확인 (기존 정적 타입들과 Manager 스프레드 모두 포함)
  const isCustomTarot = [
    FORTUNE_TYPES.CUSTOM_THREE_CARD,
    FORTUNE_TYPES.CUSTOM_FIVE_CARD,
    FORTUNE_TYPES.CUSTOM_SEVEN_CARD,
    FORTUNE_TYPES.CUSTOM_TEN_CARD,
    FORTUNE_TYPES.CUSTOM_TAROT_READING
  ].includes(fortuneType) || (fortuneType.startsWith('custom') && !Object.values(FORTUNE_TYPES).includes(fortuneType));
  
  if (isCustomTarot) {
    // 로그인 여부 확인
    if (!userId) {
      result.reason = 'login_required';
      result.error = '고민 기반 타로점은 로그인이 필요한 서비스입니다.';
      return result;
    }
    
    // 이후 로직은 로그인 사용자의 크레딧 체크로 계속 진행
  } else {
  // 1. 무료 운세(오늘의 운세)인 경우, 로그인 여부와 관계없이 항상 IP 쿨다운부터 먼저 체크
  if (fortuneType === FORTUNE_TYPES.TODAY_THREE_CARD || fortuneType === FORTUNE_TYPES.TODAY_FIVE_CARD) {
    // IP 쿨다운 체크
    const ipCooldown = await prisma.iPFortuneCooldown.findUnique({
      where: { ipAddress_fortuneType: { ipAddress, fortuneType } },
    });
    
    if (ipCooldown && ipCooldown.cooldownExpiresAt > now) {
      result.reason = 'cooldown_active';
      result.data = {
        cooldownSource: 'ip',
        cooldownExpiresAt: ipCooldown.cooldownExpiresAt,
        remainingTime: getRemainingCooldownTime(ipCooldown.cooldownExpiresAt),
        fortuneType,
      };
      return result;
    }
  }
  }
  
  // 로그인 사용자인 경우
  if (userId) {
    const user = await prisma.user.findUnique({ where: { id: userId } });

    // 사용자 데이터가 없으면 진행 불가
    if (!user) {
      result.reason = 'user_not_found';
      result.error = '사용자를 찾을 수 없습니다. 다시 로그인해주세요.';
      return result;
    }
    
    let requiredCost = 0;
    if (fortuneType === FORTUNE_TYPES.YEAR || fortuneType === FORTUNE_TYPES.YEAR_FIVE_CARD) {
      requiredCost = YEAR_FORTUNE_COST;
    } else if (fortuneType === FORTUNE_TYPES.CUSTOM_THREE_CARD) {
      requiredCost = CUSTOM_TAROT_COST_3_CARDS;
    } else if (fortuneType === FORTUNE_TYPES.CUSTOM_FIVE_CARD) {
      requiredCost = CUSTOM_TAROT_COST_5_CARDS;
    } else if (fortuneType === FORTUNE_TYPES.CUSTOM_SEVEN_CARD) {
      requiredCost = CUSTOM_TAROT_COST_7_CARDS;
    } else if (fortuneType === FORTUNE_TYPES.CUSTOM_TEN_CARD) {
      requiredCost = CUSTOM_TAROT_COST_10_CARDS;
    } else if (fortuneType.startsWith('custom') && !Object.values(FORTUNE_TYPES).includes(fortuneType)) {
      // Manager 스프레드인 경우 데이터베이스에서 비용 정보 조회
      try {
        const spread = await prisma.tarotSpread.findFirst({
          where: {
            spreadType: fortuneType,
            isActive: true
          }
        });
        
        if (spread) {
          // 데이터베이스에서 직접 cost 필드를 사용
          const baseCost = spread.cost || 0;
          const discountRate = spread.discountRate || spread.discount || 0;
          
          // 할인율 적용
          requiredCost = Math.max(1, Math.round(baseCost * (100 - discountRate) / 100));
        } else {
          console.warn(`[Credit Check] Manager spread ${fortuneType} not found or not active`);
          requiredCost = CUSTOM_TAROT_COST_5_CARDS; // Default fallback
        }
      } catch (dbError) {
        console.error('[Credit Check] Failed to get manager spread cost:', dbError);
        requiredCost = CUSTOM_TAROT_COST_5_CARDS; // Default fallback
      }
    }

    logCreditCheck('Pre-Check', userId, user, requiredCost);
    
    // 유료 운세 또는 커스텀 타로인 경우 크레딧 검증
    if (requiredCost > 0) {
      if (user.credits < requiredCost) {
        result.reason = 'insufficient_credits';
        result.data = {
          currentCredits: user.credits,
          neededCredits: requiredCost,
          fortuneType, // Pass fortuneType for better error messaging on frontend
        };
        return result;
      }
    }

    // 사용자 쿨다운 확인 (커스텀 타로는 쿨다운 없음)
    if (!isCustomTarot) {
    const userCooldown = await prisma.userFortuneCooldown.findUnique({
      where: { userId_fortuneType: { userId, fortuneType } },
    });

    if (userCooldown && userCooldown.cooldownExpiresAt > now) {
      result.reason = 'cooldown_active';
      result.data = {
        cooldownSource: 'user',
        cooldownExpiresAt: userCooldown.cooldownExpiresAt,
        remainingTime: getRemainingCooldownTime(userCooldown.cooldownExpiresAt),
        fortuneType,
      };
      return result;
      }
    }
    
    // 모든 검사를 통과한 사용자의 데이터
    result.isAllowed = true;
    result.data.currentCredits = user.credits;
    return result;
  } 
  // 비로그인 사용자인 경우
  else {
    // 커스텀 타로는 로그인이 필수 (위에서 이미 체크했지만 안전을 위해 중복 체크)
    if (isCustomTarot) {
      result.reason = 'login_required';
      result.error = '고민 기반 타로점은 로그인이 필요한 서비스입니다.';
      return result;
    }
    // 유료 운세(연간 운세)도 로그인이 필수
    if (fortuneType === FORTUNE_TYPES.YEAR || fortuneType === FORTUNE_TYPES.YEAR_FIVE_CARD) {
    result.reason = 'login_required_for_paid_fortune';
    result.error = '로그인이 필요한 서비스입니다.';
    return result;
    }
  }

  // 비로그인 사용자의 경우 (오늘의 운세만 해당)
  result.isAllowed = true;
  return result;
};

/**
 * Record cooldown after fortune generation
 * @param {string} userId - The user ID (optional)
 * @param {string} ipAddress - The IP address
 * @param {string} fortuneType - The type of fortune
 * @returns {Promise<Date>} The cooldown expiration date
 */
const recordCooldown = async (userId, ipAddress, fortuneType) => {
  // 커스텀 타로는 쿨다운 없음 - 바로 리턴 (기존 정적 타입들과 Manager 스프레드 모두 포함)
  const isCustomTarot = [
    FORTUNE_TYPES.CUSTOM_THREE_CARD,
    FORTUNE_TYPES.CUSTOM_FIVE_CARD,
    FORTUNE_TYPES.CUSTOM_SEVEN_CARD,
    FORTUNE_TYPES.CUSTOM_TEN_CARD,
    FORTUNE_TYPES.CUSTOM_TAROT_READING
  ].includes(fortuneType) || (fortuneType.startsWith('custom') && !Object.values(FORTUNE_TYPES).includes(fortuneType));
  
  if (isCustomTarot) {
    console.log(`No cooldown applied for custom tarot type: ${fortuneType}`);
    return null;
  }

  const now = new Date();
  const newCooldownExpiresAt = getNextSeoul6AM(now);
  
  console.log(`[Cooldown] ${fortuneType} cooldown expires at: ${getSeoulTimeString(newCooldownExpiresAt)}`);
  
  // Record user cooldown if available
  if (userId) {
    // 사용자 존재 확인 후 쿨다운 기록
    const user = await prisma.user.findUnique({ where: { id: userId } });
    if (!user) {
      console.error(`[Critical Error] User ID ${userId} not found during cooldown recording.`);
      throw new Error("User not found during cooldown recording");
    }
    
    await prisma.userFortuneCooldown.upsert({
      where: { userId_fortuneType: { userId, fortuneType } },
      update: { cooldownExpiresAt: newCooldownExpiresAt },
      create: { userId, fortuneType, cooldownExpiresAt: newCooldownExpiresAt },
    });
    
    // Also record IP cooldown for user
    await prisma.iPFortuneCooldown.upsert({
      where: { ipAddress_fortuneType: { ipAddress, fortuneType } },
      update: { cooldownExpiresAt: newCooldownExpiresAt },
      create: { ipAddress, fortuneType, cooldownExpiresAt: newCooldownExpiresAt },
    });
    
    console.log(`Recorded/Updated ${fortuneType} cooldown for user ${userId} AND IP ${ipAddress} until ${getSeoulTimeString(newCooldownExpiresAt)}`);
  } else {
    // Only record IP cooldown for non-logged users
    await prisma.iPFortuneCooldown.upsert({
      where: { ipAddress_fortuneType: { ipAddress, fortuneType } },
      update: { cooldownExpiresAt: newCooldownExpiresAt },
      create: { ipAddress, fortuneType, cooldownExpiresAt: newCooldownExpiresAt },
    });
    
    console.log(`Recorded/Updated ${fortuneType} cooldown for IP ${ipAddress} until ${getSeoulTimeString(newCooldownExpiresAt)}`);
  }
  
  return newCooldownExpiresAt;
};

/**
 * Deduct credits for paid fortunes
 * @param {string} userId - The user ID
 * @param {number} cost - The cost to deduct
 * @returns {Promise<number>} The updated credit balance
 */
const deductCredits = async (userId, cost) => {
  if (!userId) {
    console.error(`[Critical Error] User ID not found for paid fortune. This should not happen.`);
    throw new Error("User context lost");
  }
  
  // 사용자 존재 확인 후 크레딧 차감
  const user = await prisma.user.findUnique({ 
    where: { id: userId },
    // Use transaction or locking strategy to prevent race conditions
    select: { id: true, credits: true, lastCreditDeduction: true }
  });
  
  if (!user) {
    console.error(`[Critical Error] User ID ${userId} not found during credit deduction.`);
    throw new Error("User not found during credit deduction");
  }
  
  // 크레딧이 충분한지 재확인
  if (user.credits < cost) {
    console.error(`[Critical Error] Insufficient credits for user ${userId}. Available: ${user.credits}, Required: ${cost}`);
    throw new Error("Insufficient credits");
  }
  
  // Create a unique deduction ID for this request
  const now = new Date();
  const deductionTimestamp = now.getTime();
  
  // Check for recent deductions to prevent duplicates (within last 10 seconds)
  if (user.lastCreditDeduction) {
    const lastDeduction = new Date(user.lastCreditDeduction);
    const timeSinceLastDeduction = now.getTime() - lastDeduction.getTime();
    
    // If there was a very recent deduction (within 5 seconds), BLOCK the second deduction
    if (timeSinceLastDeduction < 5000) {
      console.log(`[Deduction Blocked] Prevented duplicate credit deduction for user ${userId}. Last deduction: ${lastDeduction.toISOString()}, Current attempt: ${now.toISOString()}`);
      // Return current credits without deducting
      return user.credits;
    }
    // If recent but not too recent (5-10 seconds), add a warning but still proceed
    else if (timeSinceLastDeduction < 10000) {
      console.log(`[Deduction Warning] Recent credit deduction detected for user ${userId}. Last: ${lastDeduction.toISOString()}, Current: ${now.toISOString()}`);
    }
  }
  
  // Update credits and record deduction timestamp
  const updatedUser = await prisma.user.update({
    where: { id: userId },
    data: { 
      credits: { decrement: cost },
      lastCreditDeduction: now
    },
  });
  
  console.log(`Successfully deducted ${cost} credits from user ${userId}. New balance: ${updatedUser.credits}`);
  
  return updatedUser.credits;
};

module.exports = {
  checkCreditsAndCooldown,
  recordCooldown,
  deductCredits,
  trackRequest
}; 