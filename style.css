/* style.css */

/* 기본 폰트 및 전체 스타일 */
body {
    font-family: 'Poor Story', 'Cute Font', cursive, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #FFFDE7; /* 아주 연한 크림색으로 변경하여 눈의 피로도 감소 */
    color: #5D4037; /* 좀 더 부드러운 갈색 텍스트 */
    line-height: 1.7; /* 줄 간격 살짝 넓힘 */
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased; /* 폰트 부드럽게 */
    -moz-osx-font-smoothing: grayscale;
}

/* 링크 및 기본 요소 */
a {
    text-decoration: none;
    color: #FF69B4; /* 기본 링크 색상을 귀여운 핑크로 */
    transition: color 0.3s ease;
}
a:hover {
    color: #E91E63; /* 호버 시 좀 더 진한 핑크 */
}

ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

/* 공통 클래스 */
.icon {
    margin-right: 8px;
    vertical-align: middle;
    width: 1.1em; /* Default width for icons */
    height: 1.1em; /* Default height for icons */
    fill: currentColor; /* Inherit color from parent text for monochrome SVGs */
}

/* 공통 아이콘 스타일 (구체적인 svg 태그 대상) */
svg.icon {
    /* You can add more specific styles for SVGs if needed */
    /* For example, if some SVGs need different default colors */
}

.section-title {
    font-family: 'Cute Font', cursive;
    font-size: 2.8em; /* 살짝 더 크게 */
    color: #795548;
    text-align: center;
    margin-top: 70px;
    margin-bottom: 50px;
    text-shadow: 1px 1px 3px rgba(121, 85, 72, 0.2); /* 그림자 부드럽게 */
}

.cta-button {
    display: inline-block;
    padding: 14px 30px; /* 패딩 살짝 늘림 */
    border-radius: 30px; /* 더 둥글게 */
    font-family: 'Cute Font', cursive;
    font-size: 1.3em;
    text-align: center;
    cursor: pointer;
    transition: transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275), box-shadow 0.2s ease; /* 통통 튀는 효과 */
    border: none;
    box-shadow: 3px 3px 7px rgba(0,0,0,0.1);
}

.cta-button:hover {
    transform: translateY(-3px) scale(1.05); /* 호버 시 살짝 커지며 위로 */
    box-shadow: 5px 5px 10px rgba(0,0,0,0.15);
}

/* 헤더 */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 40px; /* 상하 패딩을 15px에서 10px으로 줄임 */
    background-color: rgba(255, 255, 255, 0.9); /* 투명도 살짝 조절 */
    box-shadow: 0 3px 8px rgba(0,0,0,0.08); /* 그림자 부드럽게 */
    position: fixed;
    width: calc(100% - 80px);
    top: 0;
    left: 0;
    z-index: 1000;
    border-bottom: 1px solid #FFF9C4; /* 하단에 얇은 경계선 */
}

header .logo h1 a {
    font-family: 'Cute Font', cursive;
    font-size: 2.2em;
    color: #673AB7; /* 좀 더 진한 보라색으로 로고 강조 */
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

header nav ul {
    display: flex;
    gap: 10px; /* 메뉴 간 간격 */
}

header nav ul li a {
    padding: 12px 18px;
    font-size: 1.15em;
    color: #7E57C2; /* 보라색 계열 */
    transition: color 0.2s ease, transform 0.2s ease;
    border-radius: 15px;
}

header nav ul li a:hover {
    color: #FFFFFF;
    background-color: #FFB74D; /* 호버 시 주황색 배경 */
    transform: translateY(-2px);
}
header nav ul li a:hover svg.icon { /* 호버 시 아이콘 색상 변경 (흰색) */
    fill: #FFFFFF;
}

header .user-menu a {
    margin-left: 20px;
    padding: 10px 18px;
    border-radius: 25px;
    font-size: 1em;
    font-weight: bold; /* 글씨 살짝 두껍게 */
}

header .user-menu .login-btn {
    background-color: #90CAF9; /* 연한 하늘색 */
    color: #1E88E5;
    border: 1px solid #64B5F6;
}
header .user-menu .login-btn:hover {
    background-color: #64B5F6;
    color: white;
}

header .user-menu .signup-btn {
    background-color: #A5D6A7; /* 연한 민트색 */
    color: #388E3C;
    border: 1px solid #81C784;
}
header .user-menu .signup-btn:hover {
    background-color: #81C784;
    color: white;
}

/* 메인 콘텐츠 영역 */
main {
    padding-top: 80px; /* 헤더 높이 변경에 맞춰 수정 (기존 90px에서 10px 줄임) */
}

/* 히어로 섹션 */
.hero {
    /* background-image: url('images/hero_background_cute.png'); */
    background: linear-gradient(135deg, #E1BEE7 0%, #D1C4E9 100%); /* 연보라 그라데이션 */
    background-size: cover;
    background-position: center;
    padding: 100px 20px 120px; /* 패딩 늘려 더 넓게 */
    text-align: center;
    color: white;
    position: relative; /* 애니메이션 요소 배치를 위해 */
    overflow: hidden; /* 내부 애니메이션 요소가 벗어나지 않도록 */
}
/* 히어로 섹션 별 애니메이션 (예시) */
.hero::before, .hero::after {
    content: '✨';
    position: absolute;
    font-size: 2em;
    opacity: 0.7;
    animation: sparkle 5s infinite linear;
}
.hero::before {
    top: 15%; left: 10%;
    animation-delay: 0s;
}
.hero::after {
    top: 60%; right: 15%; font-size: 3em;
    animation-delay: 1s;
}
@keyframes sparkle {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.5); opacity: 1; }
}

.hero h2 {
    font-family: 'Cute Font', cursive;
    font-size: 4em; /* 제목 더 강조 */
    margin-bottom: 25px;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.25);
    line-height: 1.2;
}

.hero p {
    font-size: 1.4em;
    margin-bottom: 40px;
    max-width: 650px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.8;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
}

.hero .primary-cta {
    background: linear-gradient(45deg, #FFAB91, #FF8A65, #FF7043); /* 좀 더 풍부한 복숭아 그라데이션 */
    color: white;
    font-size: 1.6em;
    padding: 18px 40px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

/* 서비스 소개 섹션 */
.services {
    padding: 70px 20px;
    background-color: #FFECB3; /* 연한 노랑/꿀색 배경 */
}

.service-cards-container {
    display: flex;
    justify-content: center;
    gap: 35px;
    flex-wrap: wrap;
}

.service-card {
    background-color: white;
    border-radius: 25px;
    padding: 35px;
    width: 310px;
    box-shadow: 0 8px 20px rgba(180, 141, 87, 0.15); /* 좀 더 부드럽고 넓은 그림자 */
    text-align: center;
    transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), box-shadow 0.3s ease;
    border: 2px dashed #FFD54F; /* 점선 테두리 추가 */
}

.service-card:hover {
    transform: translateY(-12px) rotate(1deg); /* 살짝 기울어지는 효과 추가 */
    box-shadow: 0 12px 25px rgba(180, 141, 87, 0.2);
}

.service-card .service-icon {
    width: 90px;
    height: 90px;
    margin-bottom: 25px;
    /* filter: drop-shadow(3px 3px 5px rgba(0,0,0,0.1)); 아이콘에 그림자 */
}

.service-card h4 {
    font-family: 'Cute Font', cursive;
    font-size: 2em;
    color: #A1887F; /* 연한 갈색 */
    margin-bottom: 15px;
}

.service-card p {
    font-size: 1.05em;
    margin-bottom: 25px;
    color: #78564B;
}

.service-card .cta-button {
    background-color: #FFB74D; /* 주황색 */
    color: #fff;
    font-size: 1.1em;
}

/* AI 마스터 소개 */
.ai-masters {
    padding: 70px 20px;
    background-color: #C5CAE9; /* 연한 인디고/라벤더 배경 */
}

.masters-container {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.master-card {
    background-color: white;
    border-radius: 20px; /* 원형 대신 둥근 사각형으로 변경 */
    width: 250px; /* 너비 살짝 키움 */
    /* height: auto; 콘텐츠에 맞게 자동 조절 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start; /* 위에서부터 정렬 */
    padding: 30px;
    text-align: center;
    box-shadow: 0 8px 20px rgba(92, 107, 192, 0.15);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.master-card:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 30px rgba(92, 107, 192, 0.2);
}

.master-card .master-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin-bottom: 20px;
    border: 4px solid #E8EAF6; /* 연한 라벤더 테두리 */
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.master-card h5 {
    font-family: 'Cute Font', cursive;
    font-size: 1.7em;
    color: #3F51B5; /* 인디고 */
    margin-bottom: 8px;
}

.master-card .master-intro {
    font-size: 0.95em;
    margin-bottom: 20px;
    color: #5C6BC0;
    min-height: 40px; /* 여러 줄 소개를 위해 최소 높이 */
    display: flex; /* 아이콘과 텍스트 정렬을 위해 추가 */
    align-items: center; /* 아이콘과 텍스트 세로 중앙 정렬 */
    justify-content: center; /* AI 마스터 카드 내 텍스트 중앙 정렬 유지 */
}

.master-specialty-icon {
    width: 1.3em; /* 전문 분야 아이콘은 살짝 더 크게 */
    height: 1.3em;
    margin-left: 5px; /* 텍스트와의 간격 */
    fill: #FF69B4; /* 전문 분야 아이콘은 핑크색으로 포인트 */
}

.master-card .small-cta {
    font-size: 1em;
    padding: 10px 20px;
    background-color: #FFEE58; /* 밝은 노랑 */
    color: #5D4037;
    border: 1px solid #FDD835;
}

/* 오늘의 타로 미리보기 */
.daily-preview {
    padding: 70px 20px;
    background-color: #DCEDC8; /* 연한 초록 */
    text-align: center;
}

.daily-tarot-box {
    background: url('images/paper_texture_cute.png'); /* 종이 질감 배경 (실제 이미지 필요) */
    background-color: #FFFFF0; /* 아이보리 백업 */
    padding: 45px;
    border-radius: 25px;
    display: inline-block;
    box-shadow: 0 8px 20px rgba(124, 179, 66, 0.2);
    max-width: 450px;
    border: 3px dashed #AED581; /* 점선 테두리 */
}

.daily-tarot-box #giftBoxIcon {
    width: 120px;
    margin-bottom: 25px;
    cursor: pointer; /* 클릭 가능 암시 */
    transition: transform 0.3s ease;
}
.daily-tarot-box #giftBoxIcon:hover {
    transform: rotate(5deg) scale(1.1);
}

.daily-tarot-box #dailyCardMessage {
    font-size: 1.3em;
    margin-bottom: 25px;
    color: #558B2F; /* 진한 초록 */
    font-weight: bold;
}

.daily-tarot-box #todayCardArea {
    border-top: 2px dotted #8BC34A; /* 구분선 */
    margin-top: 25px;
    padding-top: 25px;
}

.daily-tarot-box #todayCardArea #todayCardImage {
    width: 180px;
    margin-bottom: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
}

.daily-tarot-box #todayCardArea #todayCardInterpretation {
    font-size: 1.4em;
    font-family: 'Cute Font', cursive;
    color: #EF6C00; /* 진한 주황 */
    margin-bottom: 25px;
    line-height: 1.5;
}

.daily-preview .cta-button {
    background-color: #FF80AB; /* 밝은 핑크 */
    color: white;
    font-size: 1.2em;
}

/* 사용자 후기 */
.testimonials {
    padding: 70px 20px;
    background-color: #FCE4EC; /* 아주 연한 핑크 배경 */
}

.testimonial-bubbles {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 25px;
}

.bubble {
    background-color: white;
    padding: 25px 30px;
    border-radius: 20px; /* 말풍선 느낌으로 더 둥글게 */
    box-shadow: 0 5px 15px rgba(233, 30, 99, 0.1);
    max-width: 550px;
    position: relative;
    border: 2px solid #F8BBD0; /* 연핑크 테두리 */
}

.bubble::before { /* 말풍선 꼬리 */
    content: "";
    position: absolute;
    bottom: -10px;
    left: 30px;
    border-width: 15px 15px 0 0;
    border-style: solid;
    border-color: #F8BBD0 transparent transparent transparent; /* 테두리 색과 맞춤 */
    display: block;
    width: 0;
}
.bubble::after { /* 꼬리 안쪽 흰색으로 채우기 */
    content: "";
    position: absolute;
    bottom: -7px; /* 테두리 두께만큼 안으로 */
    left: 32px;  /* 테두리 두께만큼 안으로 */
    border-width: 13px 13px 0 0;
    border-style: solid;
    border-color: white transparent transparent transparent;
    display: block;
    width: 0;
}


.bubble p {
    font-size: 1.05em;
    margin: 0;
    font-style: normal;
    color: #C2185B; /* 진한 핑크 텍스트 */
}
.bubble p::after { /* 인용구 느낌 */
    content: '”';
    font-size: 1.5em;
    color: #F48FB1;
    margin-left: 5px;
}
.bubble p::before {
    content: '“';
    font-size: 1.5em;
    color: #F48FB1;
    margin-right: 5px;
}


/* 푸터 */
footer {
    background-color: #FFFFFFE6; /* 좀 더 부드러운 갈색 */
    color: #FFF3E0; /* 연한 오렌지/크림 텍스트 */
    padding: 40px 20px;
    text-align: center;
    border-top: 5px solid #FFFFFFE6;
}

.footer-content {
    max-width: 800px;
    margin: 0 auto;
}

.footer-nav {
    margin-bottom: 20px; /* 하단 저작권과 간격 */
}

.footer-nav a {
    margin: 0 12px;
    font-size: 1em;
    color: #FFCC80; /* 연한 주황 */
    transition: color 0.2s ease, text-shadow 0.2s ease;
}

.footer-nav a:hover {
    color: #FFFFFF;
    text-shadow: 0 0 5px #FFFFFF;
}

.copyright {
    font-size: 0.95em;
    margin-top: 20px;
    margin-bottom: 20px;
    opacity: 0.9;
}

.social-links img {
    width: 35px;
    height: 35px;
    margin: 0 10px;
    opacity: 0.85;
    transition: opacity 0.2s ease, transform 0.2s ease;
    border-radius: 50%; /* 아이콘도 둥글게 */
    background-color: rgba(255,255,255,0.1); /* 아이콘 배경 살짝 */
    padding: 5px;
}

.social-links img:hover {
    opacity: 1;
    transform: scale(1.1);
}

.footer-mascot {
    width: 90px;
    margin-top: 25px;
    opacity: 0.8;
    filter: drop-shadow(0 0 5px #A1887F);
}

/* Modal Styles */
.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 2000; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgba(0,0,0,0.5); /* Black w/ opacity */
    padding-top: 60px;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background-color: #FFFAF0; /* FloralWhite, 부드러운 배경 */
    margin: 5% auto;
    padding: 30px 40px;
    border: 3px dashed #FFB6C1; /* LightPink 테두리 */
    border-radius: 25px;
    width: 80%;
    max-width: 500px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    position: relative;
    text-align: center;
    animation: slideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.close-btn {
    color: #FF7F50; /* Coral */
    float: right;
    font-size: 32px;
    font-weight: bold;
    transition: color 0.2s ease, transform 0.2s ease;
}

.close-btn:hover,
.close-btn:focus {
    color: #E9967A; /* DarkSalmon */
    text-decoration: none;
    cursor: pointer;
    transform: rotate(90deg) scale(1.1);
}

.modal-content h3 {
    font-family: 'Cute Font', cursive;
    font-size: 2.2em;
    color: #FF69B4; /* HotPink */
    margin-bottom: 25px;
}

/* Form Group Styles */
.form-group {
    margin-bottom: 20px;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-family: 'Poor Story', cursive;
    font-size: 1.1em;
    color: #777;
}

.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="text"] {
    width: calc(100% - 24px); /* padding 고려 */
    padding: 12px;
    border: 2px solid #FFDAB9; /* PeachPuff */
    border-radius: 15px;
    font-family: 'Poor Story', cursive;
    font-size: 1.1em;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input[type="email"]:focus,
.form-group input[type="password"]:focus,
.form-group input[type="text"]:focus {
    border-color: #FFA07A; /* LightSalmon */
    box-shadow: 0 0 8px rgba(255, 160, 122, 0.5);
    outline: none;
}

.auth-button {
    width: 100%;
    padding: 15px;
    font-size: 1.4em;
    background-color: #98FB98; /* PaleGreen */
    color: #2E8B57; /* SeaGreen */
    border: 2px solid #8FBC8F; /* DarkSeaGreen */
    margin-top: 10px;
    margin-bottom: 20px;
}
.auth-button:hover {
    background-color: #8FBC8F;
    color: white;
}

/* Social Login Buttons */
.social-login {
    margin-top: 25px;
    margin-bottom: 15px;
}
.social-login p {
    font-size: 0.95em;
    color: #888;
    margin-bottom: 15px;
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 12px;
    border-radius: 15px;
    font-family: 'Poor Story', cursive;
    font-size: 1.1em;
    margin-bottom: 10px;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: background-color 0.2s ease, transform 0.2s ease;
}
.social-btn:hover {
    transform: translateY(-2px);
}

.social-btn img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
}

.google-btn {
    background-color: #FFE0B2; /* 연한 주황 (구글 느낌) */
    color: #BF360C;
}
.google-btn:hover {
    background-color: #FFCC80;
}

.kakao-btn {
    background-color: #FFF59D; /* 연한 노랑 (카카오 느낌) */
    color: #795548;
}
.kakao-btn:hover {
    background-color: #FFEE58;
}

.switch-form {
    margin-top: 25px;
    font-size: 0.9em;
    color: #666;
}
.switch-form a {
    color: #FF6347; /* Tomato */
    font-weight: bold;
    text-decoration: underline;
}
.switch-form a:hover {
    color: #E5533D;
}

.tarot-card-container {
  /* Ensure bottom center origin for fan rotation if not implicitly handled by layout */
  /* transform-origin: bottom center; */
}

.tarot-card-image:hover {
  /* Kept hover transform in inline style for dynamic adjustments based on selection state */
}

.tarot-card-container.selected .tarot-card-image {
  /* Styles for selected card image if any specific needed beyond inline styles */
  /* Example: border: 2px solid gold; */
}


/* Animation for button fade-in */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Ensure the root or a parent container allows the absolute positioning to work as expected */
/* For example, if #root is your main app container */
#root {
  position: relative; /* or any other positioning context */
}

/* General centering for full screen views if not already handled */
.full-screen-content-wrapper > .flex.flex-col.items-center.justify-center {
    min-height: 100vh; /* Ensure it takes full viewport height */
} 