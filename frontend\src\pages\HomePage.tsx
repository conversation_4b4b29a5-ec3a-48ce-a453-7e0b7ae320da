import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { tarotCardsData, type TarotCard } from '../data/tarotCardsData';
import './HomePage.css';

// Zodiac constellation SVG components
const ZodiacConstellation: React.FC<{ 
  constellation: string; 
  className?: string; 
  style?: React.CSSProperties 
}> = ({ constellation, className = '', style = {} }) => {
  const constellations = {
    aries: (
      <svg viewBox="0 0 100 100" className={`constellation ${className}`} style={style}>
        <circle cx="20" cy="30" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="45" cy="20" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="70" cy="35" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="80" cy="60" r="1.5" fill="currentColor" opacity="0.8" />
        <line x1="20" y1="30" x2="45" y2="20" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
        <line x1="45" y1="20" x2="70" y2="35" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
        <line x1="70" y1="35" x2="80" y2="60" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
      </svg>
    ),
    leo: (
      <svg viewBox="0 0 100 100" className={`constellation ${className}`} style={style}>
        <circle cx="15" cy="20" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="35" cy="15" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="55" cy="25" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="75" cy="30" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="85" cy="50" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="65" cy="65" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="45" cy="70" r="1.5" fill="currentColor" opacity="0.8" />
        <line x1="15" y1="20" x2="35" y2="15" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
        <line x1="35" y1="15" x2="55" y2="25" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
        <line x1="55" y1="25" x2="75" y2="30" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
        <line x1="75" y1="30" x2="85" y2="50" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
        <line x1="85" y1="50" x2="65" y2="65" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
        <line x1="65" y1="65" x2="45" y2="70" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
      </svg>
    ),
    libra: (
      <svg viewBox="0 0 100 100" className={`constellation ${className}`} style={style}>
        <circle cx="25" cy="40" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="50" cy="25" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="75" cy="40" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="50" cy="60" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="30" cy="75" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="70" cy="75" r="1.5" fill="currentColor" opacity="0.8" />
        <line x1="25" y1="40" x2="50" y2="25" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
        <line x1="50" y1="25" x2="75" y2="40" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
        <line x1="25" y1="40" x2="30" y2="75" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
        <line x1="75" y1="40" x2="70" y2="75" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
        <line x1="50" y1="60" x2="30" y2="75" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
        <line x1="50" y1="60" x2="70" y2="75" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
      </svg>
    ),
    scorpio: (
      <svg viewBox="0 0 100 100" className={`constellation ${className}`} style={style}>
        <circle cx="20" cy="30" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="40" cy="45" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="60" cy="35" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="75" cy="20" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="85" cy="40" r="1.5" fill="currentColor" opacity="0.8" />
        <circle cx="70" cy="65" r="1.5" fill="currentColor" opacity="0.8" />
        <line x1="20" y1="30" x2="40" y2="45" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
        <line x1="40" y1="45" x2="60" y2="35" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
        <line x1="60" y1="35" x2="75" y2="20" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
        <line x1="75" y1="20" x2="85" y2="40" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
        <line x1="85" y1="40" x2="70" y2="65" stroke="currentColor" strokeWidth="0.5" opacity="0.6" />
      </svg>
    ),
  };

  return constellations[constellation as keyof typeof constellations] || null;
};

// Mystical Astrology Orb Component
const MysticalAstrologyOrb: React.FC = () => {
  return (
    <div className="mystical-astrology-orb">
      {/* Outer Ring - Tarot Major Arcana Symbols - 작고 은은한 화이트 디자인 */}
      <div className="astrology-ring outer-ring">
        <svg viewBox="0 0 300 300" className="tarot-ring">
          <g className="tarot-symbols">
            {/* The Fool - 0 */}
            <g transform="translate(150,25)">
              <circle cx="0" cy="0" r="4" stroke="rgba(255,255,255,0.5)" strokeWidth="1.5" fill="none"/>
              <path d="M-2,-5 L2,-5 M0,-5 L0,-8" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
            </g>
            
            {/* The Magician - I */}
            <g transform="translate(230,60)">
              <path d="M0,-6 L0,6 M-2,-4 L2,-4 M-2,4 L2,4" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
              <circle cx="0" cy="-6" r="1.5" fill="rgba(255,255,255,0.5)"/>
            </g>
            
            {/* High Priestess - II */}
            <g transform="translate(275,150)">
              <path d="M-5,-5 L5,-5 M-5,5 L5,5 M0,-5 L0,5" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
              <circle cx="-3" cy="0" r="1.5" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8" fill="none"/>
              <circle cx="3" cy="0" r="1.5" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8" fill="none"/>
            </g>
            
            {/* Empress - III */}
            <g transform="translate(230,240)">
              <path d="M0,-5 C-3,-5 -5,-3 -5,0 C-5,3 -3,5 0,5 C3,5 5,3 5,0 C5,-3 3,-5 0,-5" 
                    stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
              <circle cx="0" cy="-2" r="0.8" fill="rgba(255,255,255,0.5)"/>
            </g>
            
            {/* Emperor - IV */}
            <g transform="translate(150,275)">
              <path d="M-5,-5 L5,-5 L5,5 L-5,5 Z M-2,-5 L-2,5 M2,-5 L2,5" 
                    stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
            </g>
            
            {/* Hierophant - V */}
            <g transform="translate(70,240)">
              <path d="M0,-6 L0,6 M-4,-3 L4,-3 M-4,0 L4,0 M-4,3 L4,3" 
                    stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
            </g>
            
            {/* Lovers - VI */}
            <g transform="translate(25,150)">
              <circle cx="-3" cy="0" r="2.5" stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
              <circle cx="3" cy="0" r="2.5" stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
              <path d="M0,-5 L0,5" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
            </g>
            
            {/* Chariot - VII */}
            <g transform="translate(70,60)">
              <path d="M-5,-3 L5,-3 L3,3 L-3,3 Z M-2,-3 L-2,3 M2,-3 L2,3" 
                    stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
              <circle cx="-2" cy="5" r="1.5" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8" fill="none"/>
              <circle cx="2" cy="5" r="1.5" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8" fill="none"/>
            </g>

            {/* Strength - VIII */}
            <g transform="translate(195,40)">
              <path d="M0,-4 L4,0 L0,4 L-4,0 Z" stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
            </g>
            
            {/* Hermit - IX */}
            <g transform="translate(260,105)">
              <path d="M0,-5 L0,5 M-4,3 L4,3" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
              <circle cx="0" cy="-4" r="1.5" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8" fill="none"/>
            </g>
            
            {/* Wheel of Fortune - X */}
            <g transform="translate(260,195)">
              <circle cx="0" cy="0" r="5" stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
              <path d="M0,-5 L0,5 M-5,0 L5,0 M-3,-3 L3,3 M-3,3 L3,-3" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8"/>
            </g>
            
            {/* Justice - XI */}
            <g transform="translate(195,260)">
              <path d="M-5,-5 L5,-5 M0,-5 L0,5 M-6,1 L6,1" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
            </g>
            
            {/* Hanged Man - XII */}
            <g transform="translate(105,260)">
              <path d="M-4,-5 L4,-5 M0,-5 L0,1" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
              <circle cx="0" cy="3" r="2" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8" fill="none"/>
            </g>
            
            {/* Death - XIII */}
            <g transform="translate(40,195)">
              <path d="M-4,-4 L4,-4 L4,4 L-4,4 Z" stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
              <path d="M-3,-3 L3,3 M-3,3 L3,-3" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8"/>
            </g>
            
            {/* Temperance - XIV */}
            <g transform="translate(40,105)">
              <path d="M0,-5 L0,5 M-4,0 L4,0" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
              <circle cx="0" cy="-3" r="1.5" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8" fill="none"/>
              <circle cx="0" cy="3" r="1.5" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8" fill="none"/>
            </g>
            
            {/* Devil - XV */}
            <g transform="translate(105,40)">
              <path d="M0,-5 L0,5 M-4,-2 L4,-2 M-4,2 L4,2" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
              <polygon points="0,-6 2,-4 -2,-4" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8" fill="none"/>
            </g>
          </g>
        </svg>
      </div>

      {/* Middle Ring - Alchemical Elements - 작고 은은한 화이트 디자인 */}
      <div className="astrology-ring middle-ring">
        <svg viewBox="0 0 240 240" className="alchemy-ring">
          <g className="alchemy-symbols">
            {/* Fire - △ */}
            <g transform="translate(120,30)">
              <polygon points="0,-5 4,3 -4,3" stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
              <circle cx="0" cy="0" r="1" fill="rgba(255,255,255,0.5)"/>
            </g>
            
            {/* Water - ▽ */}
            <g transform="translate(190,70)">
              <polygon points="0,5 4,-3 -4,-3" stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
              <path d="M-2,1 C-1,2 1,2 2,1" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8"/>
            </g>
            
            {/* Air - △ with line */}
            <g transform="translate(210,120)">
              <polygon points="0,-5 4,3 -4,3" stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
              <path d="M-3,0 L3,0" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
            </g>
            
            {/* Earth - ▽ with line */}
            <g transform="translate(190,170)">
              <polygon points="0,5 4,-3 -4,-3" stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
              <path d="M-3,0 L3,0" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
            </g>
            
            {/* Gold - ☉ */}
            <g transform="translate(120,210)">
              <circle cx="0" cy="0" r="4" stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
              <circle cx="0" cy="0" r="1.5" fill="rgba(255,255,255,0.5)"/>
              <path d="M0,-6 L0,-4 M0,4 L0,6 M-6,0 L-4,0 M4,0 L6,0" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8"/>
            </g>
            
            {/* Silver - ☽ */}
            <g transform="translate(50,170)">
              <path d="M-3,0 C-3,-4 3,-4 3,0 C3,4 -3,4 -3,0" 
                    stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
              <circle cx="1" cy="0" r="0.8" fill="rgba(255,255,255,0.5)"/>
            </g>
            
            {/* Mercury - ☿ */}
            <g transform="translate(30,120)">
              <circle cx="0" cy="0" r="3" stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
              <path d="M0,-5 L0,0 M-2,-5 L2,-5 M0,3 L0,6 M-1.5,4.5 L1.5,4.5" 
                    stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
            </g>
            
            {/* Sulfur - 🜍 */}
            <g transform="translate(50,70)">
              <polygon points="0,-4 3,1 -3,1" stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
              <path d="M0,1 L0,4 M-2,2.5 L2,2.5" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
            </g>

            {/* Iron/Mars - ♂ */}
            <g transform="translate(155,50)">
              <circle cx="0" cy="1" r="3" stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
              <path d="M0,-2 L0,-6 M0,-6 L3,-6 M0,-6 L2,-4" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
            </g>
            
            {/* Venus - ♀ */}
            <g transform="translate(185,105)">
              <circle cx="0" cy="-2" r="3" stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
              <path d="M0,1 L0,5 M-2,3 L2,3" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
            </g>
            
            {/* Jupiter - ♃ */}
            <g transform="translate(185,135)">
              <path d="M-2,-6 L-2,6 M-2,0 L3,0" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
              <path d="M3,-6 C3,-3 1,-1 -1,0" stroke="rgba(255,255,255,0.5)" strokeWidth="1" fill="none"/>
            </g>
            
            {/* Saturn - ♄ */}
            <g transform="translate(155,190)">
              <path d="M0,-6 L0,6 M-3,0 L3,0 M-2,-4 L2,-4" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
            </g>
            
            {/* Salt - ⚹ */}
            <g transform="translate(85,190)">
              <circle cx="0" cy="0" r="3" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8" fill="none"/>
              <path d="M0,-5 L0,5 M-5,0 L5,0" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
            </g>
            
            {/* Antimony - ♁ */}
            <g transform="translate(55,135)">
              <circle cx="0" cy="-2" r="2" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8" fill="none"/>
              <path d="M0,0 L0,4 M-2,4 L2,4 M-3,2 L3,2" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
            </g>
            
            {/* Copper - ♀ variant */}
            <g transform="translate(55,105)">
              <circle cx="0" cy="0" r="2" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8" fill="none"/>
              <path d="M0,2 L0,4 M-1,4 L1,4 M0,-4 L0,-2" stroke="rgba(255,255,255,0.5)" strokeWidth="1"/>
              <path d="M-1,-4 L1,-4" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8"/>
            </g>
            
            {/* Tin - ♃ variant */}
            <g transform="translate(85,50)">
              <path d="M0,-4 C-2,-4 -3,-2 -3,0 C-3,2 -2,4 0,4 C2,4 3,2 3,0 C3,-2 2,-4 0,-4" 
                    stroke="rgba(255,255,255,0.5)" strokeWidth="0.8" fill="none"/>
              <path d="M0,4 L0,6 M-2,6 L2,6" stroke="rgba(255,255,255,0.5)" strokeWidth="0.8"/>
            </g>
          </g>
        </svg>
      </div>

      {/* Inner Ring - Gentle Light Sources */}
      <div className="astrology-ring inner-ring">
        <svg viewBox="0 0 180 180" className="light-sources">
          {/* Central core light */}
          <defs>
            <radialGradient id="coreLight" cx="50%" cy="50%" r="50%">
              <stop offset="0%" stopColor="rgba(255,255,255,0.8)" stopOpacity="1"/>
              <stop offset="30%" stopColor="rgba(255,255,255,0.6)" stopOpacity="0.8"/>
              <stop offset="60%" stopColor="rgba(255,255,255,0.4)" stopOpacity="0.5"/>
              <stop offset="100%" stopColor="transparent" stopOpacity="0"/>
            </radialGradient>
            
            <radialGradient id="lightOrb1" cx="50%" cy="50%" r="50%">
              <stop offset="0%" stopColor="rgba(255,255,255,0.9)" stopOpacity="1"/>
              <stop offset="50%" stopColor="rgba(255,255,255,0.5)" stopOpacity="0.6"/>
              <stop offset="100%" stopColor="transparent" stopOpacity="0"/>
            </radialGradient>
            
            <radialGradient id="lightOrb2" cx="50%" cy="50%" r="50%">
              <stop offset="0%" stopColor="rgba(255,255,255,0.9)" stopOpacity="1"/>
              <stop offset="50%" stopColor="rgba(255,255,255,0.5)" stopOpacity="0.6"/>
              <stop offset="100%" stopColor="transparent" stopOpacity="0"/>
            </radialGradient>
            
            <radialGradient id="lightOrb3" cx="50%" cy="50%" r="50%">
              <stop offset="0%" stopColor="rgba(255,255,255,0.9)" stopOpacity="1"/>
              <stop offset="50%" stopColor="rgba(255,255,255,0.5)" stopOpacity="0.6"/>
              <stop offset="100%" stopColor="transparent" stopOpacity="0"/>
            </radialGradient>
          </defs>
          
          {/* Gentle light sources positioned around the circle */}
          <g className="light-orbs">
            <circle cx="90" cy="30" r="12" fill="url(#lightOrb1)" opacity="0.8"/>
            <circle cx="150" cy="90" r="10" fill="url(#lightOrb2)" opacity="0.7"/>
            <circle cx="90" cy="150" r="11" fill="url(#lightOrb3)" opacity="0.75"/>
            <circle cx="30" cy="90" r="9" fill="url(#lightOrb1)" opacity="0.6"/>
            
            <circle cx="125" cy="55" r="8" fill="url(#lightOrb2)" opacity="0.5"/>
            <circle cx="125" cy="125" r="7" fill="url(#lightOrb3)" opacity="0.55"/>
            <circle cx="55" cy="125" r="9" fill="url(#lightOrb1)" opacity="0.6"/>
            <circle cx="55" cy="55" r="8" fill="url(#lightOrb2)" opacity="0.5"/>
          </g>
          
          {/* Gentle connecting light rays */}
          <g className="light-rays" stroke="rgba(255,255,255,0.3)" strokeWidth="1" fill="none" opacity="0.6">
            <path d="M90,30 Q120,60 150,90 Q120,120 90,150 Q60,120 30,90 Q60,60 90,30"/>
            <path d="M90,50 Q110,70 130,90 Q110,110 90,130 Q70,110 50,90 Q70,70 90,50"/>
          </g>
          
          {/* Soft pulsing center */}
          <circle cx="90" cy="90" r="25" fill="url(#coreLight)" opacity="0.4" className="center-glow"/>
          
          {/* Floating light particles */}
          <g className="light-particles">
            <circle cx="75" cy="45" r="2" fill="rgba(255,255,255,0.8)" opacity="0.7"/>
            <circle cx="105" cy="65" r="1.5" fill="rgba(255,255,255,0.8)" opacity="0.6"/>
            <circle cx="135" cy="105" r="2" fill="rgba(255,255,255,0.8)" opacity="0.8"/>
            <circle cx="115" cy="135" r="1.5" fill="rgba(255,255,255,0.8)" opacity="0.5"/>
            <circle cx="75" cy="125" r="2" fill="rgba(255,255,255,0.8)" opacity="0.7"/>
            <circle cx="45" cy="105" r="1.5" fill="rgba(255,255,255,0.8)" opacity="0.6"/>
            <circle cx="65" cy="75" r="2" fill="rgba(255,255,255,0.8)" opacity="0.8"/>
            <circle cx="95" cy="45" r="1.5" fill="rgba(255,255,255,0.8)" opacity="0.5"/>
          </g>
        </svg>
      </div>

      {/* Central Core - All-Seeing Eye */}
      <div className="orb-core">
        <svg viewBox="0 0 60 60" className="all-seeing-eye">
          {/* Eye outline */}
          <path d="M10,30 C15,20 25,15 30,15 C35,15 45,20 50,30 C45,40 35,45 30,45 C25,45 15,40 10,30" 
                stroke="rgba(255,255,255,0.7)" strokeWidth="1.5" fill="rgba(255,255,255,0.1)"/>
          
          {/* Iris */}
          <circle cx="30" cy="30" r="8" stroke="rgba(255,255,255,0.7)" strokeWidth="1" fill="rgba(255,255,255,0.2)"/>
          
          {/* Pupil */}
          <circle cx="30" cy="30" r="4" fill="rgba(255,255,255,0.7)"/>
          
          {/* Light reflection */}
          <circle cx="32" cy="28" r="1.5" fill="rgba(255,255,255,0.9)"/>
          
          {/* Mystical rays */}
          <g stroke="rgba(255,255,255,0.5)" strokeWidth="0.8">
            <path d="M30,5 L30,12"/>
            <path d="M30,48 L30,55"/>
            <path d="M5,30 L12,30"/>
            <path d="M48,30 L55,30"/>
            <path d="M12,12 L17,17"/>
            <path d="M43,43 L48,48"/>
            <path d="M48,12 L43,17"/>
            <path d="M17,43 L12,48"/>
          </g>
        </svg>
      </div>
    </div>
  );
};

// Floating particles component
const FloatingParticles: React.FC = () => {
  const [particles, setParticles] = useState<Array<{id: number, left: number, delay: number}>>([]);

  useEffect(() => {
    const particleArray = [];
    for (let i = 0; i < 20; i++) {
      particleArray.push({
        id: i,
        left: Math.random() * 100,
        delay: Math.random() * 15
      });
    }
    setParticles(particleArray);
  }, []);

  return (
    <div className="floating-particles">
      {particles.map(particle => (
        <div
          key={particle.id}
          className="particle"
          style={{
            left: `${particle.left}%`,
            animationDelay: `${particle.delay}s`
          }}
        />
      ))}
    </div>
  );
};

// 통계 데이터 타입 정의
interface StatsData {
  tarotReadings: number;
  tarotCards: number;
  spreadTypes: number;
  aiSupport: string;
}

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [dailyCardRevealed, setDailyCardRevealed] = useState(false);
  const [dailyCard, setDailyCard] = useState<TarotCard | null>(null);
  const [isCardFlipping, setIsCardFlipping] = useState(false);
  const [statsData, setStatsData] = useState<StatsData>({
    tarotReadings: 10000,
    tarotCards: 78,
    spreadTypes: 22,
    aiSupport: '24/7'
  });
  const [isLoadingStats, setIsLoadingStats] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  // 통계 데이터 가져오기
  const fetchStatsData = async () => {
    if (isLoadingStats) return;
    
    setIsLoadingStats(true);
    try {
      const response = await fetch('/api/stats/overview');
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.stats) {
          setStatsData(data.stats);
        }
      } else {
        console.error('통계 데이터 가져오기 실패:', response.status);
      }
    } catch (error) {
      console.error('통계 데이터 가져오기 오류:', error);
    } finally {
      setIsLoadingStats(false);
    }
  };

  // 컴포넌트 마운트 시 통계 데이터 가져오기
  useEffect(() => {
    fetchStatsData();
  }, []);

  // 오늘의 카드 선택 (날짜 기반으로 고정)
  useEffect(() => {
    const today = new Date();
    const dayOfYear = Math.floor((today.getTime() - new Date(today.getFullYear(), 0, 0).getTime()) / (1000 * 60 * 60 * 24));
    const cardIndex = dayOfYear % tarotCardsData.length;
    setDailyCard(tarotCardsData[cardIndex]);
  }, []);

  const handleTarotReading = () => {
    navigate('/tarot-reading');
  };

  const handleFortuneReading = () => {
    navigate('/fortune');
  };

  const handleTarotCards = () => {
    navigate('/tarot-cards');
  };

  const handleTutorial = () => {
    navigate('/tarot-tutorial');
  };

  const revealDailyCard = () => {
    if (!dailyCard || isCardFlipping) return;
    
    setIsCardFlipping(true);
    setTimeout(() => {
      setDailyCardRevealed(true);
      setIsCardFlipping(false);
    }, 1000);
  };

  // 카드 메시지 생성
  const getDailyCardMessage = (card: TarotCard) => {
    const messages = {
      'major': [
        `오늘 ${card.name} 카드가 당신에게 전하는 메시지입니다.`,
        `${card.description}`,
        `이 카드의 에너지를 마음에 담고 하루를 시작해보세요.`
      ],
      'wands': [
        `완드 수트의 ${card.name}가 당신의 열정과 창의력을 일깨웁니다.`,
        `${card.description}`,
        `오늘은 새로운 도전에 대한 용기를 가져보세요.`
      ],
      'cups': [
        `컵 수트의 ${card.name}가 당신의 감정과 인간관계에 대해 말합니다.`,
        `${card.description}`,
        `마음의 소리에 귀 기울이며 하루를 보내세요.`
      ],
      'swords': [
        `소드 수트의 ${card.name}가 당신의 지혜와 명확한 사고를 돕습니다.`,
        `${card.description}`,
        `오늘은 논리적이고 냉정한 판단이 필요한 날입니다.`
      ],
      'pentacles': [
        `펜타클 수트의 ${card.name}가 현실적인 목표와 성취에 대해 조언합니다.`,
        `${card.description}`,
        `물질적, 실용적인 면에서 안정을 추구해보세요.`
      ]
    };
    
    const suitMessages = messages[card.suit || 'major'];
    return suitMessages.join(' ');
  };

  return (
    <main className="home-page">
      {/* Floating constellation background */}
      <div className="constellation-background">
        <ZodiacConstellation 
          constellation="aries" 
          className="floating-constellation constellation-1"
          style={{ position: 'absolute', top: '10%', left: '15%', width: '120px', height: '120px' }}
        />
        <ZodiacConstellation 
          constellation="leo" 
          className="floating-constellation constellation-2"
          style={{ position: 'absolute', top: '20%', right: '20%', width: '100px', height: '100px' }}
        />
        <ZodiacConstellation 
          constellation="libra" 
          className="floating-constellation constellation-3"
          style={{ position: 'absolute', bottom: '25%', left: '10%', width: '90px', height: '90px' }}
        />
        <ZodiacConstellation 
          constellation="scorpio" 
          className="floating-constellation constellation-4"
          style={{ position: 'absolute', bottom: '15%', right: '15%', width: '110px', height: '110px' }}
        />
      </div>

      {/* Floating particles */}
      <FloatingParticles />

      {/* Hero Section */}
      <section className="hero-modern">
        <div className="hero-content">
          <div className="mystical-time">
            <span className="current-time">
              {currentTime.toLocaleString('ko-KR', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                weekday: 'long'
              })}
            </span>
          </div>
          
          <h1 className="hero-title">
            <span className="title-line">우주의 메시지를</span>
            <span className="title-line gradient-text">당신에게</span>
          </h1>
          
          <p className="hero-subtitle">
            AI가 해석하는 신비로운 타로와 점성술의 세계<br/>
            당신만을 위한 개인화된 우주적 통찰을 경험하세요
          </p>

          <div className="hero-cta-buttons">
            <button className="cta-primary" onClick={handleTarotReading}>
              <span className="cta-icon">🔮</span>
              타로 리딩 시작하기
            </button>
            <button className="cta-secondary" onClick={handleFortuneReading}>
              <span className="cta-icon">⭐</span>
              오늘의 운세
            </button>
          </div>
        </div>

        <div className="hero-visual">
          <MysticalAstrologyOrb />
        </div>
      </section>

      {/* Featured Services */}
      <section className="services-modern">
        <div className="section-header">
          <h2 className="section-title">신비로운 서비스들</h2>
          <p className="section-subtitle">당신의 영혼이 찾고 있는 답을 발견하세요</p>
        </div>

        <div className="services-grid">
          <div className="service-card premium" onClick={handleTarotReading}>
            <div className="card-header">
              <div className="service-icon">🔮</div>
              <h3>AI 타로 리딩</h3>
            </div>
            <p>22가지 다양한 스프레드로 깊이 있는 타로 해석을 경험하세요. 빠르고 정확한 3카드 스프레드부터 복잡하지만 깊이있는 켈틱 크로스까지, 당신의 질문에 맞는 완벽한 답을 찾아드립니다.</p>
            <div className="card-footer">
              <span className="service-badge">인기</span>
              <span className="service-price">10 크레딧부터</span>
            </div>
          </div>

          <div className="service-card" onClick={handleFortuneReading}>
            <div className="card-header">
              <div className="service-icon">⭐</div>
              <h3>운세 & 점성술</h3>
            </div>
            <p>오늘의 운세부터 연간 운세까지, 별들이 전하는 메시지를 받아보세요. 매일 새로운 우주의 에너지와 조언을 경험할 수 있습니다.</p>
            <div className="card-footer">
              <span className="service-badge">일일 무료</span>
              <span className="service-price">무료</span>
            </div>
          </div>

          <div className="service-card" onClick={handleTarotCards}>
            <div className="card-header">
              <div className="service-icon">🃏</div>
              <h3>타로 카드 사전</h3>
            </div>
            <p>78장의 타로 카드 의미와 해석을 상세하게 학습하세요. 메이저 아르카나부터 마이너 아르카나까지 완벽한 가이드를 제공합니다.</p>
            <div className="card-footer">
              <span className="service-badge">학습</span>
              <span className="service-price">무료</span>
            </div>
          </div>

          <div className="service-card" onClick={handleTutorial}>
            <div className="card-header">
              <div className="service-icon">📚</div>
              <h3>타로 가이드</h3>
            </div>
            <p>타로의 기초부터 고급 해석까지, 체계적인 학습 가이드로 타로 마스터가 되어보세요. 실전 예제와 함께 학습할 수 있습니다.</p>
            <div className="card-footer">
              <span className="service-badge">교육</span>
              <span className="service-price">무료</span>
            </div>
          </div>
        </div>
      </section>

      {/* Daily Reading Section */}
      <section className="daily-reading-modern">
        <div className="daily-container">
          <h2 className="daily-title">오늘의 카드</h2>
          <p className="daily-subtitle">매일 새로운 우주의 메시지를 받아보세요</p>
          
          <div className="daily-card-area">
            {!dailyCardRevealed ? (
              <div className="card-container">
                <div className={`flip-card ${isCardFlipping ? 'flipped' : ''}`} onClick={revealDailyCard}>
                  <div className="flip-card-inner">
                    {/* 카드 뒷면 */}
                    <div className="flip-card-back">
                      <img 
                        src="/images/tarot/CardBacks.jpg" 
                        alt="타로 카드 뒷면" 
                        className="card-image"
                      />
                    </div>
                    {/* 카드 앞면 */}
                    <div className="flip-card-front">
                      {dailyCard && (
                        <>
                          <img 
                            src={`/images/tarot/${dailyCard.imageName}`} 
                            alt={dailyCard.name} 
                            className="card-image"
                            onError={(e) => {
                              console.error(`카드 이미지 로드 실패: ${dailyCard.imageName}`);
                              (e.target as HTMLImageElement).style.display = 'none';
                            }}
                          />
                          <div className="card-overlay">
                            <h3 className="card-name">{dailyCard.name}</h3>
                            <div className="card-suit-badge">
                              {dailyCard.suit === 'major' && '메이저 아르카나'}
                              {dailyCard.suit === 'wands' && '완드'}
                              {dailyCard.suit === 'cups' && '컵'}
                              {dailyCard.suit === 'swords' && '소드'}
                              {dailyCard.suit === 'pentacles' && '펜타클'}
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
                <p className="reveal-text">카드를 터치하여 오늘의 메시지를 확인하세요</p>
              </div>
            ) : (
              <div className="revealed-card">
                <div className="card-front-revealed">
                  {dailyCard && (
                    <>
                      <img 
                        src={`/images/tarot/${dailyCard.imageName}`} 
                        alt={dailyCard.name} 
                        className="card-front-image"
                        onError={(e) => {
                          console.error(`카드 이미지 로드 실패: ${dailyCard.imageName}`);
                          (e.target as HTMLImageElement).style.display = 'none';
                        }}
                      />
                      <div className="card-overlay">
                        <h3 className="card-name">{dailyCard.name}</h3>
                        <div className="card-suit-badge">
                          {dailyCard.suit === 'major' && '메이저 아르카나'}
                          {dailyCard.suit === 'wands' && '완드'}
                          {dailyCard.suit === 'cups' && '컵'}
                          {dailyCard.suit === 'swords' && '소드'}
                          {dailyCard.suit === 'pentacles' && '펜타클'}
                        </div>
                      </div>
                    </>
                  )}
                </div>
                <div className="card-message-container">
                  <div className="mystical-decoration">✧</div>
                  <p className="card-message">
                    {dailyCard && getDailyCardMessage(dailyCard)}
                  </p>
                  <div className="mystical-decoration">✧</div>
                </div>
                <button 
                  className="new-card-btn" 
                  onClick={() => {
                    setDailyCardRevealed(false);
                    setIsCardFlipping(false);
                    // 새로운 랜덤 카드 선택
                    const randomIndex = Math.floor(Math.random() * tarotCardsData.length);
                    setDailyCard(tarotCardsData[randomIndex]);
                  }}
                >
                  다른 카드 보기 🔮
                </button>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="stats-section">
        <div className="stats-container">
          <h2 className="stats-title">신뢰받는 우주적 가이드</h2>
          <div className="stats-grid">
            <div className="stat-item">
              <div className="stat-number">{statsData.tarotReadings.toLocaleString()}+</div>
              <div className="stat-label">타로 및 운세 리딩 수</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">{statsData.tarotCards}</div>
              <div className="stat-label">타로 카드</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">{statsData.spreadTypes}+</div>
              <div className="stat-label">스프레드 종류</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">{statsData.aiSupport}</div>
              <div className="stat-label">AI 상담 (연중무휴😉)</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Highlight */}
      <section className="features-highlight" style={{
        padding: '6rem 5%',
        position: 'relative',
        zIndex: 1,
        background: 'linear-gradient(180deg, transparent, rgba(255, 255, 255, 0.02))'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          textAlign: 'center' as const
        }}>
          <h2 style={{
            fontSize: '2.8rem',
            marginBottom: '3rem',
            background: 'linear-gradient(45deg, #4fc3f7, #ab47bc)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: 700
          }}>
            별과 타로의 특별한 특징
          </h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '2.5rem',
            marginTop: '4rem'
          }}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.08)',
              backdropFilter: 'blur(15px)',
              borderRadius: '20px',
              padding: '2.5rem',
              border: '1px solid rgba(255, 255, 255, 0.15)'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '1.5rem' }}>⚛</div>
              <h3 style={{ fontSize: '1.4rem', marginBottom: '1rem', color: 'white' }}>
                최첨단 AI 기술
              </h3>
              <p style={{ opacity: 0.85, lineHeight: 1.6, color: '#d1d9e0' }}>
              전문 타로 및 점성술 리딩 데이터를 깊이 학습한 AI를 통해, 한층 정확하고 심도 있는 타로 해석을 제공합니다.
              </p>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.08)',
              backdropFilter: 'blur(15px)',
              borderRadius: '20px',
              padding: '2.5rem',
              border: '1px solid rgba(255, 255, 255, 0.15)'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '1.5rem' }}>☪</div>
              <h3 style={{ fontSize: '1.4rem', marginBottom: '1rem', color: 'white' }}>
                개인화된 해석
              </h3>
              <p style={{ opacity: 0.85, lineHeight: 1.6, color: '#d1d9e0' }}>
                당신의 질문과 상황에 맞춰 개인화된 타로 해석과 조언을 제공합니다.
              </p>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.08)',
              backdropFilter: 'blur(15px)',
              borderRadius: '20px',
              padding: '2.5rem',
              border: '1px solid rgba(255, 255, 255, 0.15)'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '1.5rem' }}>✡</div>
              <h3 style={{ fontSize: '1.4rem', marginBottom: '1rem', color: 'white' }}>
                신비로운 경험
              </h3>
              <p style={{ opacity: 0.85, lineHeight: 1.6, color: '#d1d9e0' }}>
                아름다운 비주얼과 인터랙티브한 카드 애니메이션으로 진정한 타로 경험을 선사합니다.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="final-cta">
        <div className="cta-container">
          <h2 className="cta-title">당신의 우주적 여정을 시작하세요</h2>
          <p className="cta-description">
            AI와 함께 타로와 점성술의 신비로운 세계를 탐험해보세요.<br/>
            지금 바로 시작하여 당신만의 특별한 메시지를 받아보세요.
          </p>
          <div className="cta-buttons">
            <button className="btn-start" onClick={handleTarotReading}>
              지금 시작하기
            </button>
            {!user && (
              <button className="btn-signup" onClick={() => navigate('/register')}>
                무료 계정 만들기
              </button>
            )}
          </div>
        </div>
      </section>
    </main>
  );
};

export default HomePage; 