// script.js

const loginModal = document.getElementById('loginModal');
const registerModal = document.getElementById('registerModal');
const myInfoModal = document.getElementById('myInfoModal');

// Function to open a modal by its ID
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = "block";
        // Add class to body to prevent scrolling when modal is open
        document.body.classList.add('modal-open');
    }
}

// Function to close a modal by its ID
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = "none";
        // Remove class from body to allow scrolling
        document.body.classList.remove('modal-open');
    }
}

// Event listeners for header login/signup buttons
document.addEventListener('DOMContentLoaded', () => {
    const loginButtonInHeader = document.querySelector('header .user-menu .login-btn');
    const signupButtonInHeader = document.querySelector('header .user-menu .signup-btn');

    if (loginButtonInHeader) {
        loginButtonInHeader.addEventListener('click', (e) => {
            e.preventDefault(); // Prevent default anchor behavior
            openModal('loginModal');
        });
    }

    if (signupButtonInHeader) {
        signupButtonInHeader.addEventListener('click', (e) => {
            e.preventDefault();
            openModal('registerModal');
        });
    }

    // Event listeners for close buttons in modals
    const closeButtons = document.querySelectorAll('.modal .close-btn');
    closeButtons.forEach(button => {
        button.addEventListener('click', () => {
            const modal = button.closest('.modal');
            if (modal) {
                closeModal(modal.id);
            }
        });
    });

    // Close modal if user clicks outside of the modal content
    window.addEventListener('click', (event) => {
        if (event.target.classList.contains('modal')) {
            closeModal(event.target.id);
        }
    });

    // Handle Register Form Submission
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', async function(event) {
            event.preventDefault();

            const name = document.getElementById('registerName').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const passwordConfirm = document.getElementById('registerPasswordConfirm').value;

            if (!name || !email || !password || !passwordConfirm) {
                alert('모든 필드를 입력해주세요.');
                return;
            }
            if (password !== passwordConfirm) {
                alert('비밀번호가 일치하지 않습니다.');
                return;
            }
            // TODO: 좀 더 자세한 비밀번호 정책 검사 (길이, 특수문자 등)

            try {
                const response = await fetch('http://125.189.172.142:3000/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ name, email, password }),
                });

                const data = await response.json();

                if (response.status === 201) {
                    alert(data.message + ` 환영합니다! ${data.credits} 크레딧이 지급되었습니다.`);
                    // TODO: 회원가입 성공 후 자동 로그인 처리 또는 로그인 페이지로 안내
                    closeModal('registerModal');
                    openModal('loginModal'); // 로그인 모달 바로 띄우기 (선택)
                } else {
                    alert('회원가입 실패: ' + data.message);
                }
            } catch (error) {
                console.error('Register API Error:', error);
                alert('회원가입 중 오류가 발생했습니다.');
            }
        });
    }

    // TODO: Add event listeners for social login buttons
    // These will typically redirect to an OAuth provider or open a popup
}); 