import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

// ThreeCardsIcon 정의 (TarotTutorialPage.tsx에서 가져옴)
const ThreeCardsIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="icon" fill="currentColor" width="18" height="18">
    <g transform="translate(0 1)">
      <rect x="2" y="5" width="7" height="11" rx="1" ry="1" transform="rotate(-15 5.5 10.5)" fill="#d1c4e9"/>
      <rect x="8.5" y="4" width="7" height="11" rx="1" ry="1" fill="#b39ddb"/>
      <rect x="15" y="5" width="7" height="11" rx="1" ry="1" transform="rotate(15 18.5 10.5)" fill="#9575cd"/>
    </g>
  </svg>
);

// 헤더 토글 아이콘
const HeaderToggleIcon: React.FC<{ isCollapsed: boolean }> = ({ isCollapsed }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    viewBox="0 0 24 24" 
    className="header-toggle-icon" 
    fill="currentColor" 
    width="20" 
    height="20"
  >
    {isCollapsed ? (
      <path d="M7 14l5-5 5 5z"/>
    ) : (
      <path d="M7 10l5 5 5-5z"/>
    )}
  </svg>
);

interface HeaderProps {
  openLoginModal: () => void;
  openRegisterModal: () => void;
  openMyInfoModal: () => void;
}

const Header: React.FC<HeaderProps> = ({ openLoginModal, openRegisterModal, openMyInfoModal }) => {
  const { isLoggedIn, user, logout } = useAuth();
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);

  // 페이지 변경 시 스크롤을 맨 위로 이동
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, [location.pathname]);

  // 헤더 토글 함수
  const toggleHeader = () => {
    setIsCollapsed(!isCollapsed);
  };

  const handleLogout = () => {
    logout();
    alert('로그아웃 되었습니다.');
  };

  // 기본 이미지 스타일
  const creditIconStyle: React.CSSProperties = {
    width: '32px', // 이미지 크기 조절
    height: '32px',
    marginLeft: '2px',
    verticalAlign: 'middle' // 텍스트와 이미지 세로 정렬
  };

  return (
    <header className={`main-header ${isCollapsed ? 'collapsed' : ''}`}>
      <div className="header-content">
        <div className="logo">
          <h1><Link to="/">별과 타로</Link></h1>
        </div>
        
        <nav className={`main-nav ${isCollapsed ? 'hidden' : ''}`}>
          <ul>
            <li><Link to="/tarot-reading"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="icon" fill="currentColor" width="18" height="18"><path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/></svg> 타로점 보기</Link></li>
            <li><Link to="/fortune"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="icon" fill="currentColor" width="18" height="18"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg> 오늘/올해의 운세</Link></li>
            <li>
              <Link to="/tarot-reading">
                <ThreeCardsIcon /> 타로 스프레드
              </Link>
            </li>
            <li><Link to="/tarot-tutorial"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="icon" fill="currentColor" width="18" height="18"><path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/></svg> 타로 첫걸음</Link></li>
            <li><Link to="/tarot-cards"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="icon" fill="currentColor" width="18" height="18"><path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zM4 6h5v8H4V6zm6 0h4v8h-4V6zm5 0h5v8h-5V6z"/></svg> 타로 카드들</Link></li>
          </ul>
        </nav>
        
        <div className={`user-menu ${isCollapsed ? 'hidden' : ''}`}>
          {isLoggedIn && user ? (
            <>
              <span className="user-greeting">
                안녕하세요, <strong style={{ color: '#7E57C2' }}>{user.name}</strong>님! 
                (크레딧: <span style={{ color: '#FFB74D', fontWeight: 'bold' }}>
                  {user.credits}
                  <img src="/images/credit.png" alt="크레딧 아이콘" style={creditIconStyle} />
                </span>)
              </span>
              <a href="#" className="user-info-btn" style={{ marginLeft: '10px' }} onClick={(e) => { e.preventDefault(); openMyInfoModal(); }}>내 정보</a>
              <a href="#" className="logout-btn" onClick={(e) => { e.preventDefault(); handleLogout(); }}>
                로그아웃
              </a>
            </>
          ) : (
            <>
              <a href="#" className="login-btn" onClick={(e) => { e.preventDefault(); openLoginModal(); }}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="icon" fill="currentColor" width="16" height="16"><path d="M10 17l5-5-5-5v10z"/></svg>
                  로그인
              </a>
              <a href="#" className="signup-btn" onClick={(e) => { e.preventDefault(); openRegisterModal(); }}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="icon" fill="currentColor" width="16" height="16"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/></svg>
                  가입하기
              </a>
            </>
          )}
        </div>
      </div>
      
      {/* 헤더 토글 버튼 */}
      <button 
        className="header-toggle-btn"
        onClick={toggleHeader}
        aria-label={isCollapsed ? "헤더 펼치기" : "헤더 접기"}
      >
        <HeaderToggleIcon isCollapsed={isCollapsed} />
      </button>
    </header>
  );
};

export default Header; 