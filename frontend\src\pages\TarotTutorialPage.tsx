import React, { useState, useEffect, useRef } from 'react';
import './TarotTutorialPage.css'; // 페이지 스타일을 위한 CSS 파일 임포트
import ArcanaModal from '../components/ArcanaModal'; // 경로 수정
import { tarotCardsData } from '../data/tarotCardsData'; // 경로 수정
import type { TarotCard } from '../data/tarotCardsData'; // 경로 수정
import StepIndicator from '../components/StepIndicator'; // StepIndicator 임포트

// Floating particles component
const FloatingParticles: React.FC = () => {
  const [particles, setParticles] = useState<Array<{id: number, left: number, delay: number}>>([]);

  useEffect(() => {
    const particleArray = [];
    for (let i = 0; i < 15; i++) {
      particleArray.push({
        id: i,
        left: Math.random() * 100,
        delay: Math.random() * 15
      });
    }
    setParticles(particleArray);
  }, []);

  return (
    <div className="floating-particles" style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      pointerEvents: 'none',
      zIndex: 0
    }}>
      {particles.map(particle => (
        <div
          key={particle.id}
          style={{
            position: 'absolute',
            width: '2px',
            height: '2px',
            background: particle.id % 3 === 0 ? 'rgba(255, 138, 128, 0.7)' : 
                       particle.id % 3 === 1 ? 'rgba(79, 195, 247, 0.7)' : 'rgba(240, 147, 251, 0.7)',
            borderRadius: '50%',
            left: `${particle.left}%`,
            animation: `particleFloat 15s linear infinite`,
            animationDelay: `${particle.delay}s`,
            boxShadow: particle.id % 3 === 0 ? '0 0 8px rgba(255, 138, 128, 0.5)' : 
                      particle.id % 3 === 1 ? '0 0 8px rgba(79, 195, 247, 0.5)' : '0 0 8px rgba(240, 147, 251, 0.5)'
          }}
        />
      ))}
      <style>{`
        @keyframes particleFloat {
          0% {
            transform: translateY(100vh) scale(0);
            opacity: 0;
          }
          10% {
            opacity: 1;
          }
          90% {
            opacity: 1;
          }
          100% {
            transform: translateY(-100px) scale(1);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  );
};

// 새로운 SVG 아이콘 컴포넌트 정의
const MoonAndStarIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="section-heading-icon" fill="currentColor" width="1em" height="1em">
    <path d="M12.01 1.99c-4.41 0-7.99 3.59-7.99 8.01s3.58 8.01 7.99 8.01c2.41 0 4.57-1.07 6.07-2.79-.54-.01-1.08-.03-1.62-.07-3.53-.29-6.38-3.19-6.38-6.76s2.85-6.47 6.38-6.76c.54-.04 1.08-.06 1.62-.07C16.58 3.06 14.42 1.99 12.01 1.99zM19.5 9.5l-1.06 2.18-2.39.36 1.73 1.69-.41 2.38 2.13-1.12 2.13 1.12-.41-2.38 1.73-1.69-2.39-.36L19.5 9.5z"/>
  </svg>
);

const OpenBookIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="section-heading-icon" fill="currentColor" width="1em" height="1em">
    <path d="M18 2H6c-1.11 0-2 .9-2 2v16c0 1.11.89 2 2 2h12c1.11 0 2-.9 2-2V4c0-1.11-.89-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4zm12 14H8v-2h10v2zm0-4H8v-2h10v2zm0-4H8V6h10v2z"/>
  </svg>
);

const HeartSparkleIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="section-heading-icon" fill="currentColor" width="1em" height="1em">
    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
    <path d="M16.5 5.5l-.7.7.7.7.7-.7-.7-.7zm-9 0l-.7.7.7.7.7-.7-.7-.7zm4.5-2l-.45.9.98.14-.71.69.17.97L12 7l-.89.46.17-.97-.71-.69.98-.14z" fill="#FFD700" opacity="0.8"/>
  </svg>
);

const ThreeCardsIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="section-heading-icon" fill="currentColor" width="1em" height="1em">
    <g transform="translate(0 1)">
      <rect x="2" y="5" width="7" height="11" rx="1" ry="1" transform="rotate(-15 5.5 10.5)" fill="#d1c4e9"/>
      <rect x="8.5" y="4" width="7" height="11" rx="1" ry="1" fill="#b39ddb"/>
      <rect x="15" y="5" width="7" height="11" rx="1" ry="1" transform="rotate(15 18.5 10.5)" fill="#9575cd"/>
    </g>
  </svg>
);

const GuidingStarCompassIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="section-heading-icon" fill="currentColor" width="1em" height="1em">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8-8-3.59 8-8 8z"/>
    <path d="M12 5.5l1.21 2.45L16 8.5l-2.79 2.05.9 3.2L12 11.8l-2.11 1.95.9-3.2L8 8.5l2.79-.55L12 5.5zM12 12.5a.5.5 0 000 1 .5.5 0 000-1z" opacity="0.9"/>
    <path d="M12 4v1m0 14v1m8-9h-1M5 12H4" stroke="currentColor" strokeWidth="0.5" opacity="0.5"/>
  </svg>
);

// 스텝 정의
const tutorialSteps = [
  { id: 'intro', title: '시작하기' },
  { id: 'structure', title: '카드 구성' },
  { id: 'befriend', title: '친해지기' },
  { id: 'spreads', title: '카드 배열법' },
  { id: 'journey', title: '다음 여정' },
];

const TarotTutorialPage: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalCards, setModalCards] = useState<TarotCard[]>([]);
  const [drawnDailyCard, setDrawnDailyCard] = useState<TarotCard | null>(null);
  const [currentStepId, setCurrentStepId] = useState<string | null>(tutorialSteps[0].id); // 현재 활성화된 스텝 ID
  const [isCardDisappearing, setIsCardDisappearing] = useState(false); // 카드 사라지는 애니메이션 상태
  const [cardAreaKey, setCardAreaKey] = useState(0); // 카드 영역 리렌더링을 위한 키

  // 각 섹션에 대한 ref 생성
  const sectionRefs = useRef<Record<string, HTMLElement | null>>({});

  useEffect(() => {
    const observerOptions = {
      root: null, // viewport
      rootMargin: '-20% 0px -40% 0px', // 화면 상단 부근에 섹션이 올 때 활성화 (우측 네비게이터에 맞게 조정)
      threshold: 0.1, // 10% 정도 보이면 활성화
    };

    const observerCallback: IntersectionObserverCallback = (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setCurrentStepId(entry.target.id);
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);

    tutorialSteps.forEach(step => {
      const element = document.getElementById(step.id);
      if (element) {
        sectionRefs.current[step.id] = element;
        observer.observe(element);
      }
    });

    return () => {
      tutorialSteps.forEach(step => {
        if (sectionRefs.current[step.id]) {
          observer.unobserve(sectionRefs.current[step.id]!);
        }
      });
    };
  }, []);

  const handleStepClick = (stepId: string) => {
    const element = document.getElementById(stepId);
    if (element) {
      const headerOffset = 150; // 헤더 높이만 고려 (우측 네비게이터는 고정이므로)
      const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
      const offsetPosition = elementPosition - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
      // setCurrentStepId(stepId); // IntersectionObserver가 처리하도록 두거나, 즉시 반영 원하면 사용
    }
  };

  const openArcanaModal = (arcanaType: 'major' | 'minor') => {
    if (arcanaType === 'major') {
      setModalTitle('메이저 아르카나 카드 목록 🌟');
      setModalCards(tarotCardsData.filter((card: TarotCard) => card.suit === 'major')); // 타입 명시
    } else {
      setModalTitle('마이너 아르카나 카드 목록 🌿');
      // 마이너 아르카나는 슈트별로 그룹화하여 표시하거나, 일단 전체 리스트를 보여줄 수 있습니다.
      // 여기서는 슈트별로 구분된 데이터를 생성하여 전달합니다.
      const minorSuitsOrder: Array<'wands' | 'cups' | 'swords' | 'pentacles'> = ['wands', 'cups', 'swords', 'pentacles'];
      const minorCardsGrouped = minorSuitsOrder.flatMap(suit => 
        tarotCardsData.filter((card: TarotCard) => card.suit === suit) // 타입 명시
      );
      setModalCards(minorCardsGrouped);
    }
    setIsModalOpen(true);
    setDrawnDailyCard(null); // 다른 모달 열 때 오늘의 카드 숨김
    setIsCardDisappearing(false); // 다른 모달 열 때 애니메이션 상태 초기화
  };

  // 단일 카드 정보를 모달로 보여주는 함수
  const openSingleCardModal = (cardNameOrId: string) => {
    const foundCard = tarotCardsData.find(card => card.name.includes(cardNameOrId) || card.id === cardNameOrId);
    if (foundCard) {
      setModalTitle(`${foundCard.name} 카드 정보 🃏`);
      setModalCards([foundCard]);
      setIsModalOpen(true);
      setDrawnDailyCard(null); // 다른 모달 열 때 오늘의 카드 숨김
      setIsCardDisappearing(false); // 다른 모달 열 때 애니메이션 상태 초기화
    } else {
      alert('해당 카드를 찾을 수 없습니다.');
    }
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const drawNewCard = () => {
    const randomIndex = Math.floor(Math.random() * tarotCardsData.length);
    setDrawnDailyCard(tarotCardsData[randomIndex]);
    setIsCardDisappearing(false); // 새 카드 뽑고 등장 애니메이션 시작
    setCardAreaKey(prevKey => prevKey + 1); // 키 변경으로 .drawn-card-area 강제 리렌더링 (애니메이션 재실행)
  };

  const handleRedrawDailyCard = () => {
    if (drawnDailyCard) {
      setIsCardDisappearing(true); // 사라지는 애니메이션 시작
      // 애니메이션 시간(0.7초) 후 새 카드 뽑기
      setTimeout(() => {
        drawNewCard();
      }, 700); // CSS 애니메이션 지속 시간과 일치
    } else {
      drawNewCard(); // 카드가 없으면 바로 뽑기
    }
  };

  // 본문 내 카드 이름을 클릭 가능하게 만드는 헬퍼 컴포넌트
  const ClickableCardName: React.FC<{ cardName: string, children: React.ReactNode }> = ({ cardName, children }) => (
    <span 
      className="clickable-card-name-in-text"
      onClick={() => openSingleCardModal(cardName)}
      role="button"
      tabIndex={0}
      onKeyPress={(e) => e.key === 'Enter' && openSingleCardModal(cardName)}
    >
      {children}
    </span>
  );

  // 특정 카드 이미지 인라인으로 보여주는 헬퍼
  const InlineCardImage: React.FC<{ cardNameOrId: string }> = ({ cardNameOrId }) => {
    const foundCard = tarotCardsData.find(card => card.name.includes(cardNameOrId) || card.id === cardNameOrId);
    if (!foundCard) return null;
    return (
      <img 
        src={`/images/tarot/${foundCard.imageName}`} 
        alt={foundCard.name} 
        className="inline-tarot-card-image"
        title={foundCard.name} // 마우스 오버 시 카드 이름 표시
      />
    );
  }

  return (
    <>
      <StepIndicator steps={tutorialSteps} currentStepId={currentStepId} onStepClick={handleStepClick} />
      <div className="tarot-tutorial-page">
        {/* Floating particles background */}
        <FloatingParticles />
        
        <h2 className="page-title">쉽고 재미있는 타로 첫걸음</h2>
        <p>
          타로의 신비로운 세계에 오신 것을 환영합니다! 타로가 처음이라도 괜찮아요. <br/>한 걸음씩 천천히, 즐겁게 타로의 매력에 빠져보아요! ✨
        </p>

        <section id="intro" className="tutorial-section">
          <h3 className="section-heading"><MoonAndStarIcon /> 궁금증 해결사? 혹은 마음의 길잡이? 타로의 진짜 매력!</h3>
          <p>
            타로 카드의 세계에 발을 들인 당신, 혹시 이런 마음으로 오셨나요?<br /><br />
            <span className='soft-highlight'>'좋아하는 그 사람, 저에게도 마음이 있을까요?'</span> 하는 애틋한 짝사랑의 고민,<br />
            <span className='soft-highlight'>'앞으로 제 미래는 어떻게 될까요?'</span> 하는 막연한 불안감,<br />
            아니면 <span className='soft-highlight'>'과거의 내가 했던 선택, 정말 괜찮았을까?'</span> 하는 조용한 되새김.<br /><br />
            단순한 호기심으로 타로를 만나는 분들도 있지만, 많은 이들이 삶의 크고 작은 갈림길에서, 혹은 마음 둘 곳 없이 힘겨울 때 타로의 문을 두드립니다. 
            타로는 신비로운 그림 속에 <span className='soft-highlight'>오랜 지혜와 공감의 언어</span>를 담고 있어요.<br /><br />
            타로 카드는 마치 <span className='soft-highlight'>다정한 상담가</span>처럼 당신의 이야기에 귀 기울이고, 복잡하게 얽힌 생각의 실타래를 푸는 데 도움을 줄 수 있습니다. 
            <span className='soft-highlight'>스스로의 마음을 들여다보고 더 나은 선택을 할 수 있도록 지혜를 주는 친구</span>와 같죠. <br/>
            당신의 고민 속에 숨겨진 진짜 마음과 가능성을 발견하는 여정, 타로와 함께 시작해보세요!
          </p>
        </section>

        <section id="structure" className="tutorial-section">
          <h3 className="section-heading"><OpenBookIcon /> 타로 카드, 한눈에 보는 구성! ✨</h3>
          <p>
            타로 카드는 보통 총 78장으로 되어 있어요. 마치 한 권의 특별한 그림책 같죠? 이 카드들은 크게 두 그룹으로 나눌 수 있습니다.
          </p>
          <ul>
            <li className="list-item-sparkle">
              <strong 
                className="clickable-arcana-title"
                onClick={() => openArcanaModal('major')}
                role="button"
                tabIndex={0}
                onKeyPress={(e) => e.key === 'Enter' && openArcanaModal('major')}
              >
                🌟 메이저 아르카나 (Major Arcana):
              </strong> 총 22장의 카드로, 우리 인생의 중요한 순간이나 큰 변화, 혹은 특별한 교훈을 담고 있는 카드들이에요. 
              마치 이야기 속 주인공들처럼 각 카드마다 특별한 이름과 번호가 있답니다. <br/> (예: <ClickableCardName cardName="바보">0번 바보<InlineCardImage cardNameOrId="바보" /></ClickableCardName> 카드, <ClickableCardName cardName="마법사">1번 마법사<InlineCardImage cardNameOrId="마법사" /></ClickableCardName> 카드처럼요!)
            </li>
            <li className="list-item-sparkle">
              <strong 
                className="clickable-arcana-title"
                onClick={() => openArcanaModal('minor')}
                role="button"
                tabIndex={0}
                onKeyPress={(e) => e.key === 'Enter' && openArcanaModal('minor')}
              >
                🌿 마이너 아르카나 (Minor Arcana):
              </strong> 총 56장의 카드로, 우리가 매일 겪는 일상적인 감정, 생각, 사건들을 보여줘요. <br/>
              마이너 아르카나는 또다시 네 가지 종류의 이야기 묶음(슈트)으로 나뉘는데, 각 묶음마다 14장의 카드가 있어요:
              <ul>
                <li className="minor-arcana-item minor-arcana-wands"><strong>완드 (Wands) 슈트:</strong> 불처럼 뜨거운 열정, 새로운 아이디어, 활기찬 에너지를 나타내요.</li>
                <li className="minor-arcana-item minor-arcana-cups"><strong>컵 (Cups) 슈트:</strong> 물처럼 흐르는 감정, 사람들과의 관계, 사랑과 마음을 이야기해요.</li>
                <li className="minor-arcana-item minor-arcana-swords"><strong>소드 (Swords) 슈트:</strong> 바람처럼 날카로운 생각, 명확한 판단, 때로는 어려운 결정을 보여줘요.</li>
                <li className="minor-arcana-item minor-arcana-pentacles"><strong>펜타클 (Pentacles) 슈트:</strong> 땅처럼 단단한 현실적인 문제, 돈, 건강, 안정된 생활을 의미해요.</li>
              </ul>
              각 슈트에는 <ClickableCardName cardName="완드 에이스">1번(에이스)<InlineCardImage cardNameOrId="완드 에이스" /></ClickableCardName>부터 10번까지의 숫자 카드와, <ClickableCardName cardName="완드 페이지">페이지(시종)<InlineCardImage cardNameOrId="완드 페이지"/></ClickableCardName>, <ClickableCardName cardName="완드 나이트">나이트(기사)<InlineCardImage cardNameOrId="완드 나이트"/></ClickableCardName>, <ClickableCardName cardName="완드 퀸">퀸(여왕)<InlineCardImage cardNameOrId="완드 퀸"/></ClickableCardName>, <ClickableCardName cardName="완드 킹">킹(왕)<InlineCardImage cardNameOrId="완드 킹"/></ClickableCardName>이라는 특별한 인물 카드들이 있답니다.
              <p style={{fontSize: '0.9em', color: '#666', marginTop: '5px'}}><em>(클릭해서 마이너 카드 전체 목록을 확인해보세요!)</em></p>
            </li>
          </ul>
        </section>

        <section id="befriend" className="tutorial-section">
          <h3 className="section-heading"><HeartSparkleIcon /> 타로 카드와 친해지는 꿀팁!</h3>
          <p>
            타로 카드의 의미를 처음부터 다 외우려고 하면 너무 어렵게 느껴질 수 있어요. 괜찮아요, 그럴 필요 없답니다!<br/>
            그것보다는 카드 그림을 보면서 <span className="soft-highlight">"이 그림은 나에게 어떤 이야기를 하는 것 같지?"</span> 하고 마음 가는 대로 상상해보는 것이 훨씬 즐겁고 효과적이에요.
          </p>
          <ol>
            <li>
              <span className="list-item-intro-phrase">오늘의 카드 뽑아보기:</span> 매일 아침, 카드 한 장을 뽑아서 <span className="soft-highlight">"오늘 나에게 어떤 메시지를 줄까?"</span> 하고 가볍게 시작해보세요. 그 카드가 하루의 작은 길잡이가 되어줄 거예요. 아래 버튼으로 한번 체험해볼까요?
            </li>
            <div className="daily-card-draw-section">
              {drawnDailyCard && (
                <div 
                  key={cardAreaKey} // 애니메이션 재실행을 위한 키
                  className={`drawn-card-area ${isCardDisappearing ? 'disappearing' : ''}`}
                >
                  <p style={{fontSize: '1.1em', fontWeight: 'bold', marginBottom: '15px'}}>짜잔! 오늘의 카드가 도착했어요!</p>
                  <img 
                    src={`/images/tarot/${drawnDailyCard.imageName}`} 
                    alt={drawnDailyCard.name} 
                    className="drawn-card-image"
                  />
                  <p className="drawn-card-name">{drawnDailyCard.name}</p>
                  <p className="drawn-card-description">{drawnDailyCard.description}</p>
                  <button className="draw-button" onClick={handleRedrawDailyCard} style={{marginTop: '15px', fontSize: '0.9em', padding: '8px 15px'}} disabled={isCardDisappearing}>
                    {isCardDisappearing ? '사라지는 중... 😭' : '다시 뽑아볼래요! ✨'}
                  </button>
                </div>
              )}
              {!drawnDailyCard && !isCardDisappearing && ( // 처음 또는 사라진 후 버튼 표시
                <button className="draw-button" onClick={handleRedrawDailyCard}>
                  🌟 나의 첫 타로 카드 뽑아보기! 🌟
                </button>
              )}
            </div>
            <li>
              <span className="list-item-intro-phrase">그림책처럼 카드 살펴보기:</span> 카드의 <span className="soft-highlight">색깔</span>, <span className="soft-highlight">등장인물</span>, <span className="soft-highlight">배경</span>, <span className="soft-highlight">작은 소품 하나하나까지</span> 그림책을 보듯 찬찬히 살펴보세요. 어떤 느낌이 드는지, 어떤 생각이 떠오르는지 <span className="soft-highlight">자유롭게 적어보는 것도 좋아요.</span> 마치 <span className="soft-highlight">숨은그림찾기 하는 것처럼요!</span>
            </li>
            <li>
              <span className="list-item-intro-phrase">나만의 이야기 만들기:</span> 카드를 보고 떠오르는 생각이나 느낌을 <span className="soft-highlight">짧은 이야기로 만들어보세요.</span> 정답은 없으니 <span className="soft-highlight">자유롭게 상상력을 펼쳐보세요!</span> "이 카드의 주인공은 지금 어떤 기분일까?" 하고 상상을 해보는 것도 재미있어요.
            </li>
            <li>
              <span className="list-item-intro-phrase">작은 질문부터 시작하기:</span> <span className="soft-highlight">"오늘 내 기분은 어떨까?"</span> 또는 <span className="soft-highlight">"이 일을 어떻게 하면 좋을까?"</span> 처럼 <span className="soft-highlight">쉽고 간단한 질문</span>으로 타로와 대화를 시작해보세요. 타로 카드는 <span className="soft-highlight">언제나 여러분의 이야기를 들어줄 준비가 되어 있답니다.</span>
            </li>
            <li>
              <span className="list-item-intro-phrase">느낌 기록하기:</span> 카드를 뽑고 나서 어떤 느낌이 들었는지, 어떤 생각이 떠올랐는지 <span className="soft-highlight">간단하게 메모하는 습관</span>을 들여보세요. 나중에 다시 보면 나에 대해 <span className="soft-highlight">새로운 발견</span>을 할 수도 있고, 타로와 더 깊이 소통하는 데 도움이 될 거예요.
            </li>
          </ol>
          <p>어때요, 생각보다 어렵지 않죠? 무엇이든 중요한 건 즐기는 마음이에요!</p>
        </section>
        
        <section id="spreads" className="tutorial-section">
          <h3 className="section-heading"><ThreeCardsIcon /> 나만의 타로 이야기 만들기: 간단한 카드 배열법 (스프레드) 📖</h3>
          <p>
            스프레드는 궁금한 점에 대한 힌트를 얻기 위해 타로 카드를 특정 순서대로 펼쳐놓는 방법이에요. 카드를 어떻게 놓느냐에 따라 이야기가 달라진답니다! 
            마치 탐정이 단서를 모아 사건을 해결하는 것처럼, 우리도 카드를 배열해서 문제의 실마리를 찾아볼 거예요. 처음에는 쉽고 간단한 방법부터 시작해볼까요?
          </p>
          <ul>
            <li className="spread-item-one-card">
              <strong>원 카드 스프레드 (One-Card Spread):</strong> "하나만 골라봐!" 가장 쉽고 빠른 방법이에요. 카드 한 장 <InlineCardImage cardNameOrId="태양" /> 을 뽑아서 오늘의 메시지나 간단한 질문에 대한 답을 얻을 수 있어요. 핵심만 간단히! 
            </li>
            <li className="spread-item-three-card">
              <strong>쓰리 카드 스프레드 (Three-Card Spread):</strong> 세 장의 카드를 <InlineCardImage cardNameOrId="컵 에이스" /><InlineCardImage cardNameOrId="컵 2" /><InlineCardImage cardNameOrId="컵 3" /> 나란히 놓는 방법이에요. 시간의 흐름(과거-현재-미래)을 보거나, 어떤 상황에 대한 원인-과정-결과를 살펴보는 데 유용해요.
              <br/>예를 들어 이렇게 해석해볼 수 있어요:
              <ul>
                  <li><strong>첫 번째 카드:</strong> 과거의 상황 또는 지금 일의 시작점 (어떤 일이 있었을까?)</li>
                  <li><strong>두 번째 카드:</strong> 현재 내가 처한 상황 또는 어떻게 행동하면 좋을지에 대한 힌트 (지금 나는 뭘 해야 할까?)</li>
                  <li><strong>세 번째 카드:</strong> 앞으로 예상되는 결과 또는 이 상황의 마무리 (그래서 어떻게 될까?)</li>
              </ul>
            </li>
          </ul>
          <p>
            세상에는 정말 다양하고 신기한 스프레드 방법들이 많지만, 처음에는 이처럼 간단한 방법으로 카드와 충분히 친해지는 것이 중요해요! 천천히, 하나씩 정복해나가면 된답니다. 💪
          </p>
        </section>

        <section id="journey" className="tutorial-section">
          <h3 className="section-heading"><GuidingStarCompassIcon /> 이제, 타로와 함께 특별한 여정을 시작해볼까요?</h3>
          <p>
            타로 카드의 세계는 알면 알수록 신비롭고 재미있는 이야기로 가득하답니다.<br/>
            오늘 배운 내용들이 여러분의 타로 여정에 작은 등불이 되길 바라요. 
            부담 갖지 마시고, 저희 AI 타로 친구들과 함께 즐거운 타로 여행을 시작해보세요! 언제나 여러분을 응원할게요! 🥰
          </p>
        </section>

        <ArcanaModal 
          isOpen={isModalOpen} 
          onClose={closeModal} 
          title={modalTitle} 
          cards={modalCards} 
        />
      </div>
    </>
  );
};

export default TarotTutorialPage; 