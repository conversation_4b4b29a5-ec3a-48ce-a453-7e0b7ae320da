
/**
 * Client
**/

import * as runtime from '@prisma/client/runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model User
 * 
 */
export type User = $Result.DefaultSelection<Prisma.$UserPayload>
/**
 * Model UserFortuneCooldown
 * 
 */
export type UserFortuneCooldown = $Result.DefaultSelection<Prisma.$UserFortuneCooldownPayload>
/**
 * Model IPFortuneCooldown
 * 
 */
export type IPFortuneCooldown = $Result.DefaultSelection<Prisma.$IPFortuneCooldownPayload>
/**
 * Model TarotSpread
 * 
 */
export type TarotSpread = $Result.DefaultSelection<Prisma.$TarotSpreadPayload>
/**
 * Model TarotGlobalSettings
 * 
 */
export type TarotGlobalSettings = $Result.DefaultSelection<Prisma.$TarotGlobalSettingsPayload>
/**
 * Model TarotReading
 * 
 */
export type TarotReading = $Result.DefaultSelection<Prisma.$TarotReadingPayload>
/**
 * Model UserQuestionHistory
 * 
 */
export type UserQuestionHistory = $Result.DefaultSelection<Prisma.$UserQuestionHistoryPayload>

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Users
   * const users = await prisma.user.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.user`: Exposes CRUD operations for the **User** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.user.findMany()
    * ```
    */
  get user(): Prisma.UserDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.userFortuneCooldown`: Exposes CRUD operations for the **UserFortuneCooldown** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more UserFortuneCooldowns
    * const userFortuneCooldowns = await prisma.userFortuneCooldown.findMany()
    * ```
    */
  get userFortuneCooldown(): Prisma.UserFortuneCooldownDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.iPFortuneCooldown`: Exposes CRUD operations for the **IPFortuneCooldown** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more IPFortuneCooldowns
    * const iPFortuneCooldowns = await prisma.iPFortuneCooldown.findMany()
    * ```
    */
  get iPFortuneCooldown(): Prisma.IPFortuneCooldownDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.tarotSpread`: Exposes CRUD operations for the **TarotSpread** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more TarotSpreads
    * const tarotSpreads = await prisma.tarotSpread.findMany()
    * ```
    */
  get tarotSpread(): Prisma.TarotSpreadDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.tarotGlobalSettings`: Exposes CRUD operations for the **TarotGlobalSettings** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more TarotGlobalSettings
    * const tarotGlobalSettings = await prisma.tarotGlobalSettings.findMany()
    * ```
    */
  get tarotGlobalSettings(): Prisma.TarotGlobalSettingsDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.tarotReading`: Exposes CRUD operations for the **TarotReading** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more TarotReadings
    * const tarotReadings = await prisma.tarotReading.findMany()
    * ```
    */
  get tarotReading(): Prisma.TarotReadingDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.userQuestionHistory`: Exposes CRUD operations for the **UserQuestionHistory** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more UserQuestionHistories
    * const userQuestionHistories = await prisma.userQuestionHistory.findMany()
    * ```
    */
  get userQuestionHistory(): Prisma.UserQuestionHistoryDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.7.0
   * Query Engine version: 3cff47a7f5d65c3ea74883f1d736e41d68ce91ed
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    User: 'User',
    UserFortuneCooldown: 'UserFortuneCooldown',
    IPFortuneCooldown: 'IPFortuneCooldown',
    TarotSpread: 'TarotSpread',
    TarotGlobalSettings: 'TarotGlobalSettings',
    TarotReading: 'TarotReading',
    UserQuestionHistory: 'UserQuestionHistory'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "user" | "userFortuneCooldown" | "iPFortuneCooldown" | "tarotSpread" | "tarotGlobalSettings" | "tarotReading" | "userQuestionHistory"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      User: {
        payload: Prisma.$UserPayload<ExtArgs>
        fields: Prisma.UserFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findFirst: {
            args: Prisma.UserFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findMany: {
            args: Prisma.UserFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          create: {
            args: Prisma.UserCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          createMany: {
            args: Prisma.UserCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UserCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          delete: {
            args: Prisma.UserDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          update: {
            args: Prisma.UserUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          deleteMany: {
            args: Prisma.UserDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.UserUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          upsert: {
            args: Prisma.UserUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          aggregate: {
            args: Prisma.UserAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUser>
          }
          groupBy: {
            args: Prisma.UserGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserCountArgs<ExtArgs>
            result: $Utils.Optional<UserCountAggregateOutputType> | number
          }
        }
      }
      UserFortuneCooldown: {
        payload: Prisma.$UserFortuneCooldownPayload<ExtArgs>
        fields: Prisma.UserFortuneCooldownFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserFortuneCooldownFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFortuneCooldownPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserFortuneCooldownFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFortuneCooldownPayload>
          }
          findFirst: {
            args: Prisma.UserFortuneCooldownFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFortuneCooldownPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserFortuneCooldownFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFortuneCooldownPayload>
          }
          findMany: {
            args: Prisma.UserFortuneCooldownFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFortuneCooldownPayload>[]
          }
          create: {
            args: Prisma.UserFortuneCooldownCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFortuneCooldownPayload>
          }
          createMany: {
            args: Prisma.UserFortuneCooldownCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UserFortuneCooldownCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFortuneCooldownPayload>[]
          }
          delete: {
            args: Prisma.UserFortuneCooldownDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFortuneCooldownPayload>
          }
          update: {
            args: Prisma.UserFortuneCooldownUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFortuneCooldownPayload>
          }
          deleteMany: {
            args: Prisma.UserFortuneCooldownDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserFortuneCooldownUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.UserFortuneCooldownUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFortuneCooldownPayload>[]
          }
          upsert: {
            args: Prisma.UserFortuneCooldownUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFortuneCooldownPayload>
          }
          aggregate: {
            args: Prisma.UserFortuneCooldownAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUserFortuneCooldown>
          }
          groupBy: {
            args: Prisma.UserFortuneCooldownGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserFortuneCooldownGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserFortuneCooldownCountArgs<ExtArgs>
            result: $Utils.Optional<UserFortuneCooldownCountAggregateOutputType> | number
          }
        }
      }
      IPFortuneCooldown: {
        payload: Prisma.$IPFortuneCooldownPayload<ExtArgs>
        fields: Prisma.IPFortuneCooldownFieldRefs
        operations: {
          findUnique: {
            args: Prisma.IPFortuneCooldownFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$IPFortuneCooldownPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.IPFortuneCooldownFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$IPFortuneCooldownPayload>
          }
          findFirst: {
            args: Prisma.IPFortuneCooldownFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$IPFortuneCooldownPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.IPFortuneCooldownFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$IPFortuneCooldownPayload>
          }
          findMany: {
            args: Prisma.IPFortuneCooldownFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$IPFortuneCooldownPayload>[]
          }
          create: {
            args: Prisma.IPFortuneCooldownCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$IPFortuneCooldownPayload>
          }
          createMany: {
            args: Prisma.IPFortuneCooldownCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.IPFortuneCooldownCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$IPFortuneCooldownPayload>[]
          }
          delete: {
            args: Prisma.IPFortuneCooldownDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$IPFortuneCooldownPayload>
          }
          update: {
            args: Prisma.IPFortuneCooldownUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$IPFortuneCooldownPayload>
          }
          deleteMany: {
            args: Prisma.IPFortuneCooldownDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.IPFortuneCooldownUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.IPFortuneCooldownUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$IPFortuneCooldownPayload>[]
          }
          upsert: {
            args: Prisma.IPFortuneCooldownUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$IPFortuneCooldownPayload>
          }
          aggregate: {
            args: Prisma.IPFortuneCooldownAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateIPFortuneCooldown>
          }
          groupBy: {
            args: Prisma.IPFortuneCooldownGroupByArgs<ExtArgs>
            result: $Utils.Optional<IPFortuneCooldownGroupByOutputType>[]
          }
          count: {
            args: Prisma.IPFortuneCooldownCountArgs<ExtArgs>
            result: $Utils.Optional<IPFortuneCooldownCountAggregateOutputType> | number
          }
        }
      }
      TarotSpread: {
        payload: Prisma.$TarotSpreadPayload<ExtArgs>
        fields: Prisma.TarotSpreadFieldRefs
        operations: {
          findUnique: {
            args: Prisma.TarotSpreadFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotSpreadPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.TarotSpreadFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotSpreadPayload>
          }
          findFirst: {
            args: Prisma.TarotSpreadFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotSpreadPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.TarotSpreadFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotSpreadPayload>
          }
          findMany: {
            args: Prisma.TarotSpreadFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotSpreadPayload>[]
          }
          create: {
            args: Prisma.TarotSpreadCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotSpreadPayload>
          }
          createMany: {
            args: Prisma.TarotSpreadCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.TarotSpreadCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotSpreadPayload>[]
          }
          delete: {
            args: Prisma.TarotSpreadDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotSpreadPayload>
          }
          update: {
            args: Prisma.TarotSpreadUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotSpreadPayload>
          }
          deleteMany: {
            args: Prisma.TarotSpreadDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.TarotSpreadUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.TarotSpreadUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotSpreadPayload>[]
          }
          upsert: {
            args: Prisma.TarotSpreadUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotSpreadPayload>
          }
          aggregate: {
            args: Prisma.TarotSpreadAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateTarotSpread>
          }
          groupBy: {
            args: Prisma.TarotSpreadGroupByArgs<ExtArgs>
            result: $Utils.Optional<TarotSpreadGroupByOutputType>[]
          }
          count: {
            args: Prisma.TarotSpreadCountArgs<ExtArgs>
            result: $Utils.Optional<TarotSpreadCountAggregateOutputType> | number
          }
        }
      }
      TarotGlobalSettings: {
        payload: Prisma.$TarotGlobalSettingsPayload<ExtArgs>
        fields: Prisma.TarotGlobalSettingsFieldRefs
        operations: {
          findUnique: {
            args: Prisma.TarotGlobalSettingsFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotGlobalSettingsPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.TarotGlobalSettingsFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotGlobalSettingsPayload>
          }
          findFirst: {
            args: Prisma.TarotGlobalSettingsFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotGlobalSettingsPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.TarotGlobalSettingsFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotGlobalSettingsPayload>
          }
          findMany: {
            args: Prisma.TarotGlobalSettingsFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotGlobalSettingsPayload>[]
          }
          create: {
            args: Prisma.TarotGlobalSettingsCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotGlobalSettingsPayload>
          }
          createMany: {
            args: Prisma.TarotGlobalSettingsCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.TarotGlobalSettingsCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotGlobalSettingsPayload>[]
          }
          delete: {
            args: Prisma.TarotGlobalSettingsDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotGlobalSettingsPayload>
          }
          update: {
            args: Prisma.TarotGlobalSettingsUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotGlobalSettingsPayload>
          }
          deleteMany: {
            args: Prisma.TarotGlobalSettingsDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.TarotGlobalSettingsUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.TarotGlobalSettingsUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotGlobalSettingsPayload>[]
          }
          upsert: {
            args: Prisma.TarotGlobalSettingsUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotGlobalSettingsPayload>
          }
          aggregate: {
            args: Prisma.TarotGlobalSettingsAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateTarotGlobalSettings>
          }
          groupBy: {
            args: Prisma.TarotGlobalSettingsGroupByArgs<ExtArgs>
            result: $Utils.Optional<TarotGlobalSettingsGroupByOutputType>[]
          }
          count: {
            args: Prisma.TarotGlobalSettingsCountArgs<ExtArgs>
            result: $Utils.Optional<TarotGlobalSettingsCountAggregateOutputType> | number
          }
        }
      }
      TarotReading: {
        payload: Prisma.$TarotReadingPayload<ExtArgs>
        fields: Prisma.TarotReadingFieldRefs
        operations: {
          findUnique: {
            args: Prisma.TarotReadingFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotReadingPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.TarotReadingFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotReadingPayload>
          }
          findFirst: {
            args: Prisma.TarotReadingFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotReadingPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.TarotReadingFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotReadingPayload>
          }
          findMany: {
            args: Prisma.TarotReadingFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotReadingPayload>[]
          }
          create: {
            args: Prisma.TarotReadingCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotReadingPayload>
          }
          createMany: {
            args: Prisma.TarotReadingCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.TarotReadingCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotReadingPayload>[]
          }
          delete: {
            args: Prisma.TarotReadingDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotReadingPayload>
          }
          update: {
            args: Prisma.TarotReadingUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotReadingPayload>
          }
          deleteMany: {
            args: Prisma.TarotReadingDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.TarotReadingUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.TarotReadingUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotReadingPayload>[]
          }
          upsert: {
            args: Prisma.TarotReadingUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TarotReadingPayload>
          }
          aggregate: {
            args: Prisma.TarotReadingAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateTarotReading>
          }
          groupBy: {
            args: Prisma.TarotReadingGroupByArgs<ExtArgs>
            result: $Utils.Optional<TarotReadingGroupByOutputType>[]
          }
          count: {
            args: Prisma.TarotReadingCountArgs<ExtArgs>
            result: $Utils.Optional<TarotReadingCountAggregateOutputType> | number
          }
        }
      }
      UserQuestionHistory: {
        payload: Prisma.$UserQuestionHistoryPayload<ExtArgs>
        fields: Prisma.UserQuestionHistoryFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserQuestionHistoryFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserQuestionHistoryPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserQuestionHistoryFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserQuestionHistoryPayload>
          }
          findFirst: {
            args: Prisma.UserQuestionHistoryFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserQuestionHistoryPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserQuestionHistoryFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserQuestionHistoryPayload>
          }
          findMany: {
            args: Prisma.UserQuestionHistoryFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserQuestionHistoryPayload>[]
          }
          create: {
            args: Prisma.UserQuestionHistoryCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserQuestionHistoryPayload>
          }
          createMany: {
            args: Prisma.UserQuestionHistoryCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UserQuestionHistoryCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserQuestionHistoryPayload>[]
          }
          delete: {
            args: Prisma.UserQuestionHistoryDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserQuestionHistoryPayload>
          }
          update: {
            args: Prisma.UserQuestionHistoryUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserQuestionHistoryPayload>
          }
          deleteMany: {
            args: Prisma.UserQuestionHistoryDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserQuestionHistoryUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.UserQuestionHistoryUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserQuestionHistoryPayload>[]
          }
          upsert: {
            args: Prisma.UserQuestionHistoryUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserQuestionHistoryPayload>
          }
          aggregate: {
            args: Prisma.UserQuestionHistoryAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUserQuestionHistory>
          }
          groupBy: {
            args: Prisma.UserQuestionHistoryGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserQuestionHistoryGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserQuestionHistoryCountArgs<ExtArgs>
            result: $Utils.Optional<UserQuestionHistoryCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    user?: UserOmit
    userFortuneCooldown?: UserFortuneCooldownOmit
    iPFortuneCooldown?: IPFortuneCooldownOmit
    tarotSpread?: TarotSpreadOmit
    tarotGlobalSettings?: TarotGlobalSettingsOmit
    tarotReading?: TarotReadingOmit
    userQuestionHistory?: UserQuestionHistoryOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type UserCountOutputType
   */

  export type UserCountOutputType = {
    cooldowns: number
    tarotReadings: number
    questionHistory: number
  }

  export type UserCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    cooldowns?: boolean | UserCountOutputTypeCountCooldownsArgs
    tarotReadings?: boolean | UserCountOutputTypeCountTarotReadingsArgs
    questionHistory?: boolean | UserCountOutputTypeCountQuestionHistoryArgs
  }

  // Custom InputTypes
  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserCountOutputType
     */
    select?: UserCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountCooldownsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserFortuneCooldownWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountTarotReadingsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: TarotReadingWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountQuestionHistoryArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserQuestionHistoryWhereInput
  }


  /**
   * Models
   */

  /**
   * Model User
   */

  export type AggregateUser = {
    _count: UserCountAggregateOutputType | null
    _avg: UserAvgAggregateOutputType | null
    _sum: UserSumAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  export type UserAvgAggregateOutputType = {
    credits: number | null
  }

  export type UserSumAggregateOutputType = {
    credits: number | null
  }

  export type UserMinAggregateOutputType = {
    id: string | null
    email: string | null
    hashedPassword: string | null
    name: string | null
    credits: number | null
    lastCreditDeduction: Date | null
    googleId: string | null
    kakaoId: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserMaxAggregateOutputType = {
    id: string | null
    email: string | null
    hashedPassword: string | null
    name: string | null
    credits: number | null
    lastCreditDeduction: Date | null
    googleId: string | null
    kakaoId: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserCountAggregateOutputType = {
    id: number
    email: number
    hashedPassword: number
    name: number
    credits: number
    lastCreditDeduction: number
    googleId: number
    kakaoId: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type UserAvgAggregateInputType = {
    credits?: true
  }

  export type UserSumAggregateInputType = {
    credits?: true
  }

  export type UserMinAggregateInputType = {
    id?: true
    email?: true
    hashedPassword?: true
    name?: true
    credits?: true
    lastCreditDeduction?: true
    googleId?: true
    kakaoId?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserMaxAggregateInputType = {
    id?: true
    email?: true
    hashedPassword?: true
    name?: true
    credits?: true
    lastCreditDeduction?: true
    googleId?: true
    kakaoId?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserCountAggregateInputType = {
    id?: true
    email?: true
    hashedPassword?: true
    name?: true
    credits?: true
    lastCreditDeduction?: true
    googleId?: true
    kakaoId?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type UserAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which User to aggregate.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Users
    **/
    _count?: true | UserCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: UserAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: UserSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserMaxAggregateInputType
  }

  export type GetUserAggregateType<T extends UserAggregateArgs> = {
        [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUser[P]>
      : GetScalarType<T[P], AggregateUser[P]>
  }




  export type UserGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserWhereInput
    orderBy?: UserOrderByWithAggregationInput | UserOrderByWithAggregationInput[]
    by: UserScalarFieldEnum[] | UserScalarFieldEnum
    having?: UserScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserCountAggregateInputType | true
    _avg?: UserAvgAggregateInputType
    _sum?: UserSumAggregateInputType
    _min?: UserMinAggregateInputType
    _max?: UserMaxAggregateInputType
  }

  export type UserGroupByOutputType = {
    id: string
    email: string
    hashedPassword: string | null
    name: string | null
    credits: number
    lastCreditDeduction: Date | null
    googleId: string | null
    kakaoId: string | null
    createdAt: Date
    updatedAt: Date
    _count: UserCountAggregateOutputType | null
    _avg: UserAvgAggregateOutputType | null
    _sum: UserSumAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserGroupByOutputType[P]>
            : GetScalarType<T[P], UserGroupByOutputType[P]>
        }
      >
    >


  export type UserSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    hashedPassword?: boolean
    name?: boolean
    credits?: boolean
    lastCreditDeduction?: boolean
    googleId?: boolean
    kakaoId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    cooldowns?: boolean | User$cooldownsArgs<ExtArgs>
    tarotReadings?: boolean | User$tarotReadingsArgs<ExtArgs>
    questionHistory?: boolean | User$questionHistoryArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user"]>

  export type UserSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    hashedPassword?: boolean
    name?: boolean
    credits?: boolean
    lastCreditDeduction?: boolean
    googleId?: boolean
    kakaoId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    hashedPassword?: boolean
    name?: boolean
    credits?: boolean
    lastCreditDeduction?: boolean
    googleId?: boolean
    kakaoId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectScalar = {
    id?: boolean
    email?: boolean
    hashedPassword?: boolean
    name?: boolean
    credits?: boolean
    lastCreditDeduction?: boolean
    googleId?: boolean
    kakaoId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type UserOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "email" | "hashedPassword" | "name" | "credits" | "lastCreditDeduction" | "googleId" | "kakaoId" | "createdAt" | "updatedAt", ExtArgs["result"]["user"]>
  export type UserInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    cooldowns?: boolean | User$cooldownsArgs<ExtArgs>
    tarotReadings?: boolean | User$tarotReadingsArgs<ExtArgs>
    questionHistory?: boolean | User$questionHistoryArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type UserIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type UserIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $UserPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "User"
    objects: {
      cooldowns: Prisma.$UserFortuneCooldownPayload<ExtArgs>[]
      tarotReadings: Prisma.$TarotReadingPayload<ExtArgs>[]
      questionHistory: Prisma.$UserQuestionHistoryPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      email: string
      hashedPassword: string | null
      name: string | null
      credits: number
      lastCreditDeduction: Date | null
      googleId: string | null
      kakaoId: string | null
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["user"]>
    composites: {}
  }

  type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = $Result.GetResult<Prisma.$UserPayload, S>

  type UserCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<UserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: UserCountAggregateInputType | true
    }

  export interface UserDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
    /**
     * Find zero or one User that matches the filter.
     * @param {UserFindUniqueArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserFindUniqueArgs>(args: SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one User that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserFindFirstArgs>(args?: SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Users
     * const users = await prisma.user.findMany()
     * 
     * // Get first 10 Users
     * const users = await prisma.user.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserFindManyArgs>(args?: SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a User.
     * @param {UserCreateArgs} args - Arguments to create a User.
     * @example
     * // Create one User
     * const User = await prisma.user.create({
     *   data: {
     *     // ... data to create a User
     *   }
     * })
     * 
     */
    create<T extends UserCreateArgs>(args: SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Users.
     * @param {UserCreateManyArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserCreateManyArgs>(args?: SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Users and returns the data saved in the database.
     * @param {UserCreateManyAndReturnArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Users and only return the `id`
     * const userWithIdOnly = await prisma.user.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UserCreateManyAndReturnArgs>(args?: SelectSubset<T, UserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a User.
     * @param {UserDeleteArgs} args - Arguments to delete one User.
     * @example
     * // Delete one User
     * const User = await prisma.user.delete({
     *   where: {
     *     // ... filter to delete one User
     *   }
     * })
     * 
     */
    delete<T extends UserDeleteArgs>(args: SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one User.
     * @param {UserUpdateArgs} args - Arguments to update one User.
     * @example
     * // Update one User
     * const user = await prisma.user.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserUpdateArgs>(args: SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Users.
     * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
     * @example
     * // Delete a few Users
     * const { count } = await prisma.user.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserDeleteManyArgs>(args?: SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserUpdateManyArgs>(args: SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users and returns the data updated in the database.
     * @param {UserUpdateManyAndReturnArgs} args - Arguments to update many Users.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Users and only return the `id`
     * const userWithIdOnly = await prisma.user.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends UserUpdateManyAndReturnArgs>(args: SelectSubset<T, UserUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one User.
     * @param {UserUpsertArgs} args - Arguments to update or create a User.
     * @example
     * // Update or create a User
     * const user = await prisma.user.upsert({
     *   create: {
     *     // ... data to create a User
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the User we want to update
     *   }
     * })
     */
    upsert<T extends UserUpsertArgs>(args: SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserCountArgs} args - Arguments to filter Users to count.
     * @example
     * // Count the number of Users
     * const count = await prisma.user.count({
     *   where: {
     *     // ... the filter for the Users we want to count
     *   }
     * })
    **/
    count<T extends UserCountArgs>(
      args?: Subset<T, UserCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserAggregateArgs>(args: Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

    /**
     * Group by User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserGroupByArgs['orderBy'] }
        : { orderBy?: UserGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the User model
   */
  readonly fields: UserFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for User.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    cooldowns<T extends User$cooldownsArgs<ExtArgs> = {}>(args?: Subset<T, User$cooldownsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserFortuneCooldownPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    tarotReadings<T extends User$tarotReadingsArgs<ExtArgs> = {}>(args?: Subset<T, User$tarotReadingsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TarotReadingPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    questionHistory<T extends User$questionHistoryArgs<ExtArgs> = {}>(args?: Subset<T, User$questionHistoryArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserQuestionHistoryPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the User model
   */
  interface UserFieldRefs {
    readonly id: FieldRef<"User", 'String'>
    readonly email: FieldRef<"User", 'String'>
    readonly hashedPassword: FieldRef<"User", 'String'>
    readonly name: FieldRef<"User", 'String'>
    readonly credits: FieldRef<"User", 'Int'>
    readonly lastCreditDeduction: FieldRef<"User", 'DateTime'>
    readonly googleId: FieldRef<"User", 'String'>
    readonly kakaoId: FieldRef<"User", 'String'>
    readonly createdAt: FieldRef<"User", 'DateTime'>
    readonly updatedAt: FieldRef<"User", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * User findUnique
   */
  export type UserFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findUniqueOrThrow
   */
  export type UserFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findFirst
   */
  export type UserFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findFirstOrThrow
   */
  export type UserFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findMany
   */
  export type UserFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User create
   */
  export type UserCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to create a User.
     */
    data: XOR<UserCreateInput, UserUncheckedCreateInput>
  }

  /**
   * User createMany
   */
  export type UserCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * User createManyAndReturn
   */
  export type UserCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * User update
   */
  export type UserUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to update a User.
     */
    data: XOR<UserUpdateInput, UserUncheckedUpdateInput>
    /**
     * Choose, which User to update.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User updateMany
   */
  export type UserUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * User updateManyAndReturn
   */
  export type UserUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * User upsert
   */
  export type UserUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The filter to search for the User to update in case it exists.
     */
    where: UserWhereUniqueInput
    /**
     * In case the User found by the `where` argument doesn't exist, create a new User with this data.
     */
    create: XOR<UserCreateInput, UserUncheckedCreateInput>
    /**
     * In case the User was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserUpdateInput, UserUncheckedUpdateInput>
  }

  /**
   * User delete
   */
  export type UserDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter which User to delete.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User deleteMany
   */
  export type UserDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Users to delete
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to delete.
     */
    limit?: number
  }

  /**
   * User.cooldowns
   */
  export type User$cooldownsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFortuneCooldown
     */
    select?: UserFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserFortuneCooldown
     */
    omit?: UserFortuneCooldownOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFortuneCooldownInclude<ExtArgs> | null
    where?: UserFortuneCooldownWhereInput
    orderBy?: UserFortuneCooldownOrderByWithRelationInput | UserFortuneCooldownOrderByWithRelationInput[]
    cursor?: UserFortuneCooldownWhereUniqueInput
    take?: number
    skip?: number
    distinct?: UserFortuneCooldownScalarFieldEnum | UserFortuneCooldownScalarFieldEnum[]
  }

  /**
   * User.tarotReadings
   */
  export type User$tarotReadingsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotReading
     */
    select?: TarotReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotReading
     */
    omit?: TarotReadingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TarotReadingInclude<ExtArgs> | null
    where?: TarotReadingWhereInput
    orderBy?: TarotReadingOrderByWithRelationInput | TarotReadingOrderByWithRelationInput[]
    cursor?: TarotReadingWhereUniqueInput
    take?: number
    skip?: number
    distinct?: TarotReadingScalarFieldEnum | TarotReadingScalarFieldEnum[]
  }

  /**
   * User.questionHistory
   */
  export type User$questionHistoryArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserQuestionHistory
     */
    select?: UserQuestionHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserQuestionHistory
     */
    omit?: UserQuestionHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserQuestionHistoryInclude<ExtArgs> | null
    where?: UserQuestionHistoryWhereInput
    orderBy?: UserQuestionHistoryOrderByWithRelationInput | UserQuestionHistoryOrderByWithRelationInput[]
    cursor?: UserQuestionHistoryWhereUniqueInput
    take?: number
    skip?: number
    distinct?: UserQuestionHistoryScalarFieldEnum | UserQuestionHistoryScalarFieldEnum[]
  }

  /**
   * User without action
   */
  export type UserDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
  }


  /**
   * Model UserFortuneCooldown
   */

  export type AggregateUserFortuneCooldown = {
    _count: UserFortuneCooldownCountAggregateOutputType | null
    _min: UserFortuneCooldownMinAggregateOutputType | null
    _max: UserFortuneCooldownMaxAggregateOutputType | null
  }

  export type UserFortuneCooldownMinAggregateOutputType = {
    id: string | null
    userId: string | null
    fortuneType: string | null
    cooldownExpiresAt: Date | null
    createdAt: Date | null
  }

  export type UserFortuneCooldownMaxAggregateOutputType = {
    id: string | null
    userId: string | null
    fortuneType: string | null
    cooldownExpiresAt: Date | null
    createdAt: Date | null
  }

  export type UserFortuneCooldownCountAggregateOutputType = {
    id: number
    userId: number
    fortuneType: number
    cooldownExpiresAt: number
    createdAt: number
    _all: number
  }


  export type UserFortuneCooldownMinAggregateInputType = {
    id?: true
    userId?: true
    fortuneType?: true
    cooldownExpiresAt?: true
    createdAt?: true
  }

  export type UserFortuneCooldownMaxAggregateInputType = {
    id?: true
    userId?: true
    fortuneType?: true
    cooldownExpiresAt?: true
    createdAt?: true
  }

  export type UserFortuneCooldownCountAggregateInputType = {
    id?: true
    userId?: true
    fortuneType?: true
    cooldownExpiresAt?: true
    createdAt?: true
    _all?: true
  }

  export type UserFortuneCooldownAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which UserFortuneCooldown to aggregate.
     */
    where?: UserFortuneCooldownWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserFortuneCooldowns to fetch.
     */
    orderBy?: UserFortuneCooldownOrderByWithRelationInput | UserFortuneCooldownOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserFortuneCooldownWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserFortuneCooldowns from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserFortuneCooldowns.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned UserFortuneCooldowns
    **/
    _count?: true | UserFortuneCooldownCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserFortuneCooldownMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserFortuneCooldownMaxAggregateInputType
  }

  export type GetUserFortuneCooldownAggregateType<T extends UserFortuneCooldownAggregateArgs> = {
        [P in keyof T & keyof AggregateUserFortuneCooldown]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUserFortuneCooldown[P]>
      : GetScalarType<T[P], AggregateUserFortuneCooldown[P]>
  }




  export type UserFortuneCooldownGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserFortuneCooldownWhereInput
    orderBy?: UserFortuneCooldownOrderByWithAggregationInput | UserFortuneCooldownOrderByWithAggregationInput[]
    by: UserFortuneCooldownScalarFieldEnum[] | UserFortuneCooldownScalarFieldEnum
    having?: UserFortuneCooldownScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserFortuneCooldownCountAggregateInputType | true
    _min?: UserFortuneCooldownMinAggregateInputType
    _max?: UserFortuneCooldownMaxAggregateInputType
  }

  export type UserFortuneCooldownGroupByOutputType = {
    id: string
    userId: string
    fortuneType: string
    cooldownExpiresAt: Date
    createdAt: Date
    _count: UserFortuneCooldownCountAggregateOutputType | null
    _min: UserFortuneCooldownMinAggregateOutputType | null
    _max: UserFortuneCooldownMaxAggregateOutputType | null
  }

  type GetUserFortuneCooldownGroupByPayload<T extends UserFortuneCooldownGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserFortuneCooldownGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserFortuneCooldownGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserFortuneCooldownGroupByOutputType[P]>
            : GetScalarType<T[P], UserFortuneCooldownGroupByOutputType[P]>
        }
      >
    >


  export type UserFortuneCooldownSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    fortuneType?: boolean
    cooldownExpiresAt?: boolean
    createdAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["userFortuneCooldown"]>

  export type UserFortuneCooldownSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    fortuneType?: boolean
    cooldownExpiresAt?: boolean
    createdAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["userFortuneCooldown"]>

  export type UserFortuneCooldownSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    fortuneType?: boolean
    cooldownExpiresAt?: boolean
    createdAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["userFortuneCooldown"]>

  export type UserFortuneCooldownSelectScalar = {
    id?: boolean
    userId?: boolean
    fortuneType?: boolean
    cooldownExpiresAt?: boolean
    createdAt?: boolean
  }

  export type UserFortuneCooldownOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "userId" | "fortuneType" | "cooldownExpiresAt" | "createdAt", ExtArgs["result"]["userFortuneCooldown"]>
  export type UserFortuneCooldownInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type UserFortuneCooldownIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type UserFortuneCooldownIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $UserFortuneCooldownPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "UserFortuneCooldown"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      userId: string
      fortuneType: string
      cooldownExpiresAt: Date
      createdAt: Date
    }, ExtArgs["result"]["userFortuneCooldown"]>
    composites: {}
  }

  type UserFortuneCooldownGetPayload<S extends boolean | null | undefined | UserFortuneCooldownDefaultArgs> = $Result.GetResult<Prisma.$UserFortuneCooldownPayload, S>

  type UserFortuneCooldownCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<UserFortuneCooldownFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: UserFortuneCooldownCountAggregateInputType | true
    }

  export interface UserFortuneCooldownDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['UserFortuneCooldown'], meta: { name: 'UserFortuneCooldown' } }
    /**
     * Find zero or one UserFortuneCooldown that matches the filter.
     * @param {UserFortuneCooldownFindUniqueArgs} args - Arguments to find a UserFortuneCooldown
     * @example
     * // Get one UserFortuneCooldown
     * const userFortuneCooldown = await prisma.userFortuneCooldown.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserFortuneCooldownFindUniqueArgs>(args: SelectSubset<T, UserFortuneCooldownFindUniqueArgs<ExtArgs>>): Prisma__UserFortuneCooldownClient<$Result.GetResult<Prisma.$UserFortuneCooldownPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one UserFortuneCooldown that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {UserFortuneCooldownFindUniqueOrThrowArgs} args - Arguments to find a UserFortuneCooldown
     * @example
     * // Get one UserFortuneCooldown
     * const userFortuneCooldown = await prisma.userFortuneCooldown.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserFortuneCooldownFindUniqueOrThrowArgs>(args: SelectSubset<T, UserFortuneCooldownFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserFortuneCooldownClient<$Result.GetResult<Prisma.$UserFortuneCooldownPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first UserFortuneCooldown that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFortuneCooldownFindFirstArgs} args - Arguments to find a UserFortuneCooldown
     * @example
     * // Get one UserFortuneCooldown
     * const userFortuneCooldown = await prisma.userFortuneCooldown.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserFortuneCooldownFindFirstArgs>(args?: SelectSubset<T, UserFortuneCooldownFindFirstArgs<ExtArgs>>): Prisma__UserFortuneCooldownClient<$Result.GetResult<Prisma.$UserFortuneCooldownPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first UserFortuneCooldown that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFortuneCooldownFindFirstOrThrowArgs} args - Arguments to find a UserFortuneCooldown
     * @example
     * // Get one UserFortuneCooldown
     * const userFortuneCooldown = await prisma.userFortuneCooldown.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserFortuneCooldownFindFirstOrThrowArgs>(args?: SelectSubset<T, UserFortuneCooldownFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserFortuneCooldownClient<$Result.GetResult<Prisma.$UserFortuneCooldownPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more UserFortuneCooldowns that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFortuneCooldownFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all UserFortuneCooldowns
     * const userFortuneCooldowns = await prisma.userFortuneCooldown.findMany()
     * 
     * // Get first 10 UserFortuneCooldowns
     * const userFortuneCooldowns = await prisma.userFortuneCooldown.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userFortuneCooldownWithIdOnly = await prisma.userFortuneCooldown.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserFortuneCooldownFindManyArgs>(args?: SelectSubset<T, UserFortuneCooldownFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserFortuneCooldownPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a UserFortuneCooldown.
     * @param {UserFortuneCooldownCreateArgs} args - Arguments to create a UserFortuneCooldown.
     * @example
     * // Create one UserFortuneCooldown
     * const UserFortuneCooldown = await prisma.userFortuneCooldown.create({
     *   data: {
     *     // ... data to create a UserFortuneCooldown
     *   }
     * })
     * 
     */
    create<T extends UserFortuneCooldownCreateArgs>(args: SelectSubset<T, UserFortuneCooldownCreateArgs<ExtArgs>>): Prisma__UserFortuneCooldownClient<$Result.GetResult<Prisma.$UserFortuneCooldownPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many UserFortuneCooldowns.
     * @param {UserFortuneCooldownCreateManyArgs} args - Arguments to create many UserFortuneCooldowns.
     * @example
     * // Create many UserFortuneCooldowns
     * const userFortuneCooldown = await prisma.userFortuneCooldown.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserFortuneCooldownCreateManyArgs>(args?: SelectSubset<T, UserFortuneCooldownCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many UserFortuneCooldowns and returns the data saved in the database.
     * @param {UserFortuneCooldownCreateManyAndReturnArgs} args - Arguments to create many UserFortuneCooldowns.
     * @example
     * // Create many UserFortuneCooldowns
     * const userFortuneCooldown = await prisma.userFortuneCooldown.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many UserFortuneCooldowns and only return the `id`
     * const userFortuneCooldownWithIdOnly = await prisma.userFortuneCooldown.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UserFortuneCooldownCreateManyAndReturnArgs>(args?: SelectSubset<T, UserFortuneCooldownCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserFortuneCooldownPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a UserFortuneCooldown.
     * @param {UserFortuneCooldownDeleteArgs} args - Arguments to delete one UserFortuneCooldown.
     * @example
     * // Delete one UserFortuneCooldown
     * const UserFortuneCooldown = await prisma.userFortuneCooldown.delete({
     *   where: {
     *     // ... filter to delete one UserFortuneCooldown
     *   }
     * })
     * 
     */
    delete<T extends UserFortuneCooldownDeleteArgs>(args: SelectSubset<T, UserFortuneCooldownDeleteArgs<ExtArgs>>): Prisma__UserFortuneCooldownClient<$Result.GetResult<Prisma.$UserFortuneCooldownPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one UserFortuneCooldown.
     * @param {UserFortuneCooldownUpdateArgs} args - Arguments to update one UserFortuneCooldown.
     * @example
     * // Update one UserFortuneCooldown
     * const userFortuneCooldown = await prisma.userFortuneCooldown.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserFortuneCooldownUpdateArgs>(args: SelectSubset<T, UserFortuneCooldownUpdateArgs<ExtArgs>>): Prisma__UserFortuneCooldownClient<$Result.GetResult<Prisma.$UserFortuneCooldownPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more UserFortuneCooldowns.
     * @param {UserFortuneCooldownDeleteManyArgs} args - Arguments to filter UserFortuneCooldowns to delete.
     * @example
     * // Delete a few UserFortuneCooldowns
     * const { count } = await prisma.userFortuneCooldown.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserFortuneCooldownDeleteManyArgs>(args?: SelectSubset<T, UserFortuneCooldownDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more UserFortuneCooldowns.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFortuneCooldownUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many UserFortuneCooldowns
     * const userFortuneCooldown = await prisma.userFortuneCooldown.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserFortuneCooldownUpdateManyArgs>(args: SelectSubset<T, UserFortuneCooldownUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more UserFortuneCooldowns and returns the data updated in the database.
     * @param {UserFortuneCooldownUpdateManyAndReturnArgs} args - Arguments to update many UserFortuneCooldowns.
     * @example
     * // Update many UserFortuneCooldowns
     * const userFortuneCooldown = await prisma.userFortuneCooldown.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more UserFortuneCooldowns and only return the `id`
     * const userFortuneCooldownWithIdOnly = await prisma.userFortuneCooldown.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends UserFortuneCooldownUpdateManyAndReturnArgs>(args: SelectSubset<T, UserFortuneCooldownUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserFortuneCooldownPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one UserFortuneCooldown.
     * @param {UserFortuneCooldownUpsertArgs} args - Arguments to update or create a UserFortuneCooldown.
     * @example
     * // Update or create a UserFortuneCooldown
     * const userFortuneCooldown = await prisma.userFortuneCooldown.upsert({
     *   create: {
     *     // ... data to create a UserFortuneCooldown
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the UserFortuneCooldown we want to update
     *   }
     * })
     */
    upsert<T extends UserFortuneCooldownUpsertArgs>(args: SelectSubset<T, UserFortuneCooldownUpsertArgs<ExtArgs>>): Prisma__UserFortuneCooldownClient<$Result.GetResult<Prisma.$UserFortuneCooldownPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of UserFortuneCooldowns.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFortuneCooldownCountArgs} args - Arguments to filter UserFortuneCooldowns to count.
     * @example
     * // Count the number of UserFortuneCooldowns
     * const count = await prisma.userFortuneCooldown.count({
     *   where: {
     *     // ... the filter for the UserFortuneCooldowns we want to count
     *   }
     * })
    **/
    count<T extends UserFortuneCooldownCountArgs>(
      args?: Subset<T, UserFortuneCooldownCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserFortuneCooldownCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a UserFortuneCooldown.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFortuneCooldownAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserFortuneCooldownAggregateArgs>(args: Subset<T, UserFortuneCooldownAggregateArgs>): Prisma.PrismaPromise<GetUserFortuneCooldownAggregateType<T>>

    /**
     * Group by UserFortuneCooldown.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFortuneCooldownGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserFortuneCooldownGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserFortuneCooldownGroupByArgs['orderBy'] }
        : { orderBy?: UserFortuneCooldownGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserFortuneCooldownGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserFortuneCooldownGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the UserFortuneCooldown model
   */
  readonly fields: UserFortuneCooldownFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for UserFortuneCooldown.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserFortuneCooldownClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the UserFortuneCooldown model
   */
  interface UserFortuneCooldownFieldRefs {
    readonly id: FieldRef<"UserFortuneCooldown", 'String'>
    readonly userId: FieldRef<"UserFortuneCooldown", 'String'>
    readonly fortuneType: FieldRef<"UserFortuneCooldown", 'String'>
    readonly cooldownExpiresAt: FieldRef<"UserFortuneCooldown", 'DateTime'>
    readonly createdAt: FieldRef<"UserFortuneCooldown", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * UserFortuneCooldown findUnique
   */
  export type UserFortuneCooldownFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFortuneCooldown
     */
    select?: UserFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserFortuneCooldown
     */
    omit?: UserFortuneCooldownOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFortuneCooldownInclude<ExtArgs> | null
    /**
     * Filter, which UserFortuneCooldown to fetch.
     */
    where: UserFortuneCooldownWhereUniqueInput
  }

  /**
   * UserFortuneCooldown findUniqueOrThrow
   */
  export type UserFortuneCooldownFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFortuneCooldown
     */
    select?: UserFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserFortuneCooldown
     */
    omit?: UserFortuneCooldownOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFortuneCooldownInclude<ExtArgs> | null
    /**
     * Filter, which UserFortuneCooldown to fetch.
     */
    where: UserFortuneCooldownWhereUniqueInput
  }

  /**
   * UserFortuneCooldown findFirst
   */
  export type UserFortuneCooldownFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFortuneCooldown
     */
    select?: UserFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserFortuneCooldown
     */
    omit?: UserFortuneCooldownOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFortuneCooldownInclude<ExtArgs> | null
    /**
     * Filter, which UserFortuneCooldown to fetch.
     */
    where?: UserFortuneCooldownWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserFortuneCooldowns to fetch.
     */
    orderBy?: UserFortuneCooldownOrderByWithRelationInput | UserFortuneCooldownOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for UserFortuneCooldowns.
     */
    cursor?: UserFortuneCooldownWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserFortuneCooldowns from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserFortuneCooldowns.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of UserFortuneCooldowns.
     */
    distinct?: UserFortuneCooldownScalarFieldEnum | UserFortuneCooldownScalarFieldEnum[]
  }

  /**
   * UserFortuneCooldown findFirstOrThrow
   */
  export type UserFortuneCooldownFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFortuneCooldown
     */
    select?: UserFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserFortuneCooldown
     */
    omit?: UserFortuneCooldownOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFortuneCooldownInclude<ExtArgs> | null
    /**
     * Filter, which UserFortuneCooldown to fetch.
     */
    where?: UserFortuneCooldownWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserFortuneCooldowns to fetch.
     */
    orderBy?: UserFortuneCooldownOrderByWithRelationInput | UserFortuneCooldownOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for UserFortuneCooldowns.
     */
    cursor?: UserFortuneCooldownWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserFortuneCooldowns from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserFortuneCooldowns.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of UserFortuneCooldowns.
     */
    distinct?: UserFortuneCooldownScalarFieldEnum | UserFortuneCooldownScalarFieldEnum[]
  }

  /**
   * UserFortuneCooldown findMany
   */
  export type UserFortuneCooldownFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFortuneCooldown
     */
    select?: UserFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserFortuneCooldown
     */
    omit?: UserFortuneCooldownOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFortuneCooldownInclude<ExtArgs> | null
    /**
     * Filter, which UserFortuneCooldowns to fetch.
     */
    where?: UserFortuneCooldownWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserFortuneCooldowns to fetch.
     */
    orderBy?: UserFortuneCooldownOrderByWithRelationInput | UserFortuneCooldownOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing UserFortuneCooldowns.
     */
    cursor?: UserFortuneCooldownWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserFortuneCooldowns from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserFortuneCooldowns.
     */
    skip?: number
    distinct?: UserFortuneCooldownScalarFieldEnum | UserFortuneCooldownScalarFieldEnum[]
  }

  /**
   * UserFortuneCooldown create
   */
  export type UserFortuneCooldownCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFortuneCooldown
     */
    select?: UserFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserFortuneCooldown
     */
    omit?: UserFortuneCooldownOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFortuneCooldownInclude<ExtArgs> | null
    /**
     * The data needed to create a UserFortuneCooldown.
     */
    data: XOR<UserFortuneCooldownCreateInput, UserFortuneCooldownUncheckedCreateInput>
  }

  /**
   * UserFortuneCooldown createMany
   */
  export type UserFortuneCooldownCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many UserFortuneCooldowns.
     */
    data: UserFortuneCooldownCreateManyInput | UserFortuneCooldownCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * UserFortuneCooldown createManyAndReturn
   */
  export type UserFortuneCooldownCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFortuneCooldown
     */
    select?: UserFortuneCooldownSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the UserFortuneCooldown
     */
    omit?: UserFortuneCooldownOmit<ExtArgs> | null
    /**
     * The data used to create many UserFortuneCooldowns.
     */
    data: UserFortuneCooldownCreateManyInput | UserFortuneCooldownCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFortuneCooldownIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * UserFortuneCooldown update
   */
  export type UserFortuneCooldownUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFortuneCooldown
     */
    select?: UserFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserFortuneCooldown
     */
    omit?: UserFortuneCooldownOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFortuneCooldownInclude<ExtArgs> | null
    /**
     * The data needed to update a UserFortuneCooldown.
     */
    data: XOR<UserFortuneCooldownUpdateInput, UserFortuneCooldownUncheckedUpdateInput>
    /**
     * Choose, which UserFortuneCooldown to update.
     */
    where: UserFortuneCooldownWhereUniqueInput
  }

  /**
   * UserFortuneCooldown updateMany
   */
  export type UserFortuneCooldownUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update UserFortuneCooldowns.
     */
    data: XOR<UserFortuneCooldownUpdateManyMutationInput, UserFortuneCooldownUncheckedUpdateManyInput>
    /**
     * Filter which UserFortuneCooldowns to update
     */
    where?: UserFortuneCooldownWhereInput
    /**
     * Limit how many UserFortuneCooldowns to update.
     */
    limit?: number
  }

  /**
   * UserFortuneCooldown updateManyAndReturn
   */
  export type UserFortuneCooldownUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFortuneCooldown
     */
    select?: UserFortuneCooldownSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the UserFortuneCooldown
     */
    omit?: UserFortuneCooldownOmit<ExtArgs> | null
    /**
     * The data used to update UserFortuneCooldowns.
     */
    data: XOR<UserFortuneCooldownUpdateManyMutationInput, UserFortuneCooldownUncheckedUpdateManyInput>
    /**
     * Filter which UserFortuneCooldowns to update
     */
    where?: UserFortuneCooldownWhereInput
    /**
     * Limit how many UserFortuneCooldowns to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFortuneCooldownIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * UserFortuneCooldown upsert
   */
  export type UserFortuneCooldownUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFortuneCooldown
     */
    select?: UserFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserFortuneCooldown
     */
    omit?: UserFortuneCooldownOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFortuneCooldownInclude<ExtArgs> | null
    /**
     * The filter to search for the UserFortuneCooldown to update in case it exists.
     */
    where: UserFortuneCooldownWhereUniqueInput
    /**
     * In case the UserFortuneCooldown found by the `where` argument doesn't exist, create a new UserFortuneCooldown with this data.
     */
    create: XOR<UserFortuneCooldownCreateInput, UserFortuneCooldownUncheckedCreateInput>
    /**
     * In case the UserFortuneCooldown was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserFortuneCooldownUpdateInput, UserFortuneCooldownUncheckedUpdateInput>
  }

  /**
   * UserFortuneCooldown delete
   */
  export type UserFortuneCooldownDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFortuneCooldown
     */
    select?: UserFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserFortuneCooldown
     */
    omit?: UserFortuneCooldownOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFortuneCooldownInclude<ExtArgs> | null
    /**
     * Filter which UserFortuneCooldown to delete.
     */
    where: UserFortuneCooldownWhereUniqueInput
  }

  /**
   * UserFortuneCooldown deleteMany
   */
  export type UserFortuneCooldownDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which UserFortuneCooldowns to delete
     */
    where?: UserFortuneCooldownWhereInput
    /**
     * Limit how many UserFortuneCooldowns to delete.
     */
    limit?: number
  }

  /**
   * UserFortuneCooldown without action
   */
  export type UserFortuneCooldownDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFortuneCooldown
     */
    select?: UserFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserFortuneCooldown
     */
    omit?: UserFortuneCooldownOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFortuneCooldownInclude<ExtArgs> | null
  }


  /**
   * Model IPFortuneCooldown
   */

  export type AggregateIPFortuneCooldown = {
    _count: IPFortuneCooldownCountAggregateOutputType | null
    _min: IPFortuneCooldownMinAggregateOutputType | null
    _max: IPFortuneCooldownMaxAggregateOutputType | null
  }

  export type IPFortuneCooldownMinAggregateOutputType = {
    id: string | null
    ipAddress: string | null
    fortuneType: string | null
    cooldownExpiresAt: Date | null
    createdAt: Date | null
  }

  export type IPFortuneCooldownMaxAggregateOutputType = {
    id: string | null
    ipAddress: string | null
    fortuneType: string | null
    cooldownExpiresAt: Date | null
    createdAt: Date | null
  }

  export type IPFortuneCooldownCountAggregateOutputType = {
    id: number
    ipAddress: number
    fortuneType: number
    cooldownExpiresAt: number
    createdAt: number
    _all: number
  }


  export type IPFortuneCooldownMinAggregateInputType = {
    id?: true
    ipAddress?: true
    fortuneType?: true
    cooldownExpiresAt?: true
    createdAt?: true
  }

  export type IPFortuneCooldownMaxAggregateInputType = {
    id?: true
    ipAddress?: true
    fortuneType?: true
    cooldownExpiresAt?: true
    createdAt?: true
  }

  export type IPFortuneCooldownCountAggregateInputType = {
    id?: true
    ipAddress?: true
    fortuneType?: true
    cooldownExpiresAt?: true
    createdAt?: true
    _all?: true
  }

  export type IPFortuneCooldownAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which IPFortuneCooldown to aggregate.
     */
    where?: IPFortuneCooldownWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of IPFortuneCooldowns to fetch.
     */
    orderBy?: IPFortuneCooldownOrderByWithRelationInput | IPFortuneCooldownOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: IPFortuneCooldownWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` IPFortuneCooldowns from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` IPFortuneCooldowns.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned IPFortuneCooldowns
    **/
    _count?: true | IPFortuneCooldownCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: IPFortuneCooldownMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: IPFortuneCooldownMaxAggregateInputType
  }

  export type GetIPFortuneCooldownAggregateType<T extends IPFortuneCooldownAggregateArgs> = {
        [P in keyof T & keyof AggregateIPFortuneCooldown]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateIPFortuneCooldown[P]>
      : GetScalarType<T[P], AggregateIPFortuneCooldown[P]>
  }




  export type IPFortuneCooldownGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: IPFortuneCooldownWhereInput
    orderBy?: IPFortuneCooldownOrderByWithAggregationInput | IPFortuneCooldownOrderByWithAggregationInput[]
    by: IPFortuneCooldownScalarFieldEnum[] | IPFortuneCooldownScalarFieldEnum
    having?: IPFortuneCooldownScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: IPFortuneCooldownCountAggregateInputType | true
    _min?: IPFortuneCooldownMinAggregateInputType
    _max?: IPFortuneCooldownMaxAggregateInputType
  }

  export type IPFortuneCooldownGroupByOutputType = {
    id: string
    ipAddress: string
    fortuneType: string
    cooldownExpiresAt: Date
    createdAt: Date
    _count: IPFortuneCooldownCountAggregateOutputType | null
    _min: IPFortuneCooldownMinAggregateOutputType | null
    _max: IPFortuneCooldownMaxAggregateOutputType | null
  }

  type GetIPFortuneCooldownGroupByPayload<T extends IPFortuneCooldownGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<IPFortuneCooldownGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof IPFortuneCooldownGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], IPFortuneCooldownGroupByOutputType[P]>
            : GetScalarType<T[P], IPFortuneCooldownGroupByOutputType[P]>
        }
      >
    >


  export type IPFortuneCooldownSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    ipAddress?: boolean
    fortuneType?: boolean
    cooldownExpiresAt?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["iPFortuneCooldown"]>

  export type IPFortuneCooldownSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    ipAddress?: boolean
    fortuneType?: boolean
    cooldownExpiresAt?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["iPFortuneCooldown"]>

  export type IPFortuneCooldownSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    ipAddress?: boolean
    fortuneType?: boolean
    cooldownExpiresAt?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["iPFortuneCooldown"]>

  export type IPFortuneCooldownSelectScalar = {
    id?: boolean
    ipAddress?: boolean
    fortuneType?: boolean
    cooldownExpiresAt?: boolean
    createdAt?: boolean
  }

  export type IPFortuneCooldownOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "ipAddress" | "fortuneType" | "cooldownExpiresAt" | "createdAt", ExtArgs["result"]["iPFortuneCooldown"]>

  export type $IPFortuneCooldownPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "IPFortuneCooldown"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      ipAddress: string
      fortuneType: string
      cooldownExpiresAt: Date
      createdAt: Date
    }, ExtArgs["result"]["iPFortuneCooldown"]>
    composites: {}
  }

  type IPFortuneCooldownGetPayload<S extends boolean | null | undefined | IPFortuneCooldownDefaultArgs> = $Result.GetResult<Prisma.$IPFortuneCooldownPayload, S>

  type IPFortuneCooldownCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<IPFortuneCooldownFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: IPFortuneCooldownCountAggregateInputType | true
    }

  export interface IPFortuneCooldownDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['IPFortuneCooldown'], meta: { name: 'IPFortuneCooldown' } }
    /**
     * Find zero or one IPFortuneCooldown that matches the filter.
     * @param {IPFortuneCooldownFindUniqueArgs} args - Arguments to find a IPFortuneCooldown
     * @example
     * // Get one IPFortuneCooldown
     * const iPFortuneCooldown = await prisma.iPFortuneCooldown.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends IPFortuneCooldownFindUniqueArgs>(args: SelectSubset<T, IPFortuneCooldownFindUniqueArgs<ExtArgs>>): Prisma__IPFortuneCooldownClient<$Result.GetResult<Prisma.$IPFortuneCooldownPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one IPFortuneCooldown that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {IPFortuneCooldownFindUniqueOrThrowArgs} args - Arguments to find a IPFortuneCooldown
     * @example
     * // Get one IPFortuneCooldown
     * const iPFortuneCooldown = await prisma.iPFortuneCooldown.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends IPFortuneCooldownFindUniqueOrThrowArgs>(args: SelectSubset<T, IPFortuneCooldownFindUniqueOrThrowArgs<ExtArgs>>): Prisma__IPFortuneCooldownClient<$Result.GetResult<Prisma.$IPFortuneCooldownPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first IPFortuneCooldown that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {IPFortuneCooldownFindFirstArgs} args - Arguments to find a IPFortuneCooldown
     * @example
     * // Get one IPFortuneCooldown
     * const iPFortuneCooldown = await prisma.iPFortuneCooldown.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends IPFortuneCooldownFindFirstArgs>(args?: SelectSubset<T, IPFortuneCooldownFindFirstArgs<ExtArgs>>): Prisma__IPFortuneCooldownClient<$Result.GetResult<Prisma.$IPFortuneCooldownPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first IPFortuneCooldown that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {IPFortuneCooldownFindFirstOrThrowArgs} args - Arguments to find a IPFortuneCooldown
     * @example
     * // Get one IPFortuneCooldown
     * const iPFortuneCooldown = await prisma.iPFortuneCooldown.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends IPFortuneCooldownFindFirstOrThrowArgs>(args?: SelectSubset<T, IPFortuneCooldownFindFirstOrThrowArgs<ExtArgs>>): Prisma__IPFortuneCooldownClient<$Result.GetResult<Prisma.$IPFortuneCooldownPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more IPFortuneCooldowns that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {IPFortuneCooldownFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all IPFortuneCooldowns
     * const iPFortuneCooldowns = await prisma.iPFortuneCooldown.findMany()
     * 
     * // Get first 10 IPFortuneCooldowns
     * const iPFortuneCooldowns = await prisma.iPFortuneCooldown.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const iPFortuneCooldownWithIdOnly = await prisma.iPFortuneCooldown.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends IPFortuneCooldownFindManyArgs>(args?: SelectSubset<T, IPFortuneCooldownFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$IPFortuneCooldownPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a IPFortuneCooldown.
     * @param {IPFortuneCooldownCreateArgs} args - Arguments to create a IPFortuneCooldown.
     * @example
     * // Create one IPFortuneCooldown
     * const IPFortuneCooldown = await prisma.iPFortuneCooldown.create({
     *   data: {
     *     // ... data to create a IPFortuneCooldown
     *   }
     * })
     * 
     */
    create<T extends IPFortuneCooldownCreateArgs>(args: SelectSubset<T, IPFortuneCooldownCreateArgs<ExtArgs>>): Prisma__IPFortuneCooldownClient<$Result.GetResult<Prisma.$IPFortuneCooldownPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many IPFortuneCooldowns.
     * @param {IPFortuneCooldownCreateManyArgs} args - Arguments to create many IPFortuneCooldowns.
     * @example
     * // Create many IPFortuneCooldowns
     * const iPFortuneCooldown = await prisma.iPFortuneCooldown.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends IPFortuneCooldownCreateManyArgs>(args?: SelectSubset<T, IPFortuneCooldownCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many IPFortuneCooldowns and returns the data saved in the database.
     * @param {IPFortuneCooldownCreateManyAndReturnArgs} args - Arguments to create many IPFortuneCooldowns.
     * @example
     * // Create many IPFortuneCooldowns
     * const iPFortuneCooldown = await prisma.iPFortuneCooldown.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many IPFortuneCooldowns and only return the `id`
     * const iPFortuneCooldownWithIdOnly = await prisma.iPFortuneCooldown.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends IPFortuneCooldownCreateManyAndReturnArgs>(args?: SelectSubset<T, IPFortuneCooldownCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$IPFortuneCooldownPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a IPFortuneCooldown.
     * @param {IPFortuneCooldownDeleteArgs} args - Arguments to delete one IPFortuneCooldown.
     * @example
     * // Delete one IPFortuneCooldown
     * const IPFortuneCooldown = await prisma.iPFortuneCooldown.delete({
     *   where: {
     *     // ... filter to delete one IPFortuneCooldown
     *   }
     * })
     * 
     */
    delete<T extends IPFortuneCooldownDeleteArgs>(args: SelectSubset<T, IPFortuneCooldownDeleteArgs<ExtArgs>>): Prisma__IPFortuneCooldownClient<$Result.GetResult<Prisma.$IPFortuneCooldownPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one IPFortuneCooldown.
     * @param {IPFortuneCooldownUpdateArgs} args - Arguments to update one IPFortuneCooldown.
     * @example
     * // Update one IPFortuneCooldown
     * const iPFortuneCooldown = await prisma.iPFortuneCooldown.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends IPFortuneCooldownUpdateArgs>(args: SelectSubset<T, IPFortuneCooldownUpdateArgs<ExtArgs>>): Prisma__IPFortuneCooldownClient<$Result.GetResult<Prisma.$IPFortuneCooldownPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more IPFortuneCooldowns.
     * @param {IPFortuneCooldownDeleteManyArgs} args - Arguments to filter IPFortuneCooldowns to delete.
     * @example
     * // Delete a few IPFortuneCooldowns
     * const { count } = await prisma.iPFortuneCooldown.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends IPFortuneCooldownDeleteManyArgs>(args?: SelectSubset<T, IPFortuneCooldownDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more IPFortuneCooldowns.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {IPFortuneCooldownUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many IPFortuneCooldowns
     * const iPFortuneCooldown = await prisma.iPFortuneCooldown.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends IPFortuneCooldownUpdateManyArgs>(args: SelectSubset<T, IPFortuneCooldownUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more IPFortuneCooldowns and returns the data updated in the database.
     * @param {IPFortuneCooldownUpdateManyAndReturnArgs} args - Arguments to update many IPFortuneCooldowns.
     * @example
     * // Update many IPFortuneCooldowns
     * const iPFortuneCooldown = await prisma.iPFortuneCooldown.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more IPFortuneCooldowns and only return the `id`
     * const iPFortuneCooldownWithIdOnly = await prisma.iPFortuneCooldown.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends IPFortuneCooldownUpdateManyAndReturnArgs>(args: SelectSubset<T, IPFortuneCooldownUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$IPFortuneCooldownPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one IPFortuneCooldown.
     * @param {IPFortuneCooldownUpsertArgs} args - Arguments to update or create a IPFortuneCooldown.
     * @example
     * // Update or create a IPFortuneCooldown
     * const iPFortuneCooldown = await prisma.iPFortuneCooldown.upsert({
     *   create: {
     *     // ... data to create a IPFortuneCooldown
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the IPFortuneCooldown we want to update
     *   }
     * })
     */
    upsert<T extends IPFortuneCooldownUpsertArgs>(args: SelectSubset<T, IPFortuneCooldownUpsertArgs<ExtArgs>>): Prisma__IPFortuneCooldownClient<$Result.GetResult<Prisma.$IPFortuneCooldownPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of IPFortuneCooldowns.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {IPFortuneCooldownCountArgs} args - Arguments to filter IPFortuneCooldowns to count.
     * @example
     * // Count the number of IPFortuneCooldowns
     * const count = await prisma.iPFortuneCooldown.count({
     *   where: {
     *     // ... the filter for the IPFortuneCooldowns we want to count
     *   }
     * })
    **/
    count<T extends IPFortuneCooldownCountArgs>(
      args?: Subset<T, IPFortuneCooldownCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], IPFortuneCooldownCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a IPFortuneCooldown.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {IPFortuneCooldownAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends IPFortuneCooldownAggregateArgs>(args: Subset<T, IPFortuneCooldownAggregateArgs>): Prisma.PrismaPromise<GetIPFortuneCooldownAggregateType<T>>

    /**
     * Group by IPFortuneCooldown.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {IPFortuneCooldownGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends IPFortuneCooldownGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: IPFortuneCooldownGroupByArgs['orderBy'] }
        : { orderBy?: IPFortuneCooldownGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, IPFortuneCooldownGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetIPFortuneCooldownGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the IPFortuneCooldown model
   */
  readonly fields: IPFortuneCooldownFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for IPFortuneCooldown.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__IPFortuneCooldownClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the IPFortuneCooldown model
   */
  interface IPFortuneCooldownFieldRefs {
    readonly id: FieldRef<"IPFortuneCooldown", 'String'>
    readonly ipAddress: FieldRef<"IPFortuneCooldown", 'String'>
    readonly fortuneType: FieldRef<"IPFortuneCooldown", 'String'>
    readonly cooldownExpiresAt: FieldRef<"IPFortuneCooldown", 'DateTime'>
    readonly createdAt: FieldRef<"IPFortuneCooldown", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * IPFortuneCooldown findUnique
   */
  export type IPFortuneCooldownFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the IPFortuneCooldown
     */
    select?: IPFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the IPFortuneCooldown
     */
    omit?: IPFortuneCooldownOmit<ExtArgs> | null
    /**
     * Filter, which IPFortuneCooldown to fetch.
     */
    where: IPFortuneCooldownWhereUniqueInput
  }

  /**
   * IPFortuneCooldown findUniqueOrThrow
   */
  export type IPFortuneCooldownFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the IPFortuneCooldown
     */
    select?: IPFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the IPFortuneCooldown
     */
    omit?: IPFortuneCooldownOmit<ExtArgs> | null
    /**
     * Filter, which IPFortuneCooldown to fetch.
     */
    where: IPFortuneCooldownWhereUniqueInput
  }

  /**
   * IPFortuneCooldown findFirst
   */
  export type IPFortuneCooldownFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the IPFortuneCooldown
     */
    select?: IPFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the IPFortuneCooldown
     */
    omit?: IPFortuneCooldownOmit<ExtArgs> | null
    /**
     * Filter, which IPFortuneCooldown to fetch.
     */
    where?: IPFortuneCooldownWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of IPFortuneCooldowns to fetch.
     */
    orderBy?: IPFortuneCooldownOrderByWithRelationInput | IPFortuneCooldownOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for IPFortuneCooldowns.
     */
    cursor?: IPFortuneCooldownWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` IPFortuneCooldowns from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` IPFortuneCooldowns.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of IPFortuneCooldowns.
     */
    distinct?: IPFortuneCooldownScalarFieldEnum | IPFortuneCooldownScalarFieldEnum[]
  }

  /**
   * IPFortuneCooldown findFirstOrThrow
   */
  export type IPFortuneCooldownFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the IPFortuneCooldown
     */
    select?: IPFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the IPFortuneCooldown
     */
    omit?: IPFortuneCooldownOmit<ExtArgs> | null
    /**
     * Filter, which IPFortuneCooldown to fetch.
     */
    where?: IPFortuneCooldownWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of IPFortuneCooldowns to fetch.
     */
    orderBy?: IPFortuneCooldownOrderByWithRelationInput | IPFortuneCooldownOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for IPFortuneCooldowns.
     */
    cursor?: IPFortuneCooldownWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` IPFortuneCooldowns from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` IPFortuneCooldowns.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of IPFortuneCooldowns.
     */
    distinct?: IPFortuneCooldownScalarFieldEnum | IPFortuneCooldownScalarFieldEnum[]
  }

  /**
   * IPFortuneCooldown findMany
   */
  export type IPFortuneCooldownFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the IPFortuneCooldown
     */
    select?: IPFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the IPFortuneCooldown
     */
    omit?: IPFortuneCooldownOmit<ExtArgs> | null
    /**
     * Filter, which IPFortuneCooldowns to fetch.
     */
    where?: IPFortuneCooldownWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of IPFortuneCooldowns to fetch.
     */
    orderBy?: IPFortuneCooldownOrderByWithRelationInput | IPFortuneCooldownOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing IPFortuneCooldowns.
     */
    cursor?: IPFortuneCooldownWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` IPFortuneCooldowns from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` IPFortuneCooldowns.
     */
    skip?: number
    distinct?: IPFortuneCooldownScalarFieldEnum | IPFortuneCooldownScalarFieldEnum[]
  }

  /**
   * IPFortuneCooldown create
   */
  export type IPFortuneCooldownCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the IPFortuneCooldown
     */
    select?: IPFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the IPFortuneCooldown
     */
    omit?: IPFortuneCooldownOmit<ExtArgs> | null
    /**
     * The data needed to create a IPFortuneCooldown.
     */
    data: XOR<IPFortuneCooldownCreateInput, IPFortuneCooldownUncheckedCreateInput>
  }

  /**
   * IPFortuneCooldown createMany
   */
  export type IPFortuneCooldownCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many IPFortuneCooldowns.
     */
    data: IPFortuneCooldownCreateManyInput | IPFortuneCooldownCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * IPFortuneCooldown createManyAndReturn
   */
  export type IPFortuneCooldownCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the IPFortuneCooldown
     */
    select?: IPFortuneCooldownSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the IPFortuneCooldown
     */
    omit?: IPFortuneCooldownOmit<ExtArgs> | null
    /**
     * The data used to create many IPFortuneCooldowns.
     */
    data: IPFortuneCooldownCreateManyInput | IPFortuneCooldownCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * IPFortuneCooldown update
   */
  export type IPFortuneCooldownUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the IPFortuneCooldown
     */
    select?: IPFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the IPFortuneCooldown
     */
    omit?: IPFortuneCooldownOmit<ExtArgs> | null
    /**
     * The data needed to update a IPFortuneCooldown.
     */
    data: XOR<IPFortuneCooldownUpdateInput, IPFortuneCooldownUncheckedUpdateInput>
    /**
     * Choose, which IPFortuneCooldown to update.
     */
    where: IPFortuneCooldownWhereUniqueInput
  }

  /**
   * IPFortuneCooldown updateMany
   */
  export type IPFortuneCooldownUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update IPFortuneCooldowns.
     */
    data: XOR<IPFortuneCooldownUpdateManyMutationInput, IPFortuneCooldownUncheckedUpdateManyInput>
    /**
     * Filter which IPFortuneCooldowns to update
     */
    where?: IPFortuneCooldownWhereInput
    /**
     * Limit how many IPFortuneCooldowns to update.
     */
    limit?: number
  }

  /**
   * IPFortuneCooldown updateManyAndReturn
   */
  export type IPFortuneCooldownUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the IPFortuneCooldown
     */
    select?: IPFortuneCooldownSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the IPFortuneCooldown
     */
    omit?: IPFortuneCooldownOmit<ExtArgs> | null
    /**
     * The data used to update IPFortuneCooldowns.
     */
    data: XOR<IPFortuneCooldownUpdateManyMutationInput, IPFortuneCooldownUncheckedUpdateManyInput>
    /**
     * Filter which IPFortuneCooldowns to update
     */
    where?: IPFortuneCooldownWhereInput
    /**
     * Limit how many IPFortuneCooldowns to update.
     */
    limit?: number
  }

  /**
   * IPFortuneCooldown upsert
   */
  export type IPFortuneCooldownUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the IPFortuneCooldown
     */
    select?: IPFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the IPFortuneCooldown
     */
    omit?: IPFortuneCooldownOmit<ExtArgs> | null
    /**
     * The filter to search for the IPFortuneCooldown to update in case it exists.
     */
    where: IPFortuneCooldownWhereUniqueInput
    /**
     * In case the IPFortuneCooldown found by the `where` argument doesn't exist, create a new IPFortuneCooldown with this data.
     */
    create: XOR<IPFortuneCooldownCreateInput, IPFortuneCooldownUncheckedCreateInput>
    /**
     * In case the IPFortuneCooldown was found with the provided `where` argument, update it with this data.
     */
    update: XOR<IPFortuneCooldownUpdateInput, IPFortuneCooldownUncheckedUpdateInput>
  }

  /**
   * IPFortuneCooldown delete
   */
  export type IPFortuneCooldownDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the IPFortuneCooldown
     */
    select?: IPFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the IPFortuneCooldown
     */
    omit?: IPFortuneCooldownOmit<ExtArgs> | null
    /**
     * Filter which IPFortuneCooldown to delete.
     */
    where: IPFortuneCooldownWhereUniqueInput
  }

  /**
   * IPFortuneCooldown deleteMany
   */
  export type IPFortuneCooldownDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which IPFortuneCooldowns to delete
     */
    where?: IPFortuneCooldownWhereInput
    /**
     * Limit how many IPFortuneCooldowns to delete.
     */
    limit?: number
  }

  /**
   * IPFortuneCooldown without action
   */
  export type IPFortuneCooldownDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the IPFortuneCooldown
     */
    select?: IPFortuneCooldownSelect<ExtArgs> | null
    /**
     * Omit specific fields from the IPFortuneCooldown
     */
    omit?: IPFortuneCooldownOmit<ExtArgs> | null
  }


  /**
   * Model TarotSpread
   */

  export type AggregateTarotSpread = {
    _count: TarotSpreadCountAggregateOutputType | null
    _avg: TarotSpreadAvgAggregateOutputType | null
    _sum: TarotSpreadSumAggregateOutputType | null
    _min: TarotSpreadMinAggregateOutputType | null
    _max: TarotSpreadMaxAggregateOutputType | null
  }

  export type TarotSpreadAvgAggregateOutputType = {
    cardCount: number | null
    cost: number | null
    discount: number | null
    order: number | null
  }

  export type TarotSpreadSumAggregateOutputType = {
    cardCount: number | null
    cost: number | null
    discount: number | null
    order: number | null
  }

  export type TarotSpreadMinAggregateOutputType = {
    id: string | null
    name: string | null
    description: string | null
    cardCount: number | null
    cost: number | null
    iconLayout: string | null
    spreadType: string | null
    layoutDescription: string | null
    className: string | null
    positions: string | null
    discount: number | null
    discountStartDate: Date | null
    discountEndDate: Date | null
    isActive: boolean | null
    order: number | null
    promptTemplate: string | null
    systemInstruction: string | null
    cardPositionLabels: string | null
    customVariables: string | null
    cardIntroTemplates: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type TarotSpreadMaxAggregateOutputType = {
    id: string | null
    name: string | null
    description: string | null
    cardCount: number | null
    cost: number | null
    iconLayout: string | null
    spreadType: string | null
    layoutDescription: string | null
    className: string | null
    positions: string | null
    discount: number | null
    discountStartDate: Date | null
    discountEndDate: Date | null
    isActive: boolean | null
    order: number | null
    promptTemplate: string | null
    systemInstruction: string | null
    cardPositionLabels: string | null
    customVariables: string | null
    cardIntroTemplates: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type TarotSpreadCountAggregateOutputType = {
    id: number
    name: number
    description: number
    cardCount: number
    cost: number
    iconLayout: number
    spreadType: number
    layoutDescription: number
    className: number
    positions: number
    discount: number
    discountStartDate: number
    discountEndDate: number
    isActive: number
    order: number
    promptTemplate: number
    systemInstruction: number
    cardPositionLabels: number
    customVariables: number
    cardIntroTemplates: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type TarotSpreadAvgAggregateInputType = {
    cardCount?: true
    cost?: true
    discount?: true
    order?: true
  }

  export type TarotSpreadSumAggregateInputType = {
    cardCount?: true
    cost?: true
    discount?: true
    order?: true
  }

  export type TarotSpreadMinAggregateInputType = {
    id?: true
    name?: true
    description?: true
    cardCount?: true
    cost?: true
    iconLayout?: true
    spreadType?: true
    layoutDescription?: true
    className?: true
    positions?: true
    discount?: true
    discountStartDate?: true
    discountEndDate?: true
    isActive?: true
    order?: true
    promptTemplate?: true
    systemInstruction?: true
    cardPositionLabels?: true
    customVariables?: true
    cardIntroTemplates?: true
    createdAt?: true
    updatedAt?: true
  }

  export type TarotSpreadMaxAggregateInputType = {
    id?: true
    name?: true
    description?: true
    cardCount?: true
    cost?: true
    iconLayout?: true
    spreadType?: true
    layoutDescription?: true
    className?: true
    positions?: true
    discount?: true
    discountStartDate?: true
    discountEndDate?: true
    isActive?: true
    order?: true
    promptTemplate?: true
    systemInstruction?: true
    cardPositionLabels?: true
    customVariables?: true
    cardIntroTemplates?: true
    createdAt?: true
    updatedAt?: true
  }

  export type TarotSpreadCountAggregateInputType = {
    id?: true
    name?: true
    description?: true
    cardCount?: true
    cost?: true
    iconLayout?: true
    spreadType?: true
    layoutDescription?: true
    className?: true
    positions?: true
    discount?: true
    discountStartDate?: true
    discountEndDate?: true
    isActive?: true
    order?: true
    promptTemplate?: true
    systemInstruction?: true
    cardPositionLabels?: true
    customVariables?: true
    cardIntroTemplates?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type TarotSpreadAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which TarotSpread to aggregate.
     */
    where?: TarotSpreadWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TarotSpreads to fetch.
     */
    orderBy?: TarotSpreadOrderByWithRelationInput | TarotSpreadOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: TarotSpreadWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TarotSpreads from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TarotSpreads.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned TarotSpreads
    **/
    _count?: true | TarotSpreadCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: TarotSpreadAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: TarotSpreadSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: TarotSpreadMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: TarotSpreadMaxAggregateInputType
  }

  export type GetTarotSpreadAggregateType<T extends TarotSpreadAggregateArgs> = {
        [P in keyof T & keyof AggregateTarotSpread]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateTarotSpread[P]>
      : GetScalarType<T[P], AggregateTarotSpread[P]>
  }




  export type TarotSpreadGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: TarotSpreadWhereInput
    orderBy?: TarotSpreadOrderByWithAggregationInput | TarotSpreadOrderByWithAggregationInput[]
    by: TarotSpreadScalarFieldEnum[] | TarotSpreadScalarFieldEnum
    having?: TarotSpreadScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: TarotSpreadCountAggregateInputType | true
    _avg?: TarotSpreadAvgAggregateInputType
    _sum?: TarotSpreadSumAggregateInputType
    _min?: TarotSpreadMinAggregateInputType
    _max?: TarotSpreadMaxAggregateInputType
  }

  export type TarotSpreadGroupByOutputType = {
    id: string
    name: string
    description: string
    cardCount: number
    cost: number
    iconLayout: string
    spreadType: string
    layoutDescription: string
    className: string
    positions: string
    discount: number
    discountStartDate: Date | null
    discountEndDate: Date | null
    isActive: boolean
    order: number
    promptTemplate: string
    systemInstruction: string
    cardPositionLabels: string
    customVariables: string
    cardIntroTemplates: string
    createdAt: Date
    updatedAt: Date
    _count: TarotSpreadCountAggregateOutputType | null
    _avg: TarotSpreadAvgAggregateOutputType | null
    _sum: TarotSpreadSumAggregateOutputType | null
    _min: TarotSpreadMinAggregateOutputType | null
    _max: TarotSpreadMaxAggregateOutputType | null
  }

  type GetTarotSpreadGroupByPayload<T extends TarotSpreadGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<TarotSpreadGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof TarotSpreadGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], TarotSpreadGroupByOutputType[P]>
            : GetScalarType<T[P], TarotSpreadGroupByOutputType[P]>
        }
      >
    >


  export type TarotSpreadSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    description?: boolean
    cardCount?: boolean
    cost?: boolean
    iconLayout?: boolean
    spreadType?: boolean
    layoutDescription?: boolean
    className?: boolean
    positions?: boolean
    discount?: boolean
    discountStartDate?: boolean
    discountEndDate?: boolean
    isActive?: boolean
    order?: boolean
    promptTemplate?: boolean
    systemInstruction?: boolean
    cardPositionLabels?: boolean
    customVariables?: boolean
    cardIntroTemplates?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["tarotSpread"]>

  export type TarotSpreadSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    description?: boolean
    cardCount?: boolean
    cost?: boolean
    iconLayout?: boolean
    spreadType?: boolean
    layoutDescription?: boolean
    className?: boolean
    positions?: boolean
    discount?: boolean
    discountStartDate?: boolean
    discountEndDate?: boolean
    isActive?: boolean
    order?: boolean
    promptTemplate?: boolean
    systemInstruction?: boolean
    cardPositionLabels?: boolean
    customVariables?: boolean
    cardIntroTemplates?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["tarotSpread"]>

  export type TarotSpreadSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    description?: boolean
    cardCount?: boolean
    cost?: boolean
    iconLayout?: boolean
    spreadType?: boolean
    layoutDescription?: boolean
    className?: boolean
    positions?: boolean
    discount?: boolean
    discountStartDate?: boolean
    discountEndDate?: boolean
    isActive?: boolean
    order?: boolean
    promptTemplate?: boolean
    systemInstruction?: boolean
    cardPositionLabels?: boolean
    customVariables?: boolean
    cardIntroTemplates?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["tarotSpread"]>

  export type TarotSpreadSelectScalar = {
    id?: boolean
    name?: boolean
    description?: boolean
    cardCount?: boolean
    cost?: boolean
    iconLayout?: boolean
    spreadType?: boolean
    layoutDescription?: boolean
    className?: boolean
    positions?: boolean
    discount?: boolean
    discountStartDate?: boolean
    discountEndDate?: boolean
    isActive?: boolean
    order?: boolean
    promptTemplate?: boolean
    systemInstruction?: boolean
    cardPositionLabels?: boolean
    customVariables?: boolean
    cardIntroTemplates?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type TarotSpreadOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "name" | "description" | "cardCount" | "cost" | "iconLayout" | "spreadType" | "layoutDescription" | "className" | "positions" | "discount" | "discountStartDate" | "discountEndDate" | "isActive" | "order" | "promptTemplate" | "systemInstruction" | "cardPositionLabels" | "customVariables" | "cardIntroTemplates" | "createdAt" | "updatedAt", ExtArgs["result"]["tarotSpread"]>

  export type $TarotSpreadPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "TarotSpread"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      name: string
      description: string
      cardCount: number
      cost: number
      iconLayout: string
      spreadType: string
      layoutDescription: string
      className: string
      positions: string
      discount: number
      discountStartDate: Date | null
      discountEndDate: Date | null
      isActive: boolean
      order: number
      promptTemplate: string
      systemInstruction: string
      cardPositionLabels: string
      customVariables: string
      cardIntroTemplates: string
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["tarotSpread"]>
    composites: {}
  }

  type TarotSpreadGetPayload<S extends boolean | null | undefined | TarotSpreadDefaultArgs> = $Result.GetResult<Prisma.$TarotSpreadPayload, S>

  type TarotSpreadCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<TarotSpreadFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: TarotSpreadCountAggregateInputType | true
    }

  export interface TarotSpreadDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['TarotSpread'], meta: { name: 'TarotSpread' } }
    /**
     * Find zero or one TarotSpread that matches the filter.
     * @param {TarotSpreadFindUniqueArgs} args - Arguments to find a TarotSpread
     * @example
     * // Get one TarotSpread
     * const tarotSpread = await prisma.tarotSpread.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends TarotSpreadFindUniqueArgs>(args: SelectSubset<T, TarotSpreadFindUniqueArgs<ExtArgs>>): Prisma__TarotSpreadClient<$Result.GetResult<Prisma.$TarotSpreadPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one TarotSpread that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {TarotSpreadFindUniqueOrThrowArgs} args - Arguments to find a TarotSpread
     * @example
     * // Get one TarotSpread
     * const tarotSpread = await prisma.tarotSpread.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends TarotSpreadFindUniqueOrThrowArgs>(args: SelectSubset<T, TarotSpreadFindUniqueOrThrowArgs<ExtArgs>>): Prisma__TarotSpreadClient<$Result.GetResult<Prisma.$TarotSpreadPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first TarotSpread that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotSpreadFindFirstArgs} args - Arguments to find a TarotSpread
     * @example
     * // Get one TarotSpread
     * const tarotSpread = await prisma.tarotSpread.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends TarotSpreadFindFirstArgs>(args?: SelectSubset<T, TarotSpreadFindFirstArgs<ExtArgs>>): Prisma__TarotSpreadClient<$Result.GetResult<Prisma.$TarotSpreadPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first TarotSpread that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotSpreadFindFirstOrThrowArgs} args - Arguments to find a TarotSpread
     * @example
     * // Get one TarotSpread
     * const tarotSpread = await prisma.tarotSpread.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends TarotSpreadFindFirstOrThrowArgs>(args?: SelectSubset<T, TarotSpreadFindFirstOrThrowArgs<ExtArgs>>): Prisma__TarotSpreadClient<$Result.GetResult<Prisma.$TarotSpreadPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more TarotSpreads that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotSpreadFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all TarotSpreads
     * const tarotSpreads = await prisma.tarotSpread.findMany()
     * 
     * // Get first 10 TarotSpreads
     * const tarotSpreads = await prisma.tarotSpread.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const tarotSpreadWithIdOnly = await prisma.tarotSpread.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends TarotSpreadFindManyArgs>(args?: SelectSubset<T, TarotSpreadFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TarotSpreadPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a TarotSpread.
     * @param {TarotSpreadCreateArgs} args - Arguments to create a TarotSpread.
     * @example
     * // Create one TarotSpread
     * const TarotSpread = await prisma.tarotSpread.create({
     *   data: {
     *     // ... data to create a TarotSpread
     *   }
     * })
     * 
     */
    create<T extends TarotSpreadCreateArgs>(args: SelectSubset<T, TarotSpreadCreateArgs<ExtArgs>>): Prisma__TarotSpreadClient<$Result.GetResult<Prisma.$TarotSpreadPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many TarotSpreads.
     * @param {TarotSpreadCreateManyArgs} args - Arguments to create many TarotSpreads.
     * @example
     * // Create many TarotSpreads
     * const tarotSpread = await prisma.tarotSpread.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends TarotSpreadCreateManyArgs>(args?: SelectSubset<T, TarotSpreadCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many TarotSpreads and returns the data saved in the database.
     * @param {TarotSpreadCreateManyAndReturnArgs} args - Arguments to create many TarotSpreads.
     * @example
     * // Create many TarotSpreads
     * const tarotSpread = await prisma.tarotSpread.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many TarotSpreads and only return the `id`
     * const tarotSpreadWithIdOnly = await prisma.tarotSpread.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends TarotSpreadCreateManyAndReturnArgs>(args?: SelectSubset<T, TarotSpreadCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TarotSpreadPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a TarotSpread.
     * @param {TarotSpreadDeleteArgs} args - Arguments to delete one TarotSpread.
     * @example
     * // Delete one TarotSpread
     * const TarotSpread = await prisma.tarotSpread.delete({
     *   where: {
     *     // ... filter to delete one TarotSpread
     *   }
     * })
     * 
     */
    delete<T extends TarotSpreadDeleteArgs>(args: SelectSubset<T, TarotSpreadDeleteArgs<ExtArgs>>): Prisma__TarotSpreadClient<$Result.GetResult<Prisma.$TarotSpreadPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one TarotSpread.
     * @param {TarotSpreadUpdateArgs} args - Arguments to update one TarotSpread.
     * @example
     * // Update one TarotSpread
     * const tarotSpread = await prisma.tarotSpread.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends TarotSpreadUpdateArgs>(args: SelectSubset<T, TarotSpreadUpdateArgs<ExtArgs>>): Prisma__TarotSpreadClient<$Result.GetResult<Prisma.$TarotSpreadPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more TarotSpreads.
     * @param {TarotSpreadDeleteManyArgs} args - Arguments to filter TarotSpreads to delete.
     * @example
     * // Delete a few TarotSpreads
     * const { count } = await prisma.tarotSpread.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends TarotSpreadDeleteManyArgs>(args?: SelectSubset<T, TarotSpreadDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more TarotSpreads.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotSpreadUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many TarotSpreads
     * const tarotSpread = await prisma.tarotSpread.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends TarotSpreadUpdateManyArgs>(args: SelectSubset<T, TarotSpreadUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more TarotSpreads and returns the data updated in the database.
     * @param {TarotSpreadUpdateManyAndReturnArgs} args - Arguments to update many TarotSpreads.
     * @example
     * // Update many TarotSpreads
     * const tarotSpread = await prisma.tarotSpread.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more TarotSpreads and only return the `id`
     * const tarotSpreadWithIdOnly = await prisma.tarotSpread.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends TarotSpreadUpdateManyAndReturnArgs>(args: SelectSubset<T, TarotSpreadUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TarotSpreadPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one TarotSpread.
     * @param {TarotSpreadUpsertArgs} args - Arguments to update or create a TarotSpread.
     * @example
     * // Update or create a TarotSpread
     * const tarotSpread = await prisma.tarotSpread.upsert({
     *   create: {
     *     // ... data to create a TarotSpread
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the TarotSpread we want to update
     *   }
     * })
     */
    upsert<T extends TarotSpreadUpsertArgs>(args: SelectSubset<T, TarotSpreadUpsertArgs<ExtArgs>>): Prisma__TarotSpreadClient<$Result.GetResult<Prisma.$TarotSpreadPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of TarotSpreads.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotSpreadCountArgs} args - Arguments to filter TarotSpreads to count.
     * @example
     * // Count the number of TarotSpreads
     * const count = await prisma.tarotSpread.count({
     *   where: {
     *     // ... the filter for the TarotSpreads we want to count
     *   }
     * })
    **/
    count<T extends TarotSpreadCountArgs>(
      args?: Subset<T, TarotSpreadCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], TarotSpreadCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a TarotSpread.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotSpreadAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends TarotSpreadAggregateArgs>(args: Subset<T, TarotSpreadAggregateArgs>): Prisma.PrismaPromise<GetTarotSpreadAggregateType<T>>

    /**
     * Group by TarotSpread.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotSpreadGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends TarotSpreadGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: TarotSpreadGroupByArgs['orderBy'] }
        : { orderBy?: TarotSpreadGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, TarotSpreadGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTarotSpreadGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the TarotSpread model
   */
  readonly fields: TarotSpreadFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for TarotSpread.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__TarotSpreadClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the TarotSpread model
   */
  interface TarotSpreadFieldRefs {
    readonly id: FieldRef<"TarotSpread", 'String'>
    readonly name: FieldRef<"TarotSpread", 'String'>
    readonly description: FieldRef<"TarotSpread", 'String'>
    readonly cardCount: FieldRef<"TarotSpread", 'Int'>
    readonly cost: FieldRef<"TarotSpread", 'Int'>
    readonly iconLayout: FieldRef<"TarotSpread", 'String'>
    readonly spreadType: FieldRef<"TarotSpread", 'String'>
    readonly layoutDescription: FieldRef<"TarotSpread", 'String'>
    readonly className: FieldRef<"TarotSpread", 'String'>
    readonly positions: FieldRef<"TarotSpread", 'String'>
    readonly discount: FieldRef<"TarotSpread", 'Int'>
    readonly discountStartDate: FieldRef<"TarotSpread", 'DateTime'>
    readonly discountEndDate: FieldRef<"TarotSpread", 'DateTime'>
    readonly isActive: FieldRef<"TarotSpread", 'Boolean'>
    readonly order: FieldRef<"TarotSpread", 'Int'>
    readonly promptTemplate: FieldRef<"TarotSpread", 'String'>
    readonly systemInstruction: FieldRef<"TarotSpread", 'String'>
    readonly cardPositionLabels: FieldRef<"TarotSpread", 'String'>
    readonly customVariables: FieldRef<"TarotSpread", 'String'>
    readonly cardIntroTemplates: FieldRef<"TarotSpread", 'String'>
    readonly createdAt: FieldRef<"TarotSpread", 'DateTime'>
    readonly updatedAt: FieldRef<"TarotSpread", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * TarotSpread findUnique
   */
  export type TarotSpreadFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotSpread
     */
    select?: TarotSpreadSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotSpread
     */
    omit?: TarotSpreadOmit<ExtArgs> | null
    /**
     * Filter, which TarotSpread to fetch.
     */
    where: TarotSpreadWhereUniqueInput
  }

  /**
   * TarotSpread findUniqueOrThrow
   */
  export type TarotSpreadFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotSpread
     */
    select?: TarotSpreadSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotSpread
     */
    omit?: TarotSpreadOmit<ExtArgs> | null
    /**
     * Filter, which TarotSpread to fetch.
     */
    where: TarotSpreadWhereUniqueInput
  }

  /**
   * TarotSpread findFirst
   */
  export type TarotSpreadFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotSpread
     */
    select?: TarotSpreadSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotSpread
     */
    omit?: TarotSpreadOmit<ExtArgs> | null
    /**
     * Filter, which TarotSpread to fetch.
     */
    where?: TarotSpreadWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TarotSpreads to fetch.
     */
    orderBy?: TarotSpreadOrderByWithRelationInput | TarotSpreadOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for TarotSpreads.
     */
    cursor?: TarotSpreadWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TarotSpreads from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TarotSpreads.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of TarotSpreads.
     */
    distinct?: TarotSpreadScalarFieldEnum | TarotSpreadScalarFieldEnum[]
  }

  /**
   * TarotSpread findFirstOrThrow
   */
  export type TarotSpreadFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotSpread
     */
    select?: TarotSpreadSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotSpread
     */
    omit?: TarotSpreadOmit<ExtArgs> | null
    /**
     * Filter, which TarotSpread to fetch.
     */
    where?: TarotSpreadWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TarotSpreads to fetch.
     */
    orderBy?: TarotSpreadOrderByWithRelationInput | TarotSpreadOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for TarotSpreads.
     */
    cursor?: TarotSpreadWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TarotSpreads from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TarotSpreads.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of TarotSpreads.
     */
    distinct?: TarotSpreadScalarFieldEnum | TarotSpreadScalarFieldEnum[]
  }

  /**
   * TarotSpread findMany
   */
  export type TarotSpreadFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotSpread
     */
    select?: TarotSpreadSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotSpread
     */
    omit?: TarotSpreadOmit<ExtArgs> | null
    /**
     * Filter, which TarotSpreads to fetch.
     */
    where?: TarotSpreadWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TarotSpreads to fetch.
     */
    orderBy?: TarotSpreadOrderByWithRelationInput | TarotSpreadOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing TarotSpreads.
     */
    cursor?: TarotSpreadWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TarotSpreads from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TarotSpreads.
     */
    skip?: number
    distinct?: TarotSpreadScalarFieldEnum | TarotSpreadScalarFieldEnum[]
  }

  /**
   * TarotSpread create
   */
  export type TarotSpreadCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotSpread
     */
    select?: TarotSpreadSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotSpread
     */
    omit?: TarotSpreadOmit<ExtArgs> | null
    /**
     * The data needed to create a TarotSpread.
     */
    data: XOR<TarotSpreadCreateInput, TarotSpreadUncheckedCreateInput>
  }

  /**
   * TarotSpread createMany
   */
  export type TarotSpreadCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many TarotSpreads.
     */
    data: TarotSpreadCreateManyInput | TarotSpreadCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * TarotSpread createManyAndReturn
   */
  export type TarotSpreadCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotSpread
     */
    select?: TarotSpreadSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the TarotSpread
     */
    omit?: TarotSpreadOmit<ExtArgs> | null
    /**
     * The data used to create many TarotSpreads.
     */
    data: TarotSpreadCreateManyInput | TarotSpreadCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * TarotSpread update
   */
  export type TarotSpreadUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotSpread
     */
    select?: TarotSpreadSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotSpread
     */
    omit?: TarotSpreadOmit<ExtArgs> | null
    /**
     * The data needed to update a TarotSpread.
     */
    data: XOR<TarotSpreadUpdateInput, TarotSpreadUncheckedUpdateInput>
    /**
     * Choose, which TarotSpread to update.
     */
    where: TarotSpreadWhereUniqueInput
  }

  /**
   * TarotSpread updateMany
   */
  export type TarotSpreadUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update TarotSpreads.
     */
    data: XOR<TarotSpreadUpdateManyMutationInput, TarotSpreadUncheckedUpdateManyInput>
    /**
     * Filter which TarotSpreads to update
     */
    where?: TarotSpreadWhereInput
    /**
     * Limit how many TarotSpreads to update.
     */
    limit?: number
  }

  /**
   * TarotSpread updateManyAndReturn
   */
  export type TarotSpreadUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotSpread
     */
    select?: TarotSpreadSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the TarotSpread
     */
    omit?: TarotSpreadOmit<ExtArgs> | null
    /**
     * The data used to update TarotSpreads.
     */
    data: XOR<TarotSpreadUpdateManyMutationInput, TarotSpreadUncheckedUpdateManyInput>
    /**
     * Filter which TarotSpreads to update
     */
    where?: TarotSpreadWhereInput
    /**
     * Limit how many TarotSpreads to update.
     */
    limit?: number
  }

  /**
   * TarotSpread upsert
   */
  export type TarotSpreadUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotSpread
     */
    select?: TarotSpreadSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotSpread
     */
    omit?: TarotSpreadOmit<ExtArgs> | null
    /**
     * The filter to search for the TarotSpread to update in case it exists.
     */
    where: TarotSpreadWhereUniqueInput
    /**
     * In case the TarotSpread found by the `where` argument doesn't exist, create a new TarotSpread with this data.
     */
    create: XOR<TarotSpreadCreateInput, TarotSpreadUncheckedCreateInput>
    /**
     * In case the TarotSpread was found with the provided `where` argument, update it with this data.
     */
    update: XOR<TarotSpreadUpdateInput, TarotSpreadUncheckedUpdateInput>
  }

  /**
   * TarotSpread delete
   */
  export type TarotSpreadDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotSpread
     */
    select?: TarotSpreadSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotSpread
     */
    omit?: TarotSpreadOmit<ExtArgs> | null
    /**
     * Filter which TarotSpread to delete.
     */
    where: TarotSpreadWhereUniqueInput
  }

  /**
   * TarotSpread deleteMany
   */
  export type TarotSpreadDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which TarotSpreads to delete
     */
    where?: TarotSpreadWhereInput
    /**
     * Limit how many TarotSpreads to delete.
     */
    limit?: number
  }

  /**
   * TarotSpread without action
   */
  export type TarotSpreadDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotSpread
     */
    select?: TarotSpreadSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotSpread
     */
    omit?: TarotSpreadOmit<ExtArgs> | null
  }


  /**
   * Model TarotGlobalSettings
   */

  export type AggregateTarotGlobalSettings = {
    _count: TarotGlobalSettingsCountAggregateOutputType | null
    _min: TarotGlobalSettingsMinAggregateOutputType | null
    _max: TarotGlobalSettingsMaxAggregateOutputType | null
  }

  export type TarotGlobalSettingsMinAggregateOutputType = {
    id: string | null
    settingKey: string | null
    settingValue: string | null
    description: string | null
    isActive: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type TarotGlobalSettingsMaxAggregateOutputType = {
    id: string | null
    settingKey: string | null
    settingValue: string | null
    description: string | null
    isActive: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type TarotGlobalSettingsCountAggregateOutputType = {
    id: number
    settingKey: number
    settingValue: number
    description: number
    isActive: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type TarotGlobalSettingsMinAggregateInputType = {
    id?: true
    settingKey?: true
    settingValue?: true
    description?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
  }

  export type TarotGlobalSettingsMaxAggregateInputType = {
    id?: true
    settingKey?: true
    settingValue?: true
    description?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
  }

  export type TarotGlobalSettingsCountAggregateInputType = {
    id?: true
    settingKey?: true
    settingValue?: true
    description?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type TarotGlobalSettingsAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which TarotGlobalSettings to aggregate.
     */
    where?: TarotGlobalSettingsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TarotGlobalSettings to fetch.
     */
    orderBy?: TarotGlobalSettingsOrderByWithRelationInput | TarotGlobalSettingsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: TarotGlobalSettingsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TarotGlobalSettings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TarotGlobalSettings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned TarotGlobalSettings
    **/
    _count?: true | TarotGlobalSettingsCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: TarotGlobalSettingsMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: TarotGlobalSettingsMaxAggregateInputType
  }

  export type GetTarotGlobalSettingsAggregateType<T extends TarotGlobalSettingsAggregateArgs> = {
        [P in keyof T & keyof AggregateTarotGlobalSettings]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateTarotGlobalSettings[P]>
      : GetScalarType<T[P], AggregateTarotGlobalSettings[P]>
  }




  export type TarotGlobalSettingsGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: TarotGlobalSettingsWhereInput
    orderBy?: TarotGlobalSettingsOrderByWithAggregationInput | TarotGlobalSettingsOrderByWithAggregationInput[]
    by: TarotGlobalSettingsScalarFieldEnum[] | TarotGlobalSettingsScalarFieldEnum
    having?: TarotGlobalSettingsScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: TarotGlobalSettingsCountAggregateInputType | true
    _min?: TarotGlobalSettingsMinAggregateInputType
    _max?: TarotGlobalSettingsMaxAggregateInputType
  }

  export type TarotGlobalSettingsGroupByOutputType = {
    id: string
    settingKey: string
    settingValue: string
    description: string
    isActive: boolean
    createdAt: Date
    updatedAt: Date
    _count: TarotGlobalSettingsCountAggregateOutputType | null
    _min: TarotGlobalSettingsMinAggregateOutputType | null
    _max: TarotGlobalSettingsMaxAggregateOutputType | null
  }

  type GetTarotGlobalSettingsGroupByPayload<T extends TarotGlobalSettingsGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<TarotGlobalSettingsGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof TarotGlobalSettingsGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], TarotGlobalSettingsGroupByOutputType[P]>
            : GetScalarType<T[P], TarotGlobalSettingsGroupByOutputType[P]>
        }
      >
    >


  export type TarotGlobalSettingsSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    settingKey?: boolean
    settingValue?: boolean
    description?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["tarotGlobalSettings"]>

  export type TarotGlobalSettingsSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    settingKey?: boolean
    settingValue?: boolean
    description?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["tarotGlobalSettings"]>

  export type TarotGlobalSettingsSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    settingKey?: boolean
    settingValue?: boolean
    description?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["tarotGlobalSettings"]>

  export type TarotGlobalSettingsSelectScalar = {
    id?: boolean
    settingKey?: boolean
    settingValue?: boolean
    description?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type TarotGlobalSettingsOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "settingKey" | "settingValue" | "description" | "isActive" | "createdAt" | "updatedAt", ExtArgs["result"]["tarotGlobalSettings"]>

  export type $TarotGlobalSettingsPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "TarotGlobalSettings"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      settingKey: string
      settingValue: string
      description: string
      isActive: boolean
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["tarotGlobalSettings"]>
    composites: {}
  }

  type TarotGlobalSettingsGetPayload<S extends boolean | null | undefined | TarotGlobalSettingsDefaultArgs> = $Result.GetResult<Prisma.$TarotGlobalSettingsPayload, S>

  type TarotGlobalSettingsCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<TarotGlobalSettingsFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: TarotGlobalSettingsCountAggregateInputType | true
    }

  export interface TarotGlobalSettingsDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['TarotGlobalSettings'], meta: { name: 'TarotGlobalSettings' } }
    /**
     * Find zero or one TarotGlobalSettings that matches the filter.
     * @param {TarotGlobalSettingsFindUniqueArgs} args - Arguments to find a TarotGlobalSettings
     * @example
     * // Get one TarotGlobalSettings
     * const tarotGlobalSettings = await prisma.tarotGlobalSettings.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends TarotGlobalSettingsFindUniqueArgs>(args: SelectSubset<T, TarotGlobalSettingsFindUniqueArgs<ExtArgs>>): Prisma__TarotGlobalSettingsClient<$Result.GetResult<Prisma.$TarotGlobalSettingsPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one TarotGlobalSettings that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {TarotGlobalSettingsFindUniqueOrThrowArgs} args - Arguments to find a TarotGlobalSettings
     * @example
     * // Get one TarotGlobalSettings
     * const tarotGlobalSettings = await prisma.tarotGlobalSettings.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends TarotGlobalSettingsFindUniqueOrThrowArgs>(args: SelectSubset<T, TarotGlobalSettingsFindUniqueOrThrowArgs<ExtArgs>>): Prisma__TarotGlobalSettingsClient<$Result.GetResult<Prisma.$TarotGlobalSettingsPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first TarotGlobalSettings that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotGlobalSettingsFindFirstArgs} args - Arguments to find a TarotGlobalSettings
     * @example
     * // Get one TarotGlobalSettings
     * const tarotGlobalSettings = await prisma.tarotGlobalSettings.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends TarotGlobalSettingsFindFirstArgs>(args?: SelectSubset<T, TarotGlobalSettingsFindFirstArgs<ExtArgs>>): Prisma__TarotGlobalSettingsClient<$Result.GetResult<Prisma.$TarotGlobalSettingsPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first TarotGlobalSettings that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotGlobalSettingsFindFirstOrThrowArgs} args - Arguments to find a TarotGlobalSettings
     * @example
     * // Get one TarotGlobalSettings
     * const tarotGlobalSettings = await prisma.tarotGlobalSettings.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends TarotGlobalSettingsFindFirstOrThrowArgs>(args?: SelectSubset<T, TarotGlobalSettingsFindFirstOrThrowArgs<ExtArgs>>): Prisma__TarotGlobalSettingsClient<$Result.GetResult<Prisma.$TarotGlobalSettingsPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more TarotGlobalSettings that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotGlobalSettingsFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all TarotGlobalSettings
     * const tarotGlobalSettings = await prisma.tarotGlobalSettings.findMany()
     * 
     * // Get first 10 TarotGlobalSettings
     * const tarotGlobalSettings = await prisma.tarotGlobalSettings.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const tarotGlobalSettingsWithIdOnly = await prisma.tarotGlobalSettings.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends TarotGlobalSettingsFindManyArgs>(args?: SelectSubset<T, TarotGlobalSettingsFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TarotGlobalSettingsPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a TarotGlobalSettings.
     * @param {TarotGlobalSettingsCreateArgs} args - Arguments to create a TarotGlobalSettings.
     * @example
     * // Create one TarotGlobalSettings
     * const TarotGlobalSettings = await prisma.tarotGlobalSettings.create({
     *   data: {
     *     // ... data to create a TarotGlobalSettings
     *   }
     * })
     * 
     */
    create<T extends TarotGlobalSettingsCreateArgs>(args: SelectSubset<T, TarotGlobalSettingsCreateArgs<ExtArgs>>): Prisma__TarotGlobalSettingsClient<$Result.GetResult<Prisma.$TarotGlobalSettingsPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many TarotGlobalSettings.
     * @param {TarotGlobalSettingsCreateManyArgs} args - Arguments to create many TarotGlobalSettings.
     * @example
     * // Create many TarotGlobalSettings
     * const tarotGlobalSettings = await prisma.tarotGlobalSettings.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends TarotGlobalSettingsCreateManyArgs>(args?: SelectSubset<T, TarotGlobalSettingsCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many TarotGlobalSettings and returns the data saved in the database.
     * @param {TarotGlobalSettingsCreateManyAndReturnArgs} args - Arguments to create many TarotGlobalSettings.
     * @example
     * // Create many TarotGlobalSettings
     * const tarotGlobalSettings = await prisma.tarotGlobalSettings.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many TarotGlobalSettings and only return the `id`
     * const tarotGlobalSettingsWithIdOnly = await prisma.tarotGlobalSettings.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends TarotGlobalSettingsCreateManyAndReturnArgs>(args?: SelectSubset<T, TarotGlobalSettingsCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TarotGlobalSettingsPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a TarotGlobalSettings.
     * @param {TarotGlobalSettingsDeleteArgs} args - Arguments to delete one TarotGlobalSettings.
     * @example
     * // Delete one TarotGlobalSettings
     * const TarotGlobalSettings = await prisma.tarotGlobalSettings.delete({
     *   where: {
     *     // ... filter to delete one TarotGlobalSettings
     *   }
     * })
     * 
     */
    delete<T extends TarotGlobalSettingsDeleteArgs>(args: SelectSubset<T, TarotGlobalSettingsDeleteArgs<ExtArgs>>): Prisma__TarotGlobalSettingsClient<$Result.GetResult<Prisma.$TarotGlobalSettingsPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one TarotGlobalSettings.
     * @param {TarotGlobalSettingsUpdateArgs} args - Arguments to update one TarotGlobalSettings.
     * @example
     * // Update one TarotGlobalSettings
     * const tarotGlobalSettings = await prisma.tarotGlobalSettings.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends TarotGlobalSettingsUpdateArgs>(args: SelectSubset<T, TarotGlobalSettingsUpdateArgs<ExtArgs>>): Prisma__TarotGlobalSettingsClient<$Result.GetResult<Prisma.$TarotGlobalSettingsPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more TarotGlobalSettings.
     * @param {TarotGlobalSettingsDeleteManyArgs} args - Arguments to filter TarotGlobalSettings to delete.
     * @example
     * // Delete a few TarotGlobalSettings
     * const { count } = await prisma.tarotGlobalSettings.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends TarotGlobalSettingsDeleteManyArgs>(args?: SelectSubset<T, TarotGlobalSettingsDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more TarotGlobalSettings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotGlobalSettingsUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many TarotGlobalSettings
     * const tarotGlobalSettings = await prisma.tarotGlobalSettings.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends TarotGlobalSettingsUpdateManyArgs>(args: SelectSubset<T, TarotGlobalSettingsUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more TarotGlobalSettings and returns the data updated in the database.
     * @param {TarotGlobalSettingsUpdateManyAndReturnArgs} args - Arguments to update many TarotGlobalSettings.
     * @example
     * // Update many TarotGlobalSettings
     * const tarotGlobalSettings = await prisma.tarotGlobalSettings.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more TarotGlobalSettings and only return the `id`
     * const tarotGlobalSettingsWithIdOnly = await prisma.tarotGlobalSettings.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends TarotGlobalSettingsUpdateManyAndReturnArgs>(args: SelectSubset<T, TarotGlobalSettingsUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TarotGlobalSettingsPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one TarotGlobalSettings.
     * @param {TarotGlobalSettingsUpsertArgs} args - Arguments to update or create a TarotGlobalSettings.
     * @example
     * // Update or create a TarotGlobalSettings
     * const tarotGlobalSettings = await prisma.tarotGlobalSettings.upsert({
     *   create: {
     *     // ... data to create a TarotGlobalSettings
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the TarotGlobalSettings we want to update
     *   }
     * })
     */
    upsert<T extends TarotGlobalSettingsUpsertArgs>(args: SelectSubset<T, TarotGlobalSettingsUpsertArgs<ExtArgs>>): Prisma__TarotGlobalSettingsClient<$Result.GetResult<Prisma.$TarotGlobalSettingsPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of TarotGlobalSettings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotGlobalSettingsCountArgs} args - Arguments to filter TarotGlobalSettings to count.
     * @example
     * // Count the number of TarotGlobalSettings
     * const count = await prisma.tarotGlobalSettings.count({
     *   where: {
     *     // ... the filter for the TarotGlobalSettings we want to count
     *   }
     * })
    **/
    count<T extends TarotGlobalSettingsCountArgs>(
      args?: Subset<T, TarotGlobalSettingsCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], TarotGlobalSettingsCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a TarotGlobalSettings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotGlobalSettingsAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends TarotGlobalSettingsAggregateArgs>(args: Subset<T, TarotGlobalSettingsAggregateArgs>): Prisma.PrismaPromise<GetTarotGlobalSettingsAggregateType<T>>

    /**
     * Group by TarotGlobalSettings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotGlobalSettingsGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends TarotGlobalSettingsGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: TarotGlobalSettingsGroupByArgs['orderBy'] }
        : { orderBy?: TarotGlobalSettingsGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, TarotGlobalSettingsGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTarotGlobalSettingsGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the TarotGlobalSettings model
   */
  readonly fields: TarotGlobalSettingsFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for TarotGlobalSettings.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__TarotGlobalSettingsClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the TarotGlobalSettings model
   */
  interface TarotGlobalSettingsFieldRefs {
    readonly id: FieldRef<"TarotGlobalSettings", 'String'>
    readonly settingKey: FieldRef<"TarotGlobalSettings", 'String'>
    readonly settingValue: FieldRef<"TarotGlobalSettings", 'String'>
    readonly description: FieldRef<"TarotGlobalSettings", 'String'>
    readonly isActive: FieldRef<"TarotGlobalSettings", 'Boolean'>
    readonly createdAt: FieldRef<"TarotGlobalSettings", 'DateTime'>
    readonly updatedAt: FieldRef<"TarotGlobalSettings", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * TarotGlobalSettings findUnique
   */
  export type TarotGlobalSettingsFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotGlobalSettings
     */
    select?: TarotGlobalSettingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotGlobalSettings
     */
    omit?: TarotGlobalSettingsOmit<ExtArgs> | null
    /**
     * Filter, which TarotGlobalSettings to fetch.
     */
    where: TarotGlobalSettingsWhereUniqueInput
  }

  /**
   * TarotGlobalSettings findUniqueOrThrow
   */
  export type TarotGlobalSettingsFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotGlobalSettings
     */
    select?: TarotGlobalSettingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotGlobalSettings
     */
    omit?: TarotGlobalSettingsOmit<ExtArgs> | null
    /**
     * Filter, which TarotGlobalSettings to fetch.
     */
    where: TarotGlobalSettingsWhereUniqueInput
  }

  /**
   * TarotGlobalSettings findFirst
   */
  export type TarotGlobalSettingsFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotGlobalSettings
     */
    select?: TarotGlobalSettingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotGlobalSettings
     */
    omit?: TarotGlobalSettingsOmit<ExtArgs> | null
    /**
     * Filter, which TarotGlobalSettings to fetch.
     */
    where?: TarotGlobalSettingsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TarotGlobalSettings to fetch.
     */
    orderBy?: TarotGlobalSettingsOrderByWithRelationInput | TarotGlobalSettingsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for TarotGlobalSettings.
     */
    cursor?: TarotGlobalSettingsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TarotGlobalSettings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TarotGlobalSettings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of TarotGlobalSettings.
     */
    distinct?: TarotGlobalSettingsScalarFieldEnum | TarotGlobalSettingsScalarFieldEnum[]
  }

  /**
   * TarotGlobalSettings findFirstOrThrow
   */
  export type TarotGlobalSettingsFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotGlobalSettings
     */
    select?: TarotGlobalSettingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotGlobalSettings
     */
    omit?: TarotGlobalSettingsOmit<ExtArgs> | null
    /**
     * Filter, which TarotGlobalSettings to fetch.
     */
    where?: TarotGlobalSettingsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TarotGlobalSettings to fetch.
     */
    orderBy?: TarotGlobalSettingsOrderByWithRelationInput | TarotGlobalSettingsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for TarotGlobalSettings.
     */
    cursor?: TarotGlobalSettingsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TarotGlobalSettings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TarotGlobalSettings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of TarotGlobalSettings.
     */
    distinct?: TarotGlobalSettingsScalarFieldEnum | TarotGlobalSettingsScalarFieldEnum[]
  }

  /**
   * TarotGlobalSettings findMany
   */
  export type TarotGlobalSettingsFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotGlobalSettings
     */
    select?: TarotGlobalSettingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotGlobalSettings
     */
    omit?: TarotGlobalSettingsOmit<ExtArgs> | null
    /**
     * Filter, which TarotGlobalSettings to fetch.
     */
    where?: TarotGlobalSettingsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TarotGlobalSettings to fetch.
     */
    orderBy?: TarotGlobalSettingsOrderByWithRelationInput | TarotGlobalSettingsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing TarotGlobalSettings.
     */
    cursor?: TarotGlobalSettingsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TarotGlobalSettings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TarotGlobalSettings.
     */
    skip?: number
    distinct?: TarotGlobalSettingsScalarFieldEnum | TarotGlobalSettingsScalarFieldEnum[]
  }

  /**
   * TarotGlobalSettings create
   */
  export type TarotGlobalSettingsCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotGlobalSettings
     */
    select?: TarotGlobalSettingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotGlobalSettings
     */
    omit?: TarotGlobalSettingsOmit<ExtArgs> | null
    /**
     * The data needed to create a TarotGlobalSettings.
     */
    data: XOR<TarotGlobalSettingsCreateInput, TarotGlobalSettingsUncheckedCreateInput>
  }

  /**
   * TarotGlobalSettings createMany
   */
  export type TarotGlobalSettingsCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many TarotGlobalSettings.
     */
    data: TarotGlobalSettingsCreateManyInput | TarotGlobalSettingsCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * TarotGlobalSettings createManyAndReturn
   */
  export type TarotGlobalSettingsCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotGlobalSettings
     */
    select?: TarotGlobalSettingsSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the TarotGlobalSettings
     */
    omit?: TarotGlobalSettingsOmit<ExtArgs> | null
    /**
     * The data used to create many TarotGlobalSettings.
     */
    data: TarotGlobalSettingsCreateManyInput | TarotGlobalSettingsCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * TarotGlobalSettings update
   */
  export type TarotGlobalSettingsUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotGlobalSettings
     */
    select?: TarotGlobalSettingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotGlobalSettings
     */
    omit?: TarotGlobalSettingsOmit<ExtArgs> | null
    /**
     * The data needed to update a TarotGlobalSettings.
     */
    data: XOR<TarotGlobalSettingsUpdateInput, TarotGlobalSettingsUncheckedUpdateInput>
    /**
     * Choose, which TarotGlobalSettings to update.
     */
    where: TarotGlobalSettingsWhereUniqueInput
  }

  /**
   * TarotGlobalSettings updateMany
   */
  export type TarotGlobalSettingsUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update TarotGlobalSettings.
     */
    data: XOR<TarotGlobalSettingsUpdateManyMutationInput, TarotGlobalSettingsUncheckedUpdateManyInput>
    /**
     * Filter which TarotGlobalSettings to update
     */
    where?: TarotGlobalSettingsWhereInput
    /**
     * Limit how many TarotGlobalSettings to update.
     */
    limit?: number
  }

  /**
   * TarotGlobalSettings updateManyAndReturn
   */
  export type TarotGlobalSettingsUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotGlobalSettings
     */
    select?: TarotGlobalSettingsSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the TarotGlobalSettings
     */
    omit?: TarotGlobalSettingsOmit<ExtArgs> | null
    /**
     * The data used to update TarotGlobalSettings.
     */
    data: XOR<TarotGlobalSettingsUpdateManyMutationInput, TarotGlobalSettingsUncheckedUpdateManyInput>
    /**
     * Filter which TarotGlobalSettings to update
     */
    where?: TarotGlobalSettingsWhereInput
    /**
     * Limit how many TarotGlobalSettings to update.
     */
    limit?: number
  }

  /**
   * TarotGlobalSettings upsert
   */
  export type TarotGlobalSettingsUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotGlobalSettings
     */
    select?: TarotGlobalSettingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotGlobalSettings
     */
    omit?: TarotGlobalSettingsOmit<ExtArgs> | null
    /**
     * The filter to search for the TarotGlobalSettings to update in case it exists.
     */
    where: TarotGlobalSettingsWhereUniqueInput
    /**
     * In case the TarotGlobalSettings found by the `where` argument doesn't exist, create a new TarotGlobalSettings with this data.
     */
    create: XOR<TarotGlobalSettingsCreateInput, TarotGlobalSettingsUncheckedCreateInput>
    /**
     * In case the TarotGlobalSettings was found with the provided `where` argument, update it with this data.
     */
    update: XOR<TarotGlobalSettingsUpdateInput, TarotGlobalSettingsUncheckedUpdateInput>
  }

  /**
   * TarotGlobalSettings delete
   */
  export type TarotGlobalSettingsDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotGlobalSettings
     */
    select?: TarotGlobalSettingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotGlobalSettings
     */
    omit?: TarotGlobalSettingsOmit<ExtArgs> | null
    /**
     * Filter which TarotGlobalSettings to delete.
     */
    where: TarotGlobalSettingsWhereUniqueInput
  }

  /**
   * TarotGlobalSettings deleteMany
   */
  export type TarotGlobalSettingsDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which TarotGlobalSettings to delete
     */
    where?: TarotGlobalSettingsWhereInput
    /**
     * Limit how many TarotGlobalSettings to delete.
     */
    limit?: number
  }

  /**
   * TarotGlobalSettings without action
   */
  export type TarotGlobalSettingsDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotGlobalSettings
     */
    select?: TarotGlobalSettingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotGlobalSettings
     */
    omit?: TarotGlobalSettingsOmit<ExtArgs> | null
  }


  /**
   * Model TarotReading
   */

  export type AggregateTarotReading = {
    _count: TarotReadingCountAggregateOutputType | null
    _avg: TarotReadingAvgAggregateOutputType | null
    _sum: TarotReadingSumAggregateOutputType | null
    _min: TarotReadingMinAggregateOutputType | null
    _max: TarotReadingMaxAggregateOutputType | null
  }

  export type TarotReadingAvgAggregateOutputType = {
    cardCount: number | null
    promptTokens: number | null
    completionTokens: number | null
    totalTokens: number | null
  }

  export type TarotReadingSumAggregateOutputType = {
    cardCount: number | null
    promptTokens: number | null
    completionTokens: number | null
    totalTokens: number | null
  }

  export type TarotReadingMinAggregateOutputType = {
    id: string | null
    userId: string | null
    readingType: string | null
    spreadType: string | null
    cardCount: number | null
    promptTokens: number | null
    completionTokens: number | null
    totalTokens: number | null
    ipAddress: string | null
    userAgent: string | null
    createdAt: Date | null
  }

  export type TarotReadingMaxAggregateOutputType = {
    id: string | null
    userId: string | null
    readingType: string | null
    spreadType: string | null
    cardCount: number | null
    promptTokens: number | null
    completionTokens: number | null
    totalTokens: number | null
    ipAddress: string | null
    userAgent: string | null
    createdAt: Date | null
  }

  export type TarotReadingCountAggregateOutputType = {
    id: number
    userId: number
    readingType: number
    spreadType: number
    cardCount: number
    promptTokens: number
    completionTokens: number
    totalTokens: number
    ipAddress: number
    userAgent: number
    createdAt: number
    _all: number
  }


  export type TarotReadingAvgAggregateInputType = {
    cardCount?: true
    promptTokens?: true
    completionTokens?: true
    totalTokens?: true
  }

  export type TarotReadingSumAggregateInputType = {
    cardCount?: true
    promptTokens?: true
    completionTokens?: true
    totalTokens?: true
  }

  export type TarotReadingMinAggregateInputType = {
    id?: true
    userId?: true
    readingType?: true
    spreadType?: true
    cardCount?: true
    promptTokens?: true
    completionTokens?: true
    totalTokens?: true
    ipAddress?: true
    userAgent?: true
    createdAt?: true
  }

  export type TarotReadingMaxAggregateInputType = {
    id?: true
    userId?: true
    readingType?: true
    spreadType?: true
    cardCount?: true
    promptTokens?: true
    completionTokens?: true
    totalTokens?: true
    ipAddress?: true
    userAgent?: true
    createdAt?: true
  }

  export type TarotReadingCountAggregateInputType = {
    id?: true
    userId?: true
    readingType?: true
    spreadType?: true
    cardCount?: true
    promptTokens?: true
    completionTokens?: true
    totalTokens?: true
    ipAddress?: true
    userAgent?: true
    createdAt?: true
    _all?: true
  }

  export type TarotReadingAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which TarotReading to aggregate.
     */
    where?: TarotReadingWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TarotReadings to fetch.
     */
    orderBy?: TarotReadingOrderByWithRelationInput | TarotReadingOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: TarotReadingWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TarotReadings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TarotReadings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned TarotReadings
    **/
    _count?: true | TarotReadingCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: TarotReadingAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: TarotReadingSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: TarotReadingMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: TarotReadingMaxAggregateInputType
  }

  export type GetTarotReadingAggregateType<T extends TarotReadingAggregateArgs> = {
        [P in keyof T & keyof AggregateTarotReading]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateTarotReading[P]>
      : GetScalarType<T[P], AggregateTarotReading[P]>
  }




  export type TarotReadingGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: TarotReadingWhereInput
    orderBy?: TarotReadingOrderByWithAggregationInput | TarotReadingOrderByWithAggregationInput[]
    by: TarotReadingScalarFieldEnum[] | TarotReadingScalarFieldEnum
    having?: TarotReadingScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: TarotReadingCountAggregateInputType | true
    _avg?: TarotReadingAvgAggregateInputType
    _sum?: TarotReadingSumAggregateInputType
    _min?: TarotReadingMinAggregateInputType
    _max?: TarotReadingMaxAggregateInputType
  }

  export type TarotReadingGroupByOutputType = {
    id: string
    userId: string | null
    readingType: string
    spreadType: string | null
    cardCount: number
    promptTokens: number
    completionTokens: number
    totalTokens: number
    ipAddress: string | null
    userAgent: string | null
    createdAt: Date
    _count: TarotReadingCountAggregateOutputType | null
    _avg: TarotReadingAvgAggregateOutputType | null
    _sum: TarotReadingSumAggregateOutputType | null
    _min: TarotReadingMinAggregateOutputType | null
    _max: TarotReadingMaxAggregateOutputType | null
  }

  type GetTarotReadingGroupByPayload<T extends TarotReadingGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<TarotReadingGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof TarotReadingGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], TarotReadingGroupByOutputType[P]>
            : GetScalarType<T[P], TarotReadingGroupByOutputType[P]>
        }
      >
    >


  export type TarotReadingSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    readingType?: boolean
    spreadType?: boolean
    cardCount?: boolean
    promptTokens?: boolean
    completionTokens?: boolean
    totalTokens?: boolean
    ipAddress?: boolean
    userAgent?: boolean
    createdAt?: boolean
    user?: boolean | TarotReading$userArgs<ExtArgs>
  }, ExtArgs["result"]["tarotReading"]>

  export type TarotReadingSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    readingType?: boolean
    spreadType?: boolean
    cardCount?: boolean
    promptTokens?: boolean
    completionTokens?: boolean
    totalTokens?: boolean
    ipAddress?: boolean
    userAgent?: boolean
    createdAt?: boolean
    user?: boolean | TarotReading$userArgs<ExtArgs>
  }, ExtArgs["result"]["tarotReading"]>

  export type TarotReadingSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    readingType?: boolean
    spreadType?: boolean
    cardCount?: boolean
    promptTokens?: boolean
    completionTokens?: boolean
    totalTokens?: boolean
    ipAddress?: boolean
    userAgent?: boolean
    createdAt?: boolean
    user?: boolean | TarotReading$userArgs<ExtArgs>
  }, ExtArgs["result"]["tarotReading"]>

  export type TarotReadingSelectScalar = {
    id?: boolean
    userId?: boolean
    readingType?: boolean
    spreadType?: boolean
    cardCount?: boolean
    promptTokens?: boolean
    completionTokens?: boolean
    totalTokens?: boolean
    ipAddress?: boolean
    userAgent?: boolean
    createdAt?: boolean
  }

  export type TarotReadingOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "userId" | "readingType" | "spreadType" | "cardCount" | "promptTokens" | "completionTokens" | "totalTokens" | "ipAddress" | "userAgent" | "createdAt", ExtArgs["result"]["tarotReading"]>
  export type TarotReadingInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | TarotReading$userArgs<ExtArgs>
  }
  export type TarotReadingIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | TarotReading$userArgs<ExtArgs>
  }
  export type TarotReadingIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | TarotReading$userArgs<ExtArgs>
  }

  export type $TarotReadingPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "TarotReading"
    objects: {
      user: Prisma.$UserPayload<ExtArgs> | null
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      userId: string | null
      readingType: string
      spreadType: string | null
      cardCount: number
      promptTokens: number
      completionTokens: number
      totalTokens: number
      ipAddress: string | null
      userAgent: string | null
      createdAt: Date
    }, ExtArgs["result"]["tarotReading"]>
    composites: {}
  }

  type TarotReadingGetPayload<S extends boolean | null | undefined | TarotReadingDefaultArgs> = $Result.GetResult<Prisma.$TarotReadingPayload, S>

  type TarotReadingCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<TarotReadingFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: TarotReadingCountAggregateInputType | true
    }

  export interface TarotReadingDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['TarotReading'], meta: { name: 'TarotReading' } }
    /**
     * Find zero or one TarotReading that matches the filter.
     * @param {TarotReadingFindUniqueArgs} args - Arguments to find a TarotReading
     * @example
     * // Get one TarotReading
     * const tarotReading = await prisma.tarotReading.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends TarotReadingFindUniqueArgs>(args: SelectSubset<T, TarotReadingFindUniqueArgs<ExtArgs>>): Prisma__TarotReadingClient<$Result.GetResult<Prisma.$TarotReadingPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one TarotReading that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {TarotReadingFindUniqueOrThrowArgs} args - Arguments to find a TarotReading
     * @example
     * // Get one TarotReading
     * const tarotReading = await prisma.tarotReading.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends TarotReadingFindUniqueOrThrowArgs>(args: SelectSubset<T, TarotReadingFindUniqueOrThrowArgs<ExtArgs>>): Prisma__TarotReadingClient<$Result.GetResult<Prisma.$TarotReadingPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first TarotReading that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotReadingFindFirstArgs} args - Arguments to find a TarotReading
     * @example
     * // Get one TarotReading
     * const tarotReading = await prisma.tarotReading.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends TarotReadingFindFirstArgs>(args?: SelectSubset<T, TarotReadingFindFirstArgs<ExtArgs>>): Prisma__TarotReadingClient<$Result.GetResult<Prisma.$TarotReadingPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first TarotReading that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotReadingFindFirstOrThrowArgs} args - Arguments to find a TarotReading
     * @example
     * // Get one TarotReading
     * const tarotReading = await prisma.tarotReading.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends TarotReadingFindFirstOrThrowArgs>(args?: SelectSubset<T, TarotReadingFindFirstOrThrowArgs<ExtArgs>>): Prisma__TarotReadingClient<$Result.GetResult<Prisma.$TarotReadingPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more TarotReadings that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotReadingFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all TarotReadings
     * const tarotReadings = await prisma.tarotReading.findMany()
     * 
     * // Get first 10 TarotReadings
     * const tarotReadings = await prisma.tarotReading.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const tarotReadingWithIdOnly = await prisma.tarotReading.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends TarotReadingFindManyArgs>(args?: SelectSubset<T, TarotReadingFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TarotReadingPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a TarotReading.
     * @param {TarotReadingCreateArgs} args - Arguments to create a TarotReading.
     * @example
     * // Create one TarotReading
     * const TarotReading = await prisma.tarotReading.create({
     *   data: {
     *     // ... data to create a TarotReading
     *   }
     * })
     * 
     */
    create<T extends TarotReadingCreateArgs>(args: SelectSubset<T, TarotReadingCreateArgs<ExtArgs>>): Prisma__TarotReadingClient<$Result.GetResult<Prisma.$TarotReadingPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many TarotReadings.
     * @param {TarotReadingCreateManyArgs} args - Arguments to create many TarotReadings.
     * @example
     * // Create many TarotReadings
     * const tarotReading = await prisma.tarotReading.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends TarotReadingCreateManyArgs>(args?: SelectSubset<T, TarotReadingCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many TarotReadings and returns the data saved in the database.
     * @param {TarotReadingCreateManyAndReturnArgs} args - Arguments to create many TarotReadings.
     * @example
     * // Create many TarotReadings
     * const tarotReading = await prisma.tarotReading.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many TarotReadings and only return the `id`
     * const tarotReadingWithIdOnly = await prisma.tarotReading.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends TarotReadingCreateManyAndReturnArgs>(args?: SelectSubset<T, TarotReadingCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TarotReadingPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a TarotReading.
     * @param {TarotReadingDeleteArgs} args - Arguments to delete one TarotReading.
     * @example
     * // Delete one TarotReading
     * const TarotReading = await prisma.tarotReading.delete({
     *   where: {
     *     // ... filter to delete one TarotReading
     *   }
     * })
     * 
     */
    delete<T extends TarotReadingDeleteArgs>(args: SelectSubset<T, TarotReadingDeleteArgs<ExtArgs>>): Prisma__TarotReadingClient<$Result.GetResult<Prisma.$TarotReadingPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one TarotReading.
     * @param {TarotReadingUpdateArgs} args - Arguments to update one TarotReading.
     * @example
     * // Update one TarotReading
     * const tarotReading = await prisma.tarotReading.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends TarotReadingUpdateArgs>(args: SelectSubset<T, TarotReadingUpdateArgs<ExtArgs>>): Prisma__TarotReadingClient<$Result.GetResult<Prisma.$TarotReadingPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more TarotReadings.
     * @param {TarotReadingDeleteManyArgs} args - Arguments to filter TarotReadings to delete.
     * @example
     * // Delete a few TarotReadings
     * const { count } = await prisma.tarotReading.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends TarotReadingDeleteManyArgs>(args?: SelectSubset<T, TarotReadingDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more TarotReadings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotReadingUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many TarotReadings
     * const tarotReading = await prisma.tarotReading.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends TarotReadingUpdateManyArgs>(args: SelectSubset<T, TarotReadingUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more TarotReadings and returns the data updated in the database.
     * @param {TarotReadingUpdateManyAndReturnArgs} args - Arguments to update many TarotReadings.
     * @example
     * // Update many TarotReadings
     * const tarotReading = await prisma.tarotReading.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more TarotReadings and only return the `id`
     * const tarotReadingWithIdOnly = await prisma.tarotReading.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends TarotReadingUpdateManyAndReturnArgs>(args: SelectSubset<T, TarotReadingUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TarotReadingPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one TarotReading.
     * @param {TarotReadingUpsertArgs} args - Arguments to update or create a TarotReading.
     * @example
     * // Update or create a TarotReading
     * const tarotReading = await prisma.tarotReading.upsert({
     *   create: {
     *     // ... data to create a TarotReading
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the TarotReading we want to update
     *   }
     * })
     */
    upsert<T extends TarotReadingUpsertArgs>(args: SelectSubset<T, TarotReadingUpsertArgs<ExtArgs>>): Prisma__TarotReadingClient<$Result.GetResult<Prisma.$TarotReadingPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of TarotReadings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotReadingCountArgs} args - Arguments to filter TarotReadings to count.
     * @example
     * // Count the number of TarotReadings
     * const count = await prisma.tarotReading.count({
     *   where: {
     *     // ... the filter for the TarotReadings we want to count
     *   }
     * })
    **/
    count<T extends TarotReadingCountArgs>(
      args?: Subset<T, TarotReadingCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], TarotReadingCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a TarotReading.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotReadingAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends TarotReadingAggregateArgs>(args: Subset<T, TarotReadingAggregateArgs>): Prisma.PrismaPromise<GetTarotReadingAggregateType<T>>

    /**
     * Group by TarotReading.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TarotReadingGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends TarotReadingGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: TarotReadingGroupByArgs['orderBy'] }
        : { orderBy?: TarotReadingGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, TarotReadingGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTarotReadingGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the TarotReading model
   */
  readonly fields: TarotReadingFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for TarotReading.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__TarotReadingClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends TarotReading$userArgs<ExtArgs> = {}>(args?: Subset<T, TarotReading$userArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the TarotReading model
   */
  interface TarotReadingFieldRefs {
    readonly id: FieldRef<"TarotReading", 'String'>
    readonly userId: FieldRef<"TarotReading", 'String'>
    readonly readingType: FieldRef<"TarotReading", 'String'>
    readonly spreadType: FieldRef<"TarotReading", 'String'>
    readonly cardCount: FieldRef<"TarotReading", 'Int'>
    readonly promptTokens: FieldRef<"TarotReading", 'Int'>
    readonly completionTokens: FieldRef<"TarotReading", 'Int'>
    readonly totalTokens: FieldRef<"TarotReading", 'Int'>
    readonly ipAddress: FieldRef<"TarotReading", 'String'>
    readonly userAgent: FieldRef<"TarotReading", 'String'>
    readonly createdAt: FieldRef<"TarotReading", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * TarotReading findUnique
   */
  export type TarotReadingFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotReading
     */
    select?: TarotReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotReading
     */
    omit?: TarotReadingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TarotReadingInclude<ExtArgs> | null
    /**
     * Filter, which TarotReading to fetch.
     */
    where: TarotReadingWhereUniqueInput
  }

  /**
   * TarotReading findUniqueOrThrow
   */
  export type TarotReadingFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotReading
     */
    select?: TarotReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotReading
     */
    omit?: TarotReadingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TarotReadingInclude<ExtArgs> | null
    /**
     * Filter, which TarotReading to fetch.
     */
    where: TarotReadingWhereUniqueInput
  }

  /**
   * TarotReading findFirst
   */
  export type TarotReadingFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotReading
     */
    select?: TarotReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotReading
     */
    omit?: TarotReadingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TarotReadingInclude<ExtArgs> | null
    /**
     * Filter, which TarotReading to fetch.
     */
    where?: TarotReadingWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TarotReadings to fetch.
     */
    orderBy?: TarotReadingOrderByWithRelationInput | TarotReadingOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for TarotReadings.
     */
    cursor?: TarotReadingWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TarotReadings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TarotReadings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of TarotReadings.
     */
    distinct?: TarotReadingScalarFieldEnum | TarotReadingScalarFieldEnum[]
  }

  /**
   * TarotReading findFirstOrThrow
   */
  export type TarotReadingFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotReading
     */
    select?: TarotReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotReading
     */
    omit?: TarotReadingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TarotReadingInclude<ExtArgs> | null
    /**
     * Filter, which TarotReading to fetch.
     */
    where?: TarotReadingWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TarotReadings to fetch.
     */
    orderBy?: TarotReadingOrderByWithRelationInput | TarotReadingOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for TarotReadings.
     */
    cursor?: TarotReadingWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TarotReadings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TarotReadings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of TarotReadings.
     */
    distinct?: TarotReadingScalarFieldEnum | TarotReadingScalarFieldEnum[]
  }

  /**
   * TarotReading findMany
   */
  export type TarotReadingFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotReading
     */
    select?: TarotReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotReading
     */
    omit?: TarotReadingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TarotReadingInclude<ExtArgs> | null
    /**
     * Filter, which TarotReadings to fetch.
     */
    where?: TarotReadingWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TarotReadings to fetch.
     */
    orderBy?: TarotReadingOrderByWithRelationInput | TarotReadingOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing TarotReadings.
     */
    cursor?: TarotReadingWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TarotReadings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TarotReadings.
     */
    skip?: number
    distinct?: TarotReadingScalarFieldEnum | TarotReadingScalarFieldEnum[]
  }

  /**
   * TarotReading create
   */
  export type TarotReadingCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotReading
     */
    select?: TarotReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotReading
     */
    omit?: TarotReadingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TarotReadingInclude<ExtArgs> | null
    /**
     * The data needed to create a TarotReading.
     */
    data: XOR<TarotReadingCreateInput, TarotReadingUncheckedCreateInput>
  }

  /**
   * TarotReading createMany
   */
  export type TarotReadingCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many TarotReadings.
     */
    data: TarotReadingCreateManyInput | TarotReadingCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * TarotReading createManyAndReturn
   */
  export type TarotReadingCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotReading
     */
    select?: TarotReadingSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the TarotReading
     */
    omit?: TarotReadingOmit<ExtArgs> | null
    /**
     * The data used to create many TarotReadings.
     */
    data: TarotReadingCreateManyInput | TarotReadingCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TarotReadingIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * TarotReading update
   */
  export type TarotReadingUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotReading
     */
    select?: TarotReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotReading
     */
    omit?: TarotReadingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TarotReadingInclude<ExtArgs> | null
    /**
     * The data needed to update a TarotReading.
     */
    data: XOR<TarotReadingUpdateInput, TarotReadingUncheckedUpdateInput>
    /**
     * Choose, which TarotReading to update.
     */
    where: TarotReadingWhereUniqueInput
  }

  /**
   * TarotReading updateMany
   */
  export type TarotReadingUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update TarotReadings.
     */
    data: XOR<TarotReadingUpdateManyMutationInput, TarotReadingUncheckedUpdateManyInput>
    /**
     * Filter which TarotReadings to update
     */
    where?: TarotReadingWhereInput
    /**
     * Limit how many TarotReadings to update.
     */
    limit?: number
  }

  /**
   * TarotReading updateManyAndReturn
   */
  export type TarotReadingUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotReading
     */
    select?: TarotReadingSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the TarotReading
     */
    omit?: TarotReadingOmit<ExtArgs> | null
    /**
     * The data used to update TarotReadings.
     */
    data: XOR<TarotReadingUpdateManyMutationInput, TarotReadingUncheckedUpdateManyInput>
    /**
     * Filter which TarotReadings to update
     */
    where?: TarotReadingWhereInput
    /**
     * Limit how many TarotReadings to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TarotReadingIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * TarotReading upsert
   */
  export type TarotReadingUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotReading
     */
    select?: TarotReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotReading
     */
    omit?: TarotReadingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TarotReadingInclude<ExtArgs> | null
    /**
     * The filter to search for the TarotReading to update in case it exists.
     */
    where: TarotReadingWhereUniqueInput
    /**
     * In case the TarotReading found by the `where` argument doesn't exist, create a new TarotReading with this data.
     */
    create: XOR<TarotReadingCreateInput, TarotReadingUncheckedCreateInput>
    /**
     * In case the TarotReading was found with the provided `where` argument, update it with this data.
     */
    update: XOR<TarotReadingUpdateInput, TarotReadingUncheckedUpdateInput>
  }

  /**
   * TarotReading delete
   */
  export type TarotReadingDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotReading
     */
    select?: TarotReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotReading
     */
    omit?: TarotReadingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TarotReadingInclude<ExtArgs> | null
    /**
     * Filter which TarotReading to delete.
     */
    where: TarotReadingWhereUniqueInput
  }

  /**
   * TarotReading deleteMany
   */
  export type TarotReadingDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which TarotReadings to delete
     */
    where?: TarotReadingWhereInput
    /**
     * Limit how many TarotReadings to delete.
     */
    limit?: number
  }

  /**
   * TarotReading.user
   */
  export type TarotReading$userArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    where?: UserWhereInput
  }

  /**
   * TarotReading without action
   */
  export type TarotReadingDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TarotReading
     */
    select?: TarotReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TarotReading
     */
    omit?: TarotReadingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TarotReadingInclude<ExtArgs> | null
  }


  /**
   * Model UserQuestionHistory
   */

  export type AggregateUserQuestionHistory = {
    _count: UserQuestionHistoryCountAggregateOutputType | null
    _min: UserQuestionHistoryMinAggregateOutputType | null
    _max: UserQuestionHistoryMaxAggregateOutputType | null
  }

  export type UserQuestionHistoryMinAggregateOutputType = {
    id: string | null
    userId: string | null
    question: string | null
    questionType: string | null
    spreadType: string | null
    consultantName: string | null
    nameType: string | null
    geminiResponse: string | null
    responseSummary: string | null
    responseDate: string | null
    ipAddress: string | null
    userAgent: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserQuestionHistoryMaxAggregateOutputType = {
    id: string | null
    userId: string | null
    question: string | null
    questionType: string | null
    spreadType: string | null
    consultantName: string | null
    nameType: string | null
    geminiResponse: string | null
    responseSummary: string | null
    responseDate: string | null
    ipAddress: string | null
    userAgent: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserQuestionHistoryCountAggregateOutputType = {
    id: number
    userId: number
    question: number
    questionType: number
    spreadType: number
    consultantName: number
    nameType: number
    geminiResponse: number
    responseSummary: number
    responseDate: number
    ipAddress: number
    userAgent: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type UserQuestionHistoryMinAggregateInputType = {
    id?: true
    userId?: true
    question?: true
    questionType?: true
    spreadType?: true
    consultantName?: true
    nameType?: true
    geminiResponse?: true
    responseSummary?: true
    responseDate?: true
    ipAddress?: true
    userAgent?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserQuestionHistoryMaxAggregateInputType = {
    id?: true
    userId?: true
    question?: true
    questionType?: true
    spreadType?: true
    consultantName?: true
    nameType?: true
    geminiResponse?: true
    responseSummary?: true
    responseDate?: true
    ipAddress?: true
    userAgent?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserQuestionHistoryCountAggregateInputType = {
    id?: true
    userId?: true
    question?: true
    questionType?: true
    spreadType?: true
    consultantName?: true
    nameType?: true
    geminiResponse?: true
    responseSummary?: true
    responseDate?: true
    ipAddress?: true
    userAgent?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type UserQuestionHistoryAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which UserQuestionHistory to aggregate.
     */
    where?: UserQuestionHistoryWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserQuestionHistories to fetch.
     */
    orderBy?: UserQuestionHistoryOrderByWithRelationInput | UserQuestionHistoryOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserQuestionHistoryWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserQuestionHistories from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserQuestionHistories.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned UserQuestionHistories
    **/
    _count?: true | UserQuestionHistoryCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserQuestionHistoryMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserQuestionHistoryMaxAggregateInputType
  }

  export type GetUserQuestionHistoryAggregateType<T extends UserQuestionHistoryAggregateArgs> = {
        [P in keyof T & keyof AggregateUserQuestionHistory]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUserQuestionHistory[P]>
      : GetScalarType<T[P], AggregateUserQuestionHistory[P]>
  }




  export type UserQuestionHistoryGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserQuestionHistoryWhereInput
    orderBy?: UserQuestionHistoryOrderByWithAggregationInput | UserQuestionHistoryOrderByWithAggregationInput[]
    by: UserQuestionHistoryScalarFieldEnum[] | UserQuestionHistoryScalarFieldEnum
    having?: UserQuestionHistoryScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserQuestionHistoryCountAggregateInputType | true
    _min?: UserQuestionHistoryMinAggregateInputType
    _max?: UserQuestionHistoryMaxAggregateInputType
  }

  export type UserQuestionHistoryGroupByOutputType = {
    id: string
    userId: string | null
    question: string
    questionType: string
    spreadType: string | null
    consultantName: string | null
    nameType: string | null
    geminiResponse: string
    responseSummary: string
    responseDate: string
    ipAddress: string | null
    userAgent: string | null
    createdAt: Date
    updatedAt: Date
    _count: UserQuestionHistoryCountAggregateOutputType | null
    _min: UserQuestionHistoryMinAggregateOutputType | null
    _max: UserQuestionHistoryMaxAggregateOutputType | null
  }

  type GetUserQuestionHistoryGroupByPayload<T extends UserQuestionHistoryGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserQuestionHistoryGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserQuestionHistoryGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserQuestionHistoryGroupByOutputType[P]>
            : GetScalarType<T[P], UserQuestionHistoryGroupByOutputType[P]>
        }
      >
    >


  export type UserQuestionHistorySelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    question?: boolean
    questionType?: boolean
    spreadType?: boolean
    consultantName?: boolean
    nameType?: boolean
    geminiResponse?: boolean
    responseSummary?: boolean
    responseDate?: boolean
    ipAddress?: boolean
    userAgent?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserQuestionHistory$userArgs<ExtArgs>
  }, ExtArgs["result"]["userQuestionHistory"]>

  export type UserQuestionHistorySelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    question?: boolean
    questionType?: boolean
    spreadType?: boolean
    consultantName?: boolean
    nameType?: boolean
    geminiResponse?: boolean
    responseSummary?: boolean
    responseDate?: boolean
    ipAddress?: boolean
    userAgent?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserQuestionHistory$userArgs<ExtArgs>
  }, ExtArgs["result"]["userQuestionHistory"]>

  export type UserQuestionHistorySelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    question?: boolean
    questionType?: boolean
    spreadType?: boolean
    consultantName?: boolean
    nameType?: boolean
    geminiResponse?: boolean
    responseSummary?: boolean
    responseDate?: boolean
    ipAddress?: boolean
    userAgent?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserQuestionHistory$userArgs<ExtArgs>
  }, ExtArgs["result"]["userQuestionHistory"]>

  export type UserQuestionHistorySelectScalar = {
    id?: boolean
    userId?: boolean
    question?: boolean
    questionType?: boolean
    spreadType?: boolean
    consultantName?: boolean
    nameType?: boolean
    geminiResponse?: boolean
    responseSummary?: boolean
    responseDate?: boolean
    ipAddress?: boolean
    userAgent?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type UserQuestionHistoryOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "userId" | "question" | "questionType" | "spreadType" | "consultantName" | "nameType" | "geminiResponse" | "responseSummary" | "responseDate" | "ipAddress" | "userAgent" | "createdAt" | "updatedAt", ExtArgs["result"]["userQuestionHistory"]>
  export type UserQuestionHistoryInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserQuestionHistory$userArgs<ExtArgs>
  }
  export type UserQuestionHistoryIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserQuestionHistory$userArgs<ExtArgs>
  }
  export type UserQuestionHistoryIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserQuestionHistory$userArgs<ExtArgs>
  }

  export type $UserQuestionHistoryPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "UserQuestionHistory"
    objects: {
      user: Prisma.$UserPayload<ExtArgs> | null
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      userId: string | null
      question: string
      questionType: string
      spreadType: string | null
      consultantName: string | null
      nameType: string | null
      geminiResponse: string
      responseSummary: string
      responseDate: string
      ipAddress: string | null
      userAgent: string | null
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["userQuestionHistory"]>
    composites: {}
  }

  type UserQuestionHistoryGetPayload<S extends boolean | null | undefined | UserQuestionHistoryDefaultArgs> = $Result.GetResult<Prisma.$UserQuestionHistoryPayload, S>

  type UserQuestionHistoryCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<UserQuestionHistoryFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: UserQuestionHistoryCountAggregateInputType | true
    }

  export interface UserQuestionHistoryDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['UserQuestionHistory'], meta: { name: 'UserQuestionHistory' } }
    /**
     * Find zero or one UserQuestionHistory that matches the filter.
     * @param {UserQuestionHistoryFindUniqueArgs} args - Arguments to find a UserQuestionHistory
     * @example
     * // Get one UserQuestionHistory
     * const userQuestionHistory = await prisma.userQuestionHistory.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserQuestionHistoryFindUniqueArgs>(args: SelectSubset<T, UserQuestionHistoryFindUniqueArgs<ExtArgs>>): Prisma__UserQuestionHistoryClient<$Result.GetResult<Prisma.$UserQuestionHistoryPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one UserQuestionHistory that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {UserQuestionHistoryFindUniqueOrThrowArgs} args - Arguments to find a UserQuestionHistory
     * @example
     * // Get one UserQuestionHistory
     * const userQuestionHistory = await prisma.userQuestionHistory.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserQuestionHistoryFindUniqueOrThrowArgs>(args: SelectSubset<T, UserQuestionHistoryFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserQuestionHistoryClient<$Result.GetResult<Prisma.$UserQuestionHistoryPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first UserQuestionHistory that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserQuestionHistoryFindFirstArgs} args - Arguments to find a UserQuestionHistory
     * @example
     * // Get one UserQuestionHistory
     * const userQuestionHistory = await prisma.userQuestionHistory.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserQuestionHistoryFindFirstArgs>(args?: SelectSubset<T, UserQuestionHistoryFindFirstArgs<ExtArgs>>): Prisma__UserQuestionHistoryClient<$Result.GetResult<Prisma.$UserQuestionHistoryPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first UserQuestionHistory that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserQuestionHistoryFindFirstOrThrowArgs} args - Arguments to find a UserQuestionHistory
     * @example
     * // Get one UserQuestionHistory
     * const userQuestionHistory = await prisma.userQuestionHistory.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserQuestionHistoryFindFirstOrThrowArgs>(args?: SelectSubset<T, UserQuestionHistoryFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserQuestionHistoryClient<$Result.GetResult<Prisma.$UserQuestionHistoryPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more UserQuestionHistories that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserQuestionHistoryFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all UserQuestionHistories
     * const userQuestionHistories = await prisma.userQuestionHistory.findMany()
     * 
     * // Get first 10 UserQuestionHistories
     * const userQuestionHistories = await prisma.userQuestionHistory.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userQuestionHistoryWithIdOnly = await prisma.userQuestionHistory.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserQuestionHistoryFindManyArgs>(args?: SelectSubset<T, UserQuestionHistoryFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserQuestionHistoryPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a UserQuestionHistory.
     * @param {UserQuestionHistoryCreateArgs} args - Arguments to create a UserQuestionHistory.
     * @example
     * // Create one UserQuestionHistory
     * const UserQuestionHistory = await prisma.userQuestionHistory.create({
     *   data: {
     *     // ... data to create a UserQuestionHistory
     *   }
     * })
     * 
     */
    create<T extends UserQuestionHistoryCreateArgs>(args: SelectSubset<T, UserQuestionHistoryCreateArgs<ExtArgs>>): Prisma__UserQuestionHistoryClient<$Result.GetResult<Prisma.$UserQuestionHistoryPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many UserQuestionHistories.
     * @param {UserQuestionHistoryCreateManyArgs} args - Arguments to create many UserQuestionHistories.
     * @example
     * // Create many UserQuestionHistories
     * const userQuestionHistory = await prisma.userQuestionHistory.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserQuestionHistoryCreateManyArgs>(args?: SelectSubset<T, UserQuestionHistoryCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many UserQuestionHistories and returns the data saved in the database.
     * @param {UserQuestionHistoryCreateManyAndReturnArgs} args - Arguments to create many UserQuestionHistories.
     * @example
     * // Create many UserQuestionHistories
     * const userQuestionHistory = await prisma.userQuestionHistory.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many UserQuestionHistories and only return the `id`
     * const userQuestionHistoryWithIdOnly = await prisma.userQuestionHistory.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UserQuestionHistoryCreateManyAndReturnArgs>(args?: SelectSubset<T, UserQuestionHistoryCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserQuestionHistoryPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a UserQuestionHistory.
     * @param {UserQuestionHistoryDeleteArgs} args - Arguments to delete one UserQuestionHistory.
     * @example
     * // Delete one UserQuestionHistory
     * const UserQuestionHistory = await prisma.userQuestionHistory.delete({
     *   where: {
     *     // ... filter to delete one UserQuestionHistory
     *   }
     * })
     * 
     */
    delete<T extends UserQuestionHistoryDeleteArgs>(args: SelectSubset<T, UserQuestionHistoryDeleteArgs<ExtArgs>>): Prisma__UserQuestionHistoryClient<$Result.GetResult<Prisma.$UserQuestionHistoryPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one UserQuestionHistory.
     * @param {UserQuestionHistoryUpdateArgs} args - Arguments to update one UserQuestionHistory.
     * @example
     * // Update one UserQuestionHistory
     * const userQuestionHistory = await prisma.userQuestionHistory.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserQuestionHistoryUpdateArgs>(args: SelectSubset<T, UserQuestionHistoryUpdateArgs<ExtArgs>>): Prisma__UserQuestionHistoryClient<$Result.GetResult<Prisma.$UserQuestionHistoryPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more UserQuestionHistories.
     * @param {UserQuestionHistoryDeleteManyArgs} args - Arguments to filter UserQuestionHistories to delete.
     * @example
     * // Delete a few UserQuestionHistories
     * const { count } = await prisma.userQuestionHistory.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserQuestionHistoryDeleteManyArgs>(args?: SelectSubset<T, UserQuestionHistoryDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more UserQuestionHistories.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserQuestionHistoryUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many UserQuestionHistories
     * const userQuestionHistory = await prisma.userQuestionHistory.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserQuestionHistoryUpdateManyArgs>(args: SelectSubset<T, UserQuestionHistoryUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more UserQuestionHistories and returns the data updated in the database.
     * @param {UserQuestionHistoryUpdateManyAndReturnArgs} args - Arguments to update many UserQuestionHistories.
     * @example
     * // Update many UserQuestionHistories
     * const userQuestionHistory = await prisma.userQuestionHistory.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more UserQuestionHistories and only return the `id`
     * const userQuestionHistoryWithIdOnly = await prisma.userQuestionHistory.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends UserQuestionHistoryUpdateManyAndReturnArgs>(args: SelectSubset<T, UserQuestionHistoryUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserQuestionHistoryPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one UserQuestionHistory.
     * @param {UserQuestionHistoryUpsertArgs} args - Arguments to update or create a UserQuestionHistory.
     * @example
     * // Update or create a UserQuestionHistory
     * const userQuestionHistory = await prisma.userQuestionHistory.upsert({
     *   create: {
     *     // ... data to create a UserQuestionHistory
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the UserQuestionHistory we want to update
     *   }
     * })
     */
    upsert<T extends UserQuestionHistoryUpsertArgs>(args: SelectSubset<T, UserQuestionHistoryUpsertArgs<ExtArgs>>): Prisma__UserQuestionHistoryClient<$Result.GetResult<Prisma.$UserQuestionHistoryPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of UserQuestionHistories.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserQuestionHistoryCountArgs} args - Arguments to filter UserQuestionHistories to count.
     * @example
     * // Count the number of UserQuestionHistories
     * const count = await prisma.userQuestionHistory.count({
     *   where: {
     *     // ... the filter for the UserQuestionHistories we want to count
     *   }
     * })
    **/
    count<T extends UserQuestionHistoryCountArgs>(
      args?: Subset<T, UserQuestionHistoryCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserQuestionHistoryCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a UserQuestionHistory.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserQuestionHistoryAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserQuestionHistoryAggregateArgs>(args: Subset<T, UserQuestionHistoryAggregateArgs>): Prisma.PrismaPromise<GetUserQuestionHistoryAggregateType<T>>

    /**
     * Group by UserQuestionHistory.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserQuestionHistoryGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserQuestionHistoryGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserQuestionHistoryGroupByArgs['orderBy'] }
        : { orderBy?: UserQuestionHistoryGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserQuestionHistoryGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserQuestionHistoryGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the UserQuestionHistory model
   */
  readonly fields: UserQuestionHistoryFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for UserQuestionHistory.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserQuestionHistoryClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserQuestionHistory$userArgs<ExtArgs> = {}>(args?: Subset<T, UserQuestionHistory$userArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the UserQuestionHistory model
   */
  interface UserQuestionHistoryFieldRefs {
    readonly id: FieldRef<"UserQuestionHistory", 'String'>
    readonly userId: FieldRef<"UserQuestionHistory", 'String'>
    readonly question: FieldRef<"UserQuestionHistory", 'String'>
    readonly questionType: FieldRef<"UserQuestionHistory", 'String'>
    readonly spreadType: FieldRef<"UserQuestionHistory", 'String'>
    readonly consultantName: FieldRef<"UserQuestionHistory", 'String'>
    readonly nameType: FieldRef<"UserQuestionHistory", 'String'>
    readonly geminiResponse: FieldRef<"UserQuestionHistory", 'String'>
    readonly responseSummary: FieldRef<"UserQuestionHistory", 'String'>
    readonly responseDate: FieldRef<"UserQuestionHistory", 'String'>
    readonly ipAddress: FieldRef<"UserQuestionHistory", 'String'>
    readonly userAgent: FieldRef<"UserQuestionHistory", 'String'>
    readonly createdAt: FieldRef<"UserQuestionHistory", 'DateTime'>
    readonly updatedAt: FieldRef<"UserQuestionHistory", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * UserQuestionHistory findUnique
   */
  export type UserQuestionHistoryFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserQuestionHistory
     */
    select?: UserQuestionHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserQuestionHistory
     */
    omit?: UserQuestionHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserQuestionHistoryInclude<ExtArgs> | null
    /**
     * Filter, which UserQuestionHistory to fetch.
     */
    where: UserQuestionHistoryWhereUniqueInput
  }

  /**
   * UserQuestionHistory findUniqueOrThrow
   */
  export type UserQuestionHistoryFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserQuestionHistory
     */
    select?: UserQuestionHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserQuestionHistory
     */
    omit?: UserQuestionHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserQuestionHistoryInclude<ExtArgs> | null
    /**
     * Filter, which UserQuestionHistory to fetch.
     */
    where: UserQuestionHistoryWhereUniqueInput
  }

  /**
   * UserQuestionHistory findFirst
   */
  export type UserQuestionHistoryFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserQuestionHistory
     */
    select?: UserQuestionHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserQuestionHistory
     */
    omit?: UserQuestionHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserQuestionHistoryInclude<ExtArgs> | null
    /**
     * Filter, which UserQuestionHistory to fetch.
     */
    where?: UserQuestionHistoryWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserQuestionHistories to fetch.
     */
    orderBy?: UserQuestionHistoryOrderByWithRelationInput | UserQuestionHistoryOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for UserQuestionHistories.
     */
    cursor?: UserQuestionHistoryWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserQuestionHistories from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserQuestionHistories.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of UserQuestionHistories.
     */
    distinct?: UserQuestionHistoryScalarFieldEnum | UserQuestionHistoryScalarFieldEnum[]
  }

  /**
   * UserQuestionHistory findFirstOrThrow
   */
  export type UserQuestionHistoryFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserQuestionHistory
     */
    select?: UserQuestionHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserQuestionHistory
     */
    omit?: UserQuestionHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserQuestionHistoryInclude<ExtArgs> | null
    /**
     * Filter, which UserQuestionHistory to fetch.
     */
    where?: UserQuestionHistoryWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserQuestionHistories to fetch.
     */
    orderBy?: UserQuestionHistoryOrderByWithRelationInput | UserQuestionHistoryOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for UserQuestionHistories.
     */
    cursor?: UserQuestionHistoryWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserQuestionHistories from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserQuestionHistories.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of UserQuestionHistories.
     */
    distinct?: UserQuestionHistoryScalarFieldEnum | UserQuestionHistoryScalarFieldEnum[]
  }

  /**
   * UserQuestionHistory findMany
   */
  export type UserQuestionHistoryFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserQuestionHistory
     */
    select?: UserQuestionHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserQuestionHistory
     */
    omit?: UserQuestionHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserQuestionHistoryInclude<ExtArgs> | null
    /**
     * Filter, which UserQuestionHistories to fetch.
     */
    where?: UserQuestionHistoryWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserQuestionHistories to fetch.
     */
    orderBy?: UserQuestionHistoryOrderByWithRelationInput | UserQuestionHistoryOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing UserQuestionHistories.
     */
    cursor?: UserQuestionHistoryWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserQuestionHistories from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserQuestionHistories.
     */
    skip?: number
    distinct?: UserQuestionHistoryScalarFieldEnum | UserQuestionHistoryScalarFieldEnum[]
  }

  /**
   * UserQuestionHistory create
   */
  export type UserQuestionHistoryCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserQuestionHistory
     */
    select?: UserQuestionHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserQuestionHistory
     */
    omit?: UserQuestionHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserQuestionHistoryInclude<ExtArgs> | null
    /**
     * The data needed to create a UserQuestionHistory.
     */
    data: XOR<UserQuestionHistoryCreateInput, UserQuestionHistoryUncheckedCreateInput>
  }

  /**
   * UserQuestionHistory createMany
   */
  export type UserQuestionHistoryCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many UserQuestionHistories.
     */
    data: UserQuestionHistoryCreateManyInput | UserQuestionHistoryCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * UserQuestionHistory createManyAndReturn
   */
  export type UserQuestionHistoryCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserQuestionHistory
     */
    select?: UserQuestionHistorySelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the UserQuestionHistory
     */
    omit?: UserQuestionHistoryOmit<ExtArgs> | null
    /**
     * The data used to create many UserQuestionHistories.
     */
    data: UserQuestionHistoryCreateManyInput | UserQuestionHistoryCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserQuestionHistoryIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * UserQuestionHistory update
   */
  export type UserQuestionHistoryUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserQuestionHistory
     */
    select?: UserQuestionHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserQuestionHistory
     */
    omit?: UserQuestionHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserQuestionHistoryInclude<ExtArgs> | null
    /**
     * The data needed to update a UserQuestionHistory.
     */
    data: XOR<UserQuestionHistoryUpdateInput, UserQuestionHistoryUncheckedUpdateInput>
    /**
     * Choose, which UserQuestionHistory to update.
     */
    where: UserQuestionHistoryWhereUniqueInput
  }

  /**
   * UserQuestionHistory updateMany
   */
  export type UserQuestionHistoryUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update UserQuestionHistories.
     */
    data: XOR<UserQuestionHistoryUpdateManyMutationInput, UserQuestionHistoryUncheckedUpdateManyInput>
    /**
     * Filter which UserQuestionHistories to update
     */
    where?: UserQuestionHistoryWhereInput
    /**
     * Limit how many UserQuestionHistories to update.
     */
    limit?: number
  }

  /**
   * UserQuestionHistory updateManyAndReturn
   */
  export type UserQuestionHistoryUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserQuestionHistory
     */
    select?: UserQuestionHistorySelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the UserQuestionHistory
     */
    omit?: UserQuestionHistoryOmit<ExtArgs> | null
    /**
     * The data used to update UserQuestionHistories.
     */
    data: XOR<UserQuestionHistoryUpdateManyMutationInput, UserQuestionHistoryUncheckedUpdateManyInput>
    /**
     * Filter which UserQuestionHistories to update
     */
    where?: UserQuestionHistoryWhereInput
    /**
     * Limit how many UserQuestionHistories to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserQuestionHistoryIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * UserQuestionHistory upsert
   */
  export type UserQuestionHistoryUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserQuestionHistory
     */
    select?: UserQuestionHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserQuestionHistory
     */
    omit?: UserQuestionHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserQuestionHistoryInclude<ExtArgs> | null
    /**
     * The filter to search for the UserQuestionHistory to update in case it exists.
     */
    where: UserQuestionHistoryWhereUniqueInput
    /**
     * In case the UserQuestionHistory found by the `where` argument doesn't exist, create a new UserQuestionHistory with this data.
     */
    create: XOR<UserQuestionHistoryCreateInput, UserQuestionHistoryUncheckedCreateInput>
    /**
     * In case the UserQuestionHistory was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserQuestionHistoryUpdateInput, UserQuestionHistoryUncheckedUpdateInput>
  }

  /**
   * UserQuestionHistory delete
   */
  export type UserQuestionHistoryDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserQuestionHistory
     */
    select?: UserQuestionHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserQuestionHistory
     */
    omit?: UserQuestionHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserQuestionHistoryInclude<ExtArgs> | null
    /**
     * Filter which UserQuestionHistory to delete.
     */
    where: UserQuestionHistoryWhereUniqueInput
  }

  /**
   * UserQuestionHistory deleteMany
   */
  export type UserQuestionHistoryDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which UserQuestionHistories to delete
     */
    where?: UserQuestionHistoryWhereInput
    /**
     * Limit how many UserQuestionHistories to delete.
     */
    limit?: number
  }

  /**
   * UserQuestionHistory.user
   */
  export type UserQuestionHistory$userArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    where?: UserWhereInput
  }

  /**
   * UserQuestionHistory without action
   */
  export type UserQuestionHistoryDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserQuestionHistory
     */
    select?: UserQuestionHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserQuestionHistory
     */
    omit?: UserQuestionHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserQuestionHistoryInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const UserScalarFieldEnum: {
    id: 'id',
    email: 'email',
    hashedPassword: 'hashedPassword',
    name: 'name',
    credits: 'credits',
    lastCreditDeduction: 'lastCreditDeduction',
    googleId: 'googleId',
    kakaoId: 'kakaoId',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


  export const UserFortuneCooldownScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    fortuneType: 'fortuneType',
    cooldownExpiresAt: 'cooldownExpiresAt',
    createdAt: 'createdAt'
  };

  export type UserFortuneCooldownScalarFieldEnum = (typeof UserFortuneCooldownScalarFieldEnum)[keyof typeof UserFortuneCooldownScalarFieldEnum]


  export const IPFortuneCooldownScalarFieldEnum: {
    id: 'id',
    ipAddress: 'ipAddress',
    fortuneType: 'fortuneType',
    cooldownExpiresAt: 'cooldownExpiresAt',
    createdAt: 'createdAt'
  };

  export type IPFortuneCooldownScalarFieldEnum = (typeof IPFortuneCooldownScalarFieldEnum)[keyof typeof IPFortuneCooldownScalarFieldEnum]


  export const TarotSpreadScalarFieldEnum: {
    id: 'id',
    name: 'name',
    description: 'description',
    cardCount: 'cardCount',
    cost: 'cost',
    iconLayout: 'iconLayout',
    spreadType: 'spreadType',
    layoutDescription: 'layoutDescription',
    className: 'className',
    positions: 'positions',
    discount: 'discount',
    discountStartDate: 'discountStartDate',
    discountEndDate: 'discountEndDate',
    isActive: 'isActive',
    order: 'order',
    promptTemplate: 'promptTemplate',
    systemInstruction: 'systemInstruction',
    cardPositionLabels: 'cardPositionLabels',
    customVariables: 'customVariables',
    cardIntroTemplates: 'cardIntroTemplates',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type TarotSpreadScalarFieldEnum = (typeof TarotSpreadScalarFieldEnum)[keyof typeof TarotSpreadScalarFieldEnum]


  export const TarotGlobalSettingsScalarFieldEnum: {
    id: 'id',
    settingKey: 'settingKey',
    settingValue: 'settingValue',
    description: 'description',
    isActive: 'isActive',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type TarotGlobalSettingsScalarFieldEnum = (typeof TarotGlobalSettingsScalarFieldEnum)[keyof typeof TarotGlobalSettingsScalarFieldEnum]


  export const TarotReadingScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    readingType: 'readingType',
    spreadType: 'spreadType',
    cardCount: 'cardCount',
    promptTokens: 'promptTokens',
    completionTokens: 'completionTokens',
    totalTokens: 'totalTokens',
    ipAddress: 'ipAddress',
    userAgent: 'userAgent',
    createdAt: 'createdAt'
  };

  export type TarotReadingScalarFieldEnum = (typeof TarotReadingScalarFieldEnum)[keyof typeof TarotReadingScalarFieldEnum]


  export const UserQuestionHistoryScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    question: 'question',
    questionType: 'questionType',
    spreadType: 'spreadType',
    consultantName: 'consultantName',
    nameType: 'nameType',
    geminiResponse: 'geminiResponse',
    responseSummary: 'responseSummary',
    responseDate: 'responseDate',
    ipAddress: 'ipAddress',
    userAgent: 'userAgent',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type UserQuestionHistoryScalarFieldEnum = (typeof UserQuestionHistoryScalarFieldEnum)[keyof typeof UserQuestionHistoryScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'String[]'
   */
  export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Int[]'
   */
  export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'DateTime[]'
   */
  export type ListDateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime[]'>
    


  /**
   * Reference to a field of type 'Boolean'
   */
  export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


  /**
   * Reference to a field of type 'Float[]'
   */
  export type ListFloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float[]'>
    
  /**
   * Deep Input Types
   */


  export type UserWhereInput = {
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    id?: StringFilter<"User"> | string
    email?: StringFilter<"User"> | string
    hashedPassword?: StringNullableFilter<"User"> | string | null
    name?: StringNullableFilter<"User"> | string | null
    credits?: IntFilter<"User"> | number
    lastCreditDeduction?: DateTimeNullableFilter<"User"> | Date | string | null
    googleId?: StringNullableFilter<"User"> | string | null
    kakaoId?: StringNullableFilter<"User"> | string | null
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    cooldowns?: UserFortuneCooldownListRelationFilter
    tarotReadings?: TarotReadingListRelationFilter
    questionHistory?: UserQuestionHistoryListRelationFilter
  }

  export type UserOrderByWithRelationInput = {
    id?: SortOrder
    email?: SortOrder
    hashedPassword?: SortOrderInput | SortOrder
    name?: SortOrderInput | SortOrder
    credits?: SortOrder
    lastCreditDeduction?: SortOrderInput | SortOrder
    googleId?: SortOrderInput | SortOrder
    kakaoId?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    cooldowns?: UserFortuneCooldownOrderByRelationAggregateInput
    tarotReadings?: TarotReadingOrderByRelationAggregateInput
    questionHistory?: UserQuestionHistoryOrderByRelationAggregateInput
  }

  export type UserWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    email?: string
    googleId?: string
    kakaoId?: string
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    hashedPassword?: StringNullableFilter<"User"> | string | null
    name?: StringNullableFilter<"User"> | string | null
    credits?: IntFilter<"User"> | number
    lastCreditDeduction?: DateTimeNullableFilter<"User"> | Date | string | null
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    cooldowns?: UserFortuneCooldownListRelationFilter
    tarotReadings?: TarotReadingListRelationFilter
    questionHistory?: UserQuestionHistoryListRelationFilter
  }, "id" | "email" | "googleId" | "kakaoId">

  export type UserOrderByWithAggregationInput = {
    id?: SortOrder
    email?: SortOrder
    hashedPassword?: SortOrderInput | SortOrder
    name?: SortOrderInput | SortOrder
    credits?: SortOrder
    lastCreditDeduction?: SortOrderInput | SortOrder
    googleId?: SortOrderInput | SortOrder
    kakaoId?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: UserCountOrderByAggregateInput
    _avg?: UserAvgOrderByAggregateInput
    _max?: UserMaxOrderByAggregateInput
    _min?: UserMinOrderByAggregateInput
    _sum?: UserSumOrderByAggregateInput
  }

  export type UserScalarWhereWithAggregatesInput = {
    AND?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    OR?: UserScalarWhereWithAggregatesInput[]
    NOT?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"User"> | string
    email?: StringWithAggregatesFilter<"User"> | string
    hashedPassword?: StringNullableWithAggregatesFilter<"User"> | string | null
    name?: StringNullableWithAggregatesFilter<"User"> | string | null
    credits?: IntWithAggregatesFilter<"User"> | number
    lastCreditDeduction?: DateTimeNullableWithAggregatesFilter<"User"> | Date | string | null
    googleId?: StringNullableWithAggregatesFilter<"User"> | string | null
    kakaoId?: StringNullableWithAggregatesFilter<"User"> | string | null
    createdAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
  }

  export type UserFortuneCooldownWhereInput = {
    AND?: UserFortuneCooldownWhereInput | UserFortuneCooldownWhereInput[]
    OR?: UserFortuneCooldownWhereInput[]
    NOT?: UserFortuneCooldownWhereInput | UserFortuneCooldownWhereInput[]
    id?: StringFilter<"UserFortuneCooldown"> | string
    userId?: StringFilter<"UserFortuneCooldown"> | string
    fortuneType?: StringFilter<"UserFortuneCooldown"> | string
    cooldownExpiresAt?: DateTimeFilter<"UserFortuneCooldown"> | Date | string
    createdAt?: DateTimeFilter<"UserFortuneCooldown"> | Date | string
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
  }

  export type UserFortuneCooldownOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrder
    fortuneType?: SortOrder
    cooldownExpiresAt?: SortOrder
    createdAt?: SortOrder
    user?: UserOrderByWithRelationInput
  }

  export type UserFortuneCooldownWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    userId_fortuneType?: UserFortuneCooldownUserIdFortuneTypeCompoundUniqueInput
    AND?: UserFortuneCooldownWhereInput | UserFortuneCooldownWhereInput[]
    OR?: UserFortuneCooldownWhereInput[]
    NOT?: UserFortuneCooldownWhereInput | UserFortuneCooldownWhereInput[]
    userId?: StringFilter<"UserFortuneCooldown"> | string
    fortuneType?: StringFilter<"UserFortuneCooldown"> | string
    cooldownExpiresAt?: DateTimeFilter<"UserFortuneCooldown"> | Date | string
    createdAt?: DateTimeFilter<"UserFortuneCooldown"> | Date | string
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
  }, "id" | "userId_fortuneType">

  export type UserFortuneCooldownOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrder
    fortuneType?: SortOrder
    cooldownExpiresAt?: SortOrder
    createdAt?: SortOrder
    _count?: UserFortuneCooldownCountOrderByAggregateInput
    _max?: UserFortuneCooldownMaxOrderByAggregateInput
    _min?: UserFortuneCooldownMinOrderByAggregateInput
  }

  export type UserFortuneCooldownScalarWhereWithAggregatesInput = {
    AND?: UserFortuneCooldownScalarWhereWithAggregatesInput | UserFortuneCooldownScalarWhereWithAggregatesInput[]
    OR?: UserFortuneCooldownScalarWhereWithAggregatesInput[]
    NOT?: UserFortuneCooldownScalarWhereWithAggregatesInput | UserFortuneCooldownScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"UserFortuneCooldown"> | string
    userId?: StringWithAggregatesFilter<"UserFortuneCooldown"> | string
    fortuneType?: StringWithAggregatesFilter<"UserFortuneCooldown"> | string
    cooldownExpiresAt?: DateTimeWithAggregatesFilter<"UserFortuneCooldown"> | Date | string
    createdAt?: DateTimeWithAggregatesFilter<"UserFortuneCooldown"> | Date | string
  }

  export type IPFortuneCooldownWhereInput = {
    AND?: IPFortuneCooldownWhereInput | IPFortuneCooldownWhereInput[]
    OR?: IPFortuneCooldownWhereInput[]
    NOT?: IPFortuneCooldownWhereInput | IPFortuneCooldownWhereInput[]
    id?: StringFilter<"IPFortuneCooldown"> | string
    ipAddress?: StringFilter<"IPFortuneCooldown"> | string
    fortuneType?: StringFilter<"IPFortuneCooldown"> | string
    cooldownExpiresAt?: DateTimeFilter<"IPFortuneCooldown"> | Date | string
    createdAt?: DateTimeFilter<"IPFortuneCooldown"> | Date | string
  }

  export type IPFortuneCooldownOrderByWithRelationInput = {
    id?: SortOrder
    ipAddress?: SortOrder
    fortuneType?: SortOrder
    cooldownExpiresAt?: SortOrder
    createdAt?: SortOrder
  }

  export type IPFortuneCooldownWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    ipAddress_fortuneType?: IPFortuneCooldownIpAddressFortuneTypeCompoundUniqueInput
    AND?: IPFortuneCooldownWhereInput | IPFortuneCooldownWhereInput[]
    OR?: IPFortuneCooldownWhereInput[]
    NOT?: IPFortuneCooldownWhereInput | IPFortuneCooldownWhereInput[]
    ipAddress?: StringFilter<"IPFortuneCooldown"> | string
    fortuneType?: StringFilter<"IPFortuneCooldown"> | string
    cooldownExpiresAt?: DateTimeFilter<"IPFortuneCooldown"> | Date | string
    createdAt?: DateTimeFilter<"IPFortuneCooldown"> | Date | string
  }, "id" | "ipAddress_fortuneType">

  export type IPFortuneCooldownOrderByWithAggregationInput = {
    id?: SortOrder
    ipAddress?: SortOrder
    fortuneType?: SortOrder
    cooldownExpiresAt?: SortOrder
    createdAt?: SortOrder
    _count?: IPFortuneCooldownCountOrderByAggregateInput
    _max?: IPFortuneCooldownMaxOrderByAggregateInput
    _min?: IPFortuneCooldownMinOrderByAggregateInput
  }

  export type IPFortuneCooldownScalarWhereWithAggregatesInput = {
    AND?: IPFortuneCooldownScalarWhereWithAggregatesInput | IPFortuneCooldownScalarWhereWithAggregatesInput[]
    OR?: IPFortuneCooldownScalarWhereWithAggregatesInput[]
    NOT?: IPFortuneCooldownScalarWhereWithAggregatesInput | IPFortuneCooldownScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"IPFortuneCooldown"> | string
    ipAddress?: StringWithAggregatesFilter<"IPFortuneCooldown"> | string
    fortuneType?: StringWithAggregatesFilter<"IPFortuneCooldown"> | string
    cooldownExpiresAt?: DateTimeWithAggregatesFilter<"IPFortuneCooldown"> | Date | string
    createdAt?: DateTimeWithAggregatesFilter<"IPFortuneCooldown"> | Date | string
  }

  export type TarotSpreadWhereInput = {
    AND?: TarotSpreadWhereInput | TarotSpreadWhereInput[]
    OR?: TarotSpreadWhereInput[]
    NOT?: TarotSpreadWhereInput | TarotSpreadWhereInput[]
    id?: StringFilter<"TarotSpread"> | string
    name?: StringFilter<"TarotSpread"> | string
    description?: StringFilter<"TarotSpread"> | string
    cardCount?: IntFilter<"TarotSpread"> | number
    cost?: IntFilter<"TarotSpread"> | number
    iconLayout?: StringFilter<"TarotSpread"> | string
    spreadType?: StringFilter<"TarotSpread"> | string
    layoutDescription?: StringFilter<"TarotSpread"> | string
    className?: StringFilter<"TarotSpread"> | string
    positions?: StringFilter<"TarotSpread"> | string
    discount?: IntFilter<"TarotSpread"> | number
    discountStartDate?: DateTimeNullableFilter<"TarotSpread"> | Date | string | null
    discountEndDate?: DateTimeNullableFilter<"TarotSpread"> | Date | string | null
    isActive?: BoolFilter<"TarotSpread"> | boolean
    order?: IntFilter<"TarotSpread"> | number
    promptTemplate?: StringFilter<"TarotSpread"> | string
    systemInstruction?: StringFilter<"TarotSpread"> | string
    cardPositionLabels?: StringFilter<"TarotSpread"> | string
    customVariables?: StringFilter<"TarotSpread"> | string
    cardIntroTemplates?: StringFilter<"TarotSpread"> | string
    createdAt?: DateTimeFilter<"TarotSpread"> | Date | string
    updatedAt?: DateTimeFilter<"TarotSpread"> | Date | string
  }

  export type TarotSpreadOrderByWithRelationInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrder
    cardCount?: SortOrder
    cost?: SortOrder
    iconLayout?: SortOrder
    spreadType?: SortOrder
    layoutDescription?: SortOrder
    className?: SortOrder
    positions?: SortOrder
    discount?: SortOrder
    discountStartDate?: SortOrderInput | SortOrder
    discountEndDate?: SortOrderInput | SortOrder
    isActive?: SortOrder
    order?: SortOrder
    promptTemplate?: SortOrder
    systemInstruction?: SortOrder
    cardPositionLabels?: SortOrder
    customVariables?: SortOrder
    cardIntroTemplates?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type TarotSpreadWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    spreadType?: string
    AND?: TarotSpreadWhereInput | TarotSpreadWhereInput[]
    OR?: TarotSpreadWhereInput[]
    NOT?: TarotSpreadWhereInput | TarotSpreadWhereInput[]
    name?: StringFilter<"TarotSpread"> | string
    description?: StringFilter<"TarotSpread"> | string
    cardCount?: IntFilter<"TarotSpread"> | number
    cost?: IntFilter<"TarotSpread"> | number
    iconLayout?: StringFilter<"TarotSpread"> | string
    layoutDescription?: StringFilter<"TarotSpread"> | string
    className?: StringFilter<"TarotSpread"> | string
    positions?: StringFilter<"TarotSpread"> | string
    discount?: IntFilter<"TarotSpread"> | number
    discountStartDate?: DateTimeNullableFilter<"TarotSpread"> | Date | string | null
    discountEndDate?: DateTimeNullableFilter<"TarotSpread"> | Date | string | null
    isActive?: BoolFilter<"TarotSpread"> | boolean
    order?: IntFilter<"TarotSpread"> | number
    promptTemplate?: StringFilter<"TarotSpread"> | string
    systemInstruction?: StringFilter<"TarotSpread"> | string
    cardPositionLabels?: StringFilter<"TarotSpread"> | string
    customVariables?: StringFilter<"TarotSpread"> | string
    cardIntroTemplates?: StringFilter<"TarotSpread"> | string
    createdAt?: DateTimeFilter<"TarotSpread"> | Date | string
    updatedAt?: DateTimeFilter<"TarotSpread"> | Date | string
  }, "id" | "spreadType">

  export type TarotSpreadOrderByWithAggregationInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrder
    cardCount?: SortOrder
    cost?: SortOrder
    iconLayout?: SortOrder
    spreadType?: SortOrder
    layoutDescription?: SortOrder
    className?: SortOrder
    positions?: SortOrder
    discount?: SortOrder
    discountStartDate?: SortOrderInput | SortOrder
    discountEndDate?: SortOrderInput | SortOrder
    isActive?: SortOrder
    order?: SortOrder
    promptTemplate?: SortOrder
    systemInstruction?: SortOrder
    cardPositionLabels?: SortOrder
    customVariables?: SortOrder
    cardIntroTemplates?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: TarotSpreadCountOrderByAggregateInput
    _avg?: TarotSpreadAvgOrderByAggregateInput
    _max?: TarotSpreadMaxOrderByAggregateInput
    _min?: TarotSpreadMinOrderByAggregateInput
    _sum?: TarotSpreadSumOrderByAggregateInput
  }

  export type TarotSpreadScalarWhereWithAggregatesInput = {
    AND?: TarotSpreadScalarWhereWithAggregatesInput | TarotSpreadScalarWhereWithAggregatesInput[]
    OR?: TarotSpreadScalarWhereWithAggregatesInput[]
    NOT?: TarotSpreadScalarWhereWithAggregatesInput | TarotSpreadScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"TarotSpread"> | string
    name?: StringWithAggregatesFilter<"TarotSpread"> | string
    description?: StringWithAggregatesFilter<"TarotSpread"> | string
    cardCount?: IntWithAggregatesFilter<"TarotSpread"> | number
    cost?: IntWithAggregatesFilter<"TarotSpread"> | number
    iconLayout?: StringWithAggregatesFilter<"TarotSpread"> | string
    spreadType?: StringWithAggregatesFilter<"TarotSpread"> | string
    layoutDescription?: StringWithAggregatesFilter<"TarotSpread"> | string
    className?: StringWithAggregatesFilter<"TarotSpread"> | string
    positions?: StringWithAggregatesFilter<"TarotSpread"> | string
    discount?: IntWithAggregatesFilter<"TarotSpread"> | number
    discountStartDate?: DateTimeNullableWithAggregatesFilter<"TarotSpread"> | Date | string | null
    discountEndDate?: DateTimeNullableWithAggregatesFilter<"TarotSpread"> | Date | string | null
    isActive?: BoolWithAggregatesFilter<"TarotSpread"> | boolean
    order?: IntWithAggregatesFilter<"TarotSpread"> | number
    promptTemplate?: StringWithAggregatesFilter<"TarotSpread"> | string
    systemInstruction?: StringWithAggregatesFilter<"TarotSpread"> | string
    cardPositionLabels?: StringWithAggregatesFilter<"TarotSpread"> | string
    customVariables?: StringWithAggregatesFilter<"TarotSpread"> | string
    cardIntroTemplates?: StringWithAggregatesFilter<"TarotSpread"> | string
    createdAt?: DateTimeWithAggregatesFilter<"TarotSpread"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"TarotSpread"> | Date | string
  }

  export type TarotGlobalSettingsWhereInput = {
    AND?: TarotGlobalSettingsWhereInput | TarotGlobalSettingsWhereInput[]
    OR?: TarotGlobalSettingsWhereInput[]
    NOT?: TarotGlobalSettingsWhereInput | TarotGlobalSettingsWhereInput[]
    id?: StringFilter<"TarotGlobalSettings"> | string
    settingKey?: StringFilter<"TarotGlobalSettings"> | string
    settingValue?: StringFilter<"TarotGlobalSettings"> | string
    description?: StringFilter<"TarotGlobalSettings"> | string
    isActive?: BoolFilter<"TarotGlobalSettings"> | boolean
    createdAt?: DateTimeFilter<"TarotGlobalSettings"> | Date | string
    updatedAt?: DateTimeFilter<"TarotGlobalSettings"> | Date | string
  }

  export type TarotGlobalSettingsOrderByWithRelationInput = {
    id?: SortOrder
    settingKey?: SortOrder
    settingValue?: SortOrder
    description?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type TarotGlobalSettingsWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    settingKey?: string
    AND?: TarotGlobalSettingsWhereInput | TarotGlobalSettingsWhereInput[]
    OR?: TarotGlobalSettingsWhereInput[]
    NOT?: TarotGlobalSettingsWhereInput | TarotGlobalSettingsWhereInput[]
    settingValue?: StringFilter<"TarotGlobalSettings"> | string
    description?: StringFilter<"TarotGlobalSettings"> | string
    isActive?: BoolFilter<"TarotGlobalSettings"> | boolean
    createdAt?: DateTimeFilter<"TarotGlobalSettings"> | Date | string
    updatedAt?: DateTimeFilter<"TarotGlobalSettings"> | Date | string
  }, "id" | "settingKey">

  export type TarotGlobalSettingsOrderByWithAggregationInput = {
    id?: SortOrder
    settingKey?: SortOrder
    settingValue?: SortOrder
    description?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: TarotGlobalSettingsCountOrderByAggregateInput
    _max?: TarotGlobalSettingsMaxOrderByAggregateInput
    _min?: TarotGlobalSettingsMinOrderByAggregateInput
  }

  export type TarotGlobalSettingsScalarWhereWithAggregatesInput = {
    AND?: TarotGlobalSettingsScalarWhereWithAggregatesInput | TarotGlobalSettingsScalarWhereWithAggregatesInput[]
    OR?: TarotGlobalSettingsScalarWhereWithAggregatesInput[]
    NOT?: TarotGlobalSettingsScalarWhereWithAggregatesInput | TarotGlobalSettingsScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"TarotGlobalSettings"> | string
    settingKey?: StringWithAggregatesFilter<"TarotGlobalSettings"> | string
    settingValue?: StringWithAggregatesFilter<"TarotGlobalSettings"> | string
    description?: StringWithAggregatesFilter<"TarotGlobalSettings"> | string
    isActive?: BoolWithAggregatesFilter<"TarotGlobalSettings"> | boolean
    createdAt?: DateTimeWithAggregatesFilter<"TarotGlobalSettings"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"TarotGlobalSettings"> | Date | string
  }

  export type TarotReadingWhereInput = {
    AND?: TarotReadingWhereInput | TarotReadingWhereInput[]
    OR?: TarotReadingWhereInput[]
    NOT?: TarotReadingWhereInput | TarotReadingWhereInput[]
    id?: StringFilter<"TarotReading"> | string
    userId?: StringNullableFilter<"TarotReading"> | string | null
    readingType?: StringFilter<"TarotReading"> | string
    spreadType?: StringNullableFilter<"TarotReading"> | string | null
    cardCount?: IntFilter<"TarotReading"> | number
    promptTokens?: IntFilter<"TarotReading"> | number
    completionTokens?: IntFilter<"TarotReading"> | number
    totalTokens?: IntFilter<"TarotReading"> | number
    ipAddress?: StringNullableFilter<"TarotReading"> | string | null
    userAgent?: StringNullableFilter<"TarotReading"> | string | null
    createdAt?: DateTimeFilter<"TarotReading"> | Date | string
    user?: XOR<UserNullableScalarRelationFilter, UserWhereInput> | null
  }

  export type TarotReadingOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrderInput | SortOrder
    readingType?: SortOrder
    spreadType?: SortOrderInput | SortOrder
    cardCount?: SortOrder
    promptTokens?: SortOrder
    completionTokens?: SortOrder
    totalTokens?: SortOrder
    ipAddress?: SortOrderInput | SortOrder
    userAgent?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    user?: UserOrderByWithRelationInput
  }

  export type TarotReadingWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: TarotReadingWhereInput | TarotReadingWhereInput[]
    OR?: TarotReadingWhereInput[]
    NOT?: TarotReadingWhereInput | TarotReadingWhereInput[]
    userId?: StringNullableFilter<"TarotReading"> | string | null
    readingType?: StringFilter<"TarotReading"> | string
    spreadType?: StringNullableFilter<"TarotReading"> | string | null
    cardCount?: IntFilter<"TarotReading"> | number
    promptTokens?: IntFilter<"TarotReading"> | number
    completionTokens?: IntFilter<"TarotReading"> | number
    totalTokens?: IntFilter<"TarotReading"> | number
    ipAddress?: StringNullableFilter<"TarotReading"> | string | null
    userAgent?: StringNullableFilter<"TarotReading"> | string | null
    createdAt?: DateTimeFilter<"TarotReading"> | Date | string
    user?: XOR<UserNullableScalarRelationFilter, UserWhereInput> | null
  }, "id">

  export type TarotReadingOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrderInput | SortOrder
    readingType?: SortOrder
    spreadType?: SortOrderInput | SortOrder
    cardCount?: SortOrder
    promptTokens?: SortOrder
    completionTokens?: SortOrder
    totalTokens?: SortOrder
    ipAddress?: SortOrderInput | SortOrder
    userAgent?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    _count?: TarotReadingCountOrderByAggregateInput
    _avg?: TarotReadingAvgOrderByAggregateInput
    _max?: TarotReadingMaxOrderByAggregateInput
    _min?: TarotReadingMinOrderByAggregateInput
    _sum?: TarotReadingSumOrderByAggregateInput
  }

  export type TarotReadingScalarWhereWithAggregatesInput = {
    AND?: TarotReadingScalarWhereWithAggregatesInput | TarotReadingScalarWhereWithAggregatesInput[]
    OR?: TarotReadingScalarWhereWithAggregatesInput[]
    NOT?: TarotReadingScalarWhereWithAggregatesInput | TarotReadingScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"TarotReading"> | string
    userId?: StringNullableWithAggregatesFilter<"TarotReading"> | string | null
    readingType?: StringWithAggregatesFilter<"TarotReading"> | string
    spreadType?: StringNullableWithAggregatesFilter<"TarotReading"> | string | null
    cardCount?: IntWithAggregatesFilter<"TarotReading"> | number
    promptTokens?: IntWithAggregatesFilter<"TarotReading"> | number
    completionTokens?: IntWithAggregatesFilter<"TarotReading"> | number
    totalTokens?: IntWithAggregatesFilter<"TarotReading"> | number
    ipAddress?: StringNullableWithAggregatesFilter<"TarotReading"> | string | null
    userAgent?: StringNullableWithAggregatesFilter<"TarotReading"> | string | null
    createdAt?: DateTimeWithAggregatesFilter<"TarotReading"> | Date | string
  }

  export type UserQuestionHistoryWhereInput = {
    AND?: UserQuestionHistoryWhereInput | UserQuestionHistoryWhereInput[]
    OR?: UserQuestionHistoryWhereInput[]
    NOT?: UserQuestionHistoryWhereInput | UserQuestionHistoryWhereInput[]
    id?: StringFilter<"UserQuestionHistory"> | string
    userId?: StringNullableFilter<"UserQuestionHistory"> | string | null
    question?: StringFilter<"UserQuestionHistory"> | string
    questionType?: StringFilter<"UserQuestionHistory"> | string
    spreadType?: StringNullableFilter<"UserQuestionHistory"> | string | null
    consultantName?: StringNullableFilter<"UserQuestionHistory"> | string | null
    nameType?: StringNullableFilter<"UserQuestionHistory"> | string | null
    geminiResponse?: StringFilter<"UserQuestionHistory"> | string
    responseSummary?: StringFilter<"UserQuestionHistory"> | string
    responseDate?: StringFilter<"UserQuestionHistory"> | string
    ipAddress?: StringNullableFilter<"UserQuestionHistory"> | string | null
    userAgent?: StringNullableFilter<"UserQuestionHistory"> | string | null
    createdAt?: DateTimeFilter<"UserQuestionHistory"> | Date | string
    updatedAt?: DateTimeFilter<"UserQuestionHistory"> | Date | string
    user?: XOR<UserNullableScalarRelationFilter, UserWhereInput> | null
  }

  export type UserQuestionHistoryOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrderInput | SortOrder
    question?: SortOrder
    questionType?: SortOrder
    spreadType?: SortOrderInput | SortOrder
    consultantName?: SortOrderInput | SortOrder
    nameType?: SortOrderInput | SortOrder
    geminiResponse?: SortOrder
    responseSummary?: SortOrder
    responseDate?: SortOrder
    ipAddress?: SortOrderInput | SortOrder
    userAgent?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    user?: UserOrderByWithRelationInput
  }

  export type UserQuestionHistoryWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: UserQuestionHistoryWhereInput | UserQuestionHistoryWhereInput[]
    OR?: UserQuestionHistoryWhereInput[]
    NOT?: UserQuestionHistoryWhereInput | UserQuestionHistoryWhereInput[]
    userId?: StringNullableFilter<"UserQuestionHistory"> | string | null
    question?: StringFilter<"UserQuestionHistory"> | string
    questionType?: StringFilter<"UserQuestionHistory"> | string
    spreadType?: StringNullableFilter<"UserQuestionHistory"> | string | null
    consultantName?: StringNullableFilter<"UserQuestionHistory"> | string | null
    nameType?: StringNullableFilter<"UserQuestionHistory"> | string | null
    geminiResponse?: StringFilter<"UserQuestionHistory"> | string
    responseSummary?: StringFilter<"UserQuestionHistory"> | string
    responseDate?: StringFilter<"UserQuestionHistory"> | string
    ipAddress?: StringNullableFilter<"UserQuestionHistory"> | string | null
    userAgent?: StringNullableFilter<"UserQuestionHistory"> | string | null
    createdAt?: DateTimeFilter<"UserQuestionHistory"> | Date | string
    updatedAt?: DateTimeFilter<"UserQuestionHistory"> | Date | string
    user?: XOR<UserNullableScalarRelationFilter, UserWhereInput> | null
  }, "id">

  export type UserQuestionHistoryOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrderInput | SortOrder
    question?: SortOrder
    questionType?: SortOrder
    spreadType?: SortOrderInput | SortOrder
    consultantName?: SortOrderInput | SortOrder
    nameType?: SortOrderInput | SortOrder
    geminiResponse?: SortOrder
    responseSummary?: SortOrder
    responseDate?: SortOrder
    ipAddress?: SortOrderInput | SortOrder
    userAgent?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: UserQuestionHistoryCountOrderByAggregateInput
    _max?: UserQuestionHistoryMaxOrderByAggregateInput
    _min?: UserQuestionHistoryMinOrderByAggregateInput
  }

  export type UserQuestionHistoryScalarWhereWithAggregatesInput = {
    AND?: UserQuestionHistoryScalarWhereWithAggregatesInput | UserQuestionHistoryScalarWhereWithAggregatesInput[]
    OR?: UserQuestionHistoryScalarWhereWithAggregatesInput[]
    NOT?: UserQuestionHistoryScalarWhereWithAggregatesInput | UserQuestionHistoryScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"UserQuestionHistory"> | string
    userId?: StringNullableWithAggregatesFilter<"UserQuestionHistory"> | string | null
    question?: StringWithAggregatesFilter<"UserQuestionHistory"> | string
    questionType?: StringWithAggregatesFilter<"UserQuestionHistory"> | string
    spreadType?: StringNullableWithAggregatesFilter<"UserQuestionHistory"> | string | null
    consultantName?: StringNullableWithAggregatesFilter<"UserQuestionHistory"> | string | null
    nameType?: StringNullableWithAggregatesFilter<"UserQuestionHistory"> | string | null
    geminiResponse?: StringWithAggregatesFilter<"UserQuestionHistory"> | string
    responseSummary?: StringWithAggregatesFilter<"UserQuestionHistory"> | string
    responseDate?: StringWithAggregatesFilter<"UserQuestionHistory"> | string
    ipAddress?: StringNullableWithAggregatesFilter<"UserQuestionHistory"> | string | null
    userAgent?: StringNullableWithAggregatesFilter<"UserQuestionHistory"> | string | null
    createdAt?: DateTimeWithAggregatesFilter<"UserQuestionHistory"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"UserQuestionHistory"> | Date | string
  }

  export type UserCreateInput = {
    id?: string
    email: string
    hashedPassword?: string | null
    name?: string | null
    credits?: number
    lastCreditDeduction?: Date | string | null
    googleId?: string | null
    kakaoId?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    cooldowns?: UserFortuneCooldownCreateNestedManyWithoutUserInput
    tarotReadings?: TarotReadingCreateNestedManyWithoutUserInput
    questionHistory?: UserQuestionHistoryCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateInput = {
    id?: string
    email: string
    hashedPassword?: string | null
    name?: string | null
    credits?: number
    lastCreditDeduction?: Date | string | null
    googleId?: string | null
    kakaoId?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    cooldowns?: UserFortuneCooldownUncheckedCreateNestedManyWithoutUserInput
    tarotReadings?: TarotReadingUncheckedCreateNestedManyWithoutUserInput
    questionHistory?: UserQuestionHistoryUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    hashedPassword?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    credits?: IntFieldUpdateOperationsInput | number
    lastCreditDeduction?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    googleId?: NullableStringFieldUpdateOperationsInput | string | null
    kakaoId?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    cooldowns?: UserFortuneCooldownUpdateManyWithoutUserNestedInput
    tarotReadings?: TarotReadingUpdateManyWithoutUserNestedInput
    questionHistory?: UserQuestionHistoryUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    hashedPassword?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    credits?: IntFieldUpdateOperationsInput | number
    lastCreditDeduction?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    googleId?: NullableStringFieldUpdateOperationsInput | string | null
    kakaoId?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    cooldowns?: UserFortuneCooldownUncheckedUpdateManyWithoutUserNestedInput
    tarotReadings?: TarotReadingUncheckedUpdateManyWithoutUserNestedInput
    questionHistory?: UserQuestionHistoryUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserCreateManyInput = {
    id?: string
    email: string
    hashedPassword?: string | null
    name?: string | null
    credits?: number
    lastCreditDeduction?: Date | string | null
    googleId?: string | null
    kakaoId?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    hashedPassword?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    credits?: IntFieldUpdateOperationsInput | number
    lastCreditDeduction?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    googleId?: NullableStringFieldUpdateOperationsInput | string | null
    kakaoId?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    hashedPassword?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    credits?: IntFieldUpdateOperationsInput | number
    lastCreditDeduction?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    googleId?: NullableStringFieldUpdateOperationsInput | string | null
    kakaoId?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserFortuneCooldownCreateInput = {
    id?: string
    fortuneType: string
    cooldownExpiresAt: Date | string
    createdAt?: Date | string
    user: UserCreateNestedOneWithoutCooldownsInput
  }

  export type UserFortuneCooldownUncheckedCreateInput = {
    id?: string
    userId: string
    fortuneType: string
    cooldownExpiresAt: Date | string
    createdAt?: Date | string
  }

  export type UserFortuneCooldownUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    fortuneType?: StringFieldUpdateOperationsInput | string
    cooldownExpiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutCooldownsNestedInput
  }

  export type UserFortuneCooldownUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    fortuneType?: StringFieldUpdateOperationsInput | string
    cooldownExpiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserFortuneCooldownCreateManyInput = {
    id?: string
    userId: string
    fortuneType: string
    cooldownExpiresAt: Date | string
    createdAt?: Date | string
  }

  export type UserFortuneCooldownUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    fortuneType?: StringFieldUpdateOperationsInput | string
    cooldownExpiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserFortuneCooldownUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    fortuneType?: StringFieldUpdateOperationsInput | string
    cooldownExpiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type IPFortuneCooldownCreateInput = {
    id?: string
    ipAddress: string
    fortuneType: string
    cooldownExpiresAt: Date | string
    createdAt?: Date | string
  }

  export type IPFortuneCooldownUncheckedCreateInput = {
    id?: string
    ipAddress: string
    fortuneType: string
    cooldownExpiresAt: Date | string
    createdAt?: Date | string
  }

  export type IPFortuneCooldownUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    ipAddress?: StringFieldUpdateOperationsInput | string
    fortuneType?: StringFieldUpdateOperationsInput | string
    cooldownExpiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type IPFortuneCooldownUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    ipAddress?: StringFieldUpdateOperationsInput | string
    fortuneType?: StringFieldUpdateOperationsInput | string
    cooldownExpiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type IPFortuneCooldownCreateManyInput = {
    id?: string
    ipAddress: string
    fortuneType: string
    cooldownExpiresAt: Date | string
    createdAt?: Date | string
  }

  export type IPFortuneCooldownUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    ipAddress?: StringFieldUpdateOperationsInput | string
    fortuneType?: StringFieldUpdateOperationsInput | string
    cooldownExpiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type IPFortuneCooldownUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    ipAddress?: StringFieldUpdateOperationsInput | string
    fortuneType?: StringFieldUpdateOperationsInput | string
    cooldownExpiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TarotSpreadCreateInput = {
    id?: string
    name: string
    description: string
    cardCount: number
    cost: number
    iconLayout?: string
    spreadType: string
    layoutDescription?: string
    className?: string
    positions?: string
    discount?: number
    discountStartDate?: Date | string | null
    discountEndDate?: Date | string | null
    isActive?: boolean
    order?: number
    promptTemplate?: string
    systemInstruction?: string
    cardPositionLabels?: string
    customVariables?: string
    cardIntroTemplates?: string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type TarotSpreadUncheckedCreateInput = {
    id?: string
    name: string
    description: string
    cardCount: number
    cost: number
    iconLayout?: string
    spreadType: string
    layoutDescription?: string
    className?: string
    positions?: string
    discount?: number
    discountStartDate?: Date | string | null
    discountEndDate?: Date | string | null
    isActive?: boolean
    order?: number
    promptTemplate?: string
    systemInstruction?: string
    cardPositionLabels?: string
    customVariables?: string
    cardIntroTemplates?: string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type TarotSpreadUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    cardCount?: IntFieldUpdateOperationsInput | number
    cost?: IntFieldUpdateOperationsInput | number
    iconLayout?: StringFieldUpdateOperationsInput | string
    spreadType?: StringFieldUpdateOperationsInput | string
    layoutDescription?: StringFieldUpdateOperationsInput | string
    className?: StringFieldUpdateOperationsInput | string
    positions?: StringFieldUpdateOperationsInput | string
    discount?: IntFieldUpdateOperationsInput | number
    discountStartDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    discountEndDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    order?: IntFieldUpdateOperationsInput | number
    promptTemplate?: StringFieldUpdateOperationsInput | string
    systemInstruction?: StringFieldUpdateOperationsInput | string
    cardPositionLabels?: StringFieldUpdateOperationsInput | string
    customVariables?: StringFieldUpdateOperationsInput | string
    cardIntroTemplates?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TarotSpreadUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    cardCount?: IntFieldUpdateOperationsInput | number
    cost?: IntFieldUpdateOperationsInput | number
    iconLayout?: StringFieldUpdateOperationsInput | string
    spreadType?: StringFieldUpdateOperationsInput | string
    layoutDescription?: StringFieldUpdateOperationsInput | string
    className?: StringFieldUpdateOperationsInput | string
    positions?: StringFieldUpdateOperationsInput | string
    discount?: IntFieldUpdateOperationsInput | number
    discountStartDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    discountEndDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    order?: IntFieldUpdateOperationsInput | number
    promptTemplate?: StringFieldUpdateOperationsInput | string
    systemInstruction?: StringFieldUpdateOperationsInput | string
    cardPositionLabels?: StringFieldUpdateOperationsInput | string
    customVariables?: StringFieldUpdateOperationsInput | string
    cardIntroTemplates?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TarotSpreadCreateManyInput = {
    id?: string
    name: string
    description: string
    cardCount: number
    cost: number
    iconLayout?: string
    spreadType: string
    layoutDescription?: string
    className?: string
    positions?: string
    discount?: number
    discountStartDate?: Date | string | null
    discountEndDate?: Date | string | null
    isActive?: boolean
    order?: number
    promptTemplate?: string
    systemInstruction?: string
    cardPositionLabels?: string
    customVariables?: string
    cardIntroTemplates?: string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type TarotSpreadUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    cardCount?: IntFieldUpdateOperationsInput | number
    cost?: IntFieldUpdateOperationsInput | number
    iconLayout?: StringFieldUpdateOperationsInput | string
    spreadType?: StringFieldUpdateOperationsInput | string
    layoutDescription?: StringFieldUpdateOperationsInput | string
    className?: StringFieldUpdateOperationsInput | string
    positions?: StringFieldUpdateOperationsInput | string
    discount?: IntFieldUpdateOperationsInput | number
    discountStartDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    discountEndDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    order?: IntFieldUpdateOperationsInput | number
    promptTemplate?: StringFieldUpdateOperationsInput | string
    systemInstruction?: StringFieldUpdateOperationsInput | string
    cardPositionLabels?: StringFieldUpdateOperationsInput | string
    customVariables?: StringFieldUpdateOperationsInput | string
    cardIntroTemplates?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TarotSpreadUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    cardCount?: IntFieldUpdateOperationsInput | number
    cost?: IntFieldUpdateOperationsInput | number
    iconLayout?: StringFieldUpdateOperationsInput | string
    spreadType?: StringFieldUpdateOperationsInput | string
    layoutDescription?: StringFieldUpdateOperationsInput | string
    className?: StringFieldUpdateOperationsInput | string
    positions?: StringFieldUpdateOperationsInput | string
    discount?: IntFieldUpdateOperationsInput | number
    discountStartDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    discountEndDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    order?: IntFieldUpdateOperationsInput | number
    promptTemplate?: StringFieldUpdateOperationsInput | string
    systemInstruction?: StringFieldUpdateOperationsInput | string
    cardPositionLabels?: StringFieldUpdateOperationsInput | string
    customVariables?: StringFieldUpdateOperationsInput | string
    cardIntroTemplates?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TarotGlobalSettingsCreateInput = {
    id?: string
    settingKey: string
    settingValue: string
    description?: string
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type TarotGlobalSettingsUncheckedCreateInput = {
    id?: string
    settingKey: string
    settingValue: string
    description?: string
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type TarotGlobalSettingsUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    settingKey?: StringFieldUpdateOperationsInput | string
    settingValue?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TarotGlobalSettingsUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    settingKey?: StringFieldUpdateOperationsInput | string
    settingValue?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TarotGlobalSettingsCreateManyInput = {
    id?: string
    settingKey: string
    settingValue: string
    description?: string
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type TarotGlobalSettingsUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    settingKey?: StringFieldUpdateOperationsInput | string
    settingValue?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TarotGlobalSettingsUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    settingKey?: StringFieldUpdateOperationsInput | string
    settingValue?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TarotReadingCreateInput = {
    id?: string
    readingType: string
    spreadType?: string | null
    cardCount?: number
    promptTokens?: number
    completionTokens?: number
    totalTokens?: number
    ipAddress?: string | null
    userAgent?: string | null
    createdAt?: Date | string
    user?: UserCreateNestedOneWithoutTarotReadingsInput
  }

  export type TarotReadingUncheckedCreateInput = {
    id?: string
    userId?: string | null
    readingType: string
    spreadType?: string | null
    cardCount?: number
    promptTokens?: number
    completionTokens?: number
    totalTokens?: number
    ipAddress?: string | null
    userAgent?: string | null
    createdAt?: Date | string
  }

  export type TarotReadingUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    readingType?: StringFieldUpdateOperationsInput | string
    spreadType?: NullableStringFieldUpdateOperationsInput | string | null
    cardCount?: IntFieldUpdateOperationsInput | number
    promptTokens?: IntFieldUpdateOperationsInput | number
    completionTokens?: IntFieldUpdateOperationsInput | number
    totalTokens?: IntFieldUpdateOperationsInput | number
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneWithoutTarotReadingsNestedInput
  }

  export type TarotReadingUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: NullableStringFieldUpdateOperationsInput | string | null
    readingType?: StringFieldUpdateOperationsInput | string
    spreadType?: NullableStringFieldUpdateOperationsInput | string | null
    cardCount?: IntFieldUpdateOperationsInput | number
    promptTokens?: IntFieldUpdateOperationsInput | number
    completionTokens?: IntFieldUpdateOperationsInput | number
    totalTokens?: IntFieldUpdateOperationsInput | number
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TarotReadingCreateManyInput = {
    id?: string
    userId?: string | null
    readingType: string
    spreadType?: string | null
    cardCount?: number
    promptTokens?: number
    completionTokens?: number
    totalTokens?: number
    ipAddress?: string | null
    userAgent?: string | null
    createdAt?: Date | string
  }

  export type TarotReadingUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    readingType?: StringFieldUpdateOperationsInput | string
    spreadType?: NullableStringFieldUpdateOperationsInput | string | null
    cardCount?: IntFieldUpdateOperationsInput | number
    promptTokens?: IntFieldUpdateOperationsInput | number
    completionTokens?: IntFieldUpdateOperationsInput | number
    totalTokens?: IntFieldUpdateOperationsInput | number
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TarotReadingUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: NullableStringFieldUpdateOperationsInput | string | null
    readingType?: StringFieldUpdateOperationsInput | string
    spreadType?: NullableStringFieldUpdateOperationsInput | string | null
    cardCount?: IntFieldUpdateOperationsInput | number
    promptTokens?: IntFieldUpdateOperationsInput | number
    completionTokens?: IntFieldUpdateOperationsInput | number
    totalTokens?: IntFieldUpdateOperationsInput | number
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserQuestionHistoryCreateInput = {
    id?: string
    question: string
    questionType: string
    spreadType?: string | null
    consultantName?: string | null
    nameType?: string | null
    geminiResponse: string
    responseSummary: string
    responseDate: string
    ipAddress?: string | null
    userAgent?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    user?: UserCreateNestedOneWithoutQuestionHistoryInput
  }

  export type UserQuestionHistoryUncheckedCreateInput = {
    id?: string
    userId?: string | null
    question: string
    questionType: string
    spreadType?: string | null
    consultantName?: string | null
    nameType?: string | null
    geminiResponse: string
    responseSummary: string
    responseDate: string
    ipAddress?: string | null
    userAgent?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserQuestionHistoryUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    question?: StringFieldUpdateOperationsInput | string
    questionType?: StringFieldUpdateOperationsInput | string
    spreadType?: NullableStringFieldUpdateOperationsInput | string | null
    consultantName?: NullableStringFieldUpdateOperationsInput | string | null
    nameType?: NullableStringFieldUpdateOperationsInput | string | null
    geminiResponse?: StringFieldUpdateOperationsInput | string
    responseSummary?: StringFieldUpdateOperationsInput | string
    responseDate?: StringFieldUpdateOperationsInput | string
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneWithoutQuestionHistoryNestedInput
  }

  export type UserQuestionHistoryUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: NullableStringFieldUpdateOperationsInput | string | null
    question?: StringFieldUpdateOperationsInput | string
    questionType?: StringFieldUpdateOperationsInput | string
    spreadType?: NullableStringFieldUpdateOperationsInput | string | null
    consultantName?: NullableStringFieldUpdateOperationsInput | string | null
    nameType?: NullableStringFieldUpdateOperationsInput | string | null
    geminiResponse?: StringFieldUpdateOperationsInput | string
    responseSummary?: StringFieldUpdateOperationsInput | string
    responseDate?: StringFieldUpdateOperationsInput | string
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserQuestionHistoryCreateManyInput = {
    id?: string
    userId?: string | null
    question: string
    questionType: string
    spreadType?: string | null
    consultantName?: string | null
    nameType?: string | null
    geminiResponse: string
    responseSummary: string
    responseDate: string
    ipAddress?: string | null
    userAgent?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserQuestionHistoryUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    question?: StringFieldUpdateOperationsInput | string
    questionType?: StringFieldUpdateOperationsInput | string
    spreadType?: NullableStringFieldUpdateOperationsInput | string | null
    consultantName?: NullableStringFieldUpdateOperationsInput | string | null
    nameType?: NullableStringFieldUpdateOperationsInput | string | null
    geminiResponse?: StringFieldUpdateOperationsInput | string
    responseSummary?: StringFieldUpdateOperationsInput | string
    responseDate?: StringFieldUpdateOperationsInput | string
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserQuestionHistoryUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: NullableStringFieldUpdateOperationsInput | string | null
    question?: StringFieldUpdateOperationsInput | string
    questionType?: StringFieldUpdateOperationsInput | string
    spreadType?: NullableStringFieldUpdateOperationsInput | string | null
    consultantName?: NullableStringFieldUpdateOperationsInput | string | null
    nameType?: NullableStringFieldUpdateOperationsInput | string | null
    geminiResponse?: StringFieldUpdateOperationsInput | string
    responseSummary?: StringFieldUpdateOperationsInput | string
    responseDate?: StringFieldUpdateOperationsInput | string
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type UserFortuneCooldownListRelationFilter = {
    every?: UserFortuneCooldownWhereInput
    some?: UserFortuneCooldownWhereInput
    none?: UserFortuneCooldownWhereInput
  }

  export type TarotReadingListRelationFilter = {
    every?: TarotReadingWhereInput
    some?: TarotReadingWhereInput
    none?: TarotReadingWhereInput
  }

  export type UserQuestionHistoryListRelationFilter = {
    every?: UserQuestionHistoryWhereInput
    some?: UserQuestionHistoryWhereInput
    none?: UserQuestionHistoryWhereInput
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type UserFortuneCooldownOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type TarotReadingOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type UserQuestionHistoryOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type UserCountOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    hashedPassword?: SortOrder
    name?: SortOrder
    credits?: SortOrder
    lastCreditDeduction?: SortOrder
    googleId?: SortOrder
    kakaoId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserAvgOrderByAggregateInput = {
    credits?: SortOrder
  }

  export type UserMaxOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    hashedPassword?: SortOrder
    name?: SortOrder
    credits?: SortOrder
    lastCreditDeduction?: SortOrder
    googleId?: SortOrder
    kakaoId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMinOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    hashedPassword?: SortOrder
    name?: SortOrder
    credits?: SortOrder
    lastCreditDeduction?: SortOrder
    googleId?: SortOrder
    kakaoId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserSumOrderByAggregateInput = {
    credits?: SortOrder
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type UserScalarRelationFilter = {
    is?: UserWhereInput
    isNot?: UserWhereInput
  }

  export type UserFortuneCooldownUserIdFortuneTypeCompoundUniqueInput = {
    userId: string
    fortuneType: string
  }

  export type UserFortuneCooldownCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    fortuneType?: SortOrder
    cooldownExpiresAt?: SortOrder
    createdAt?: SortOrder
  }

  export type UserFortuneCooldownMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    fortuneType?: SortOrder
    cooldownExpiresAt?: SortOrder
    createdAt?: SortOrder
  }

  export type UserFortuneCooldownMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    fortuneType?: SortOrder
    cooldownExpiresAt?: SortOrder
    createdAt?: SortOrder
  }

  export type IPFortuneCooldownIpAddressFortuneTypeCompoundUniqueInput = {
    ipAddress: string
    fortuneType: string
  }

  export type IPFortuneCooldownCountOrderByAggregateInput = {
    id?: SortOrder
    ipAddress?: SortOrder
    fortuneType?: SortOrder
    cooldownExpiresAt?: SortOrder
    createdAt?: SortOrder
  }

  export type IPFortuneCooldownMaxOrderByAggregateInput = {
    id?: SortOrder
    ipAddress?: SortOrder
    fortuneType?: SortOrder
    cooldownExpiresAt?: SortOrder
    createdAt?: SortOrder
  }

  export type IPFortuneCooldownMinOrderByAggregateInput = {
    id?: SortOrder
    ipAddress?: SortOrder
    fortuneType?: SortOrder
    cooldownExpiresAt?: SortOrder
    createdAt?: SortOrder
  }

  export type BoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type TarotSpreadCountOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrder
    cardCount?: SortOrder
    cost?: SortOrder
    iconLayout?: SortOrder
    spreadType?: SortOrder
    layoutDescription?: SortOrder
    className?: SortOrder
    positions?: SortOrder
    discount?: SortOrder
    discountStartDate?: SortOrder
    discountEndDate?: SortOrder
    isActive?: SortOrder
    order?: SortOrder
    promptTemplate?: SortOrder
    systemInstruction?: SortOrder
    cardPositionLabels?: SortOrder
    customVariables?: SortOrder
    cardIntroTemplates?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type TarotSpreadAvgOrderByAggregateInput = {
    cardCount?: SortOrder
    cost?: SortOrder
    discount?: SortOrder
    order?: SortOrder
  }

  export type TarotSpreadMaxOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrder
    cardCount?: SortOrder
    cost?: SortOrder
    iconLayout?: SortOrder
    spreadType?: SortOrder
    layoutDescription?: SortOrder
    className?: SortOrder
    positions?: SortOrder
    discount?: SortOrder
    discountStartDate?: SortOrder
    discountEndDate?: SortOrder
    isActive?: SortOrder
    order?: SortOrder
    promptTemplate?: SortOrder
    systemInstruction?: SortOrder
    cardPositionLabels?: SortOrder
    customVariables?: SortOrder
    cardIntroTemplates?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type TarotSpreadMinOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrder
    cardCount?: SortOrder
    cost?: SortOrder
    iconLayout?: SortOrder
    spreadType?: SortOrder
    layoutDescription?: SortOrder
    className?: SortOrder
    positions?: SortOrder
    discount?: SortOrder
    discountStartDate?: SortOrder
    discountEndDate?: SortOrder
    isActive?: SortOrder
    order?: SortOrder
    promptTemplate?: SortOrder
    systemInstruction?: SortOrder
    cardPositionLabels?: SortOrder
    customVariables?: SortOrder
    cardIntroTemplates?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type TarotSpreadSumOrderByAggregateInput = {
    cardCount?: SortOrder
    cost?: SortOrder
    discount?: SortOrder
    order?: SortOrder
  }

  export type BoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type TarotGlobalSettingsCountOrderByAggregateInput = {
    id?: SortOrder
    settingKey?: SortOrder
    settingValue?: SortOrder
    description?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type TarotGlobalSettingsMaxOrderByAggregateInput = {
    id?: SortOrder
    settingKey?: SortOrder
    settingValue?: SortOrder
    description?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type TarotGlobalSettingsMinOrderByAggregateInput = {
    id?: SortOrder
    settingKey?: SortOrder
    settingValue?: SortOrder
    description?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserNullableScalarRelationFilter = {
    is?: UserWhereInput | null
    isNot?: UserWhereInput | null
  }

  export type TarotReadingCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    readingType?: SortOrder
    spreadType?: SortOrder
    cardCount?: SortOrder
    promptTokens?: SortOrder
    completionTokens?: SortOrder
    totalTokens?: SortOrder
    ipAddress?: SortOrder
    userAgent?: SortOrder
    createdAt?: SortOrder
  }

  export type TarotReadingAvgOrderByAggregateInput = {
    cardCount?: SortOrder
    promptTokens?: SortOrder
    completionTokens?: SortOrder
    totalTokens?: SortOrder
  }

  export type TarotReadingMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    readingType?: SortOrder
    spreadType?: SortOrder
    cardCount?: SortOrder
    promptTokens?: SortOrder
    completionTokens?: SortOrder
    totalTokens?: SortOrder
    ipAddress?: SortOrder
    userAgent?: SortOrder
    createdAt?: SortOrder
  }

  export type TarotReadingMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    readingType?: SortOrder
    spreadType?: SortOrder
    cardCount?: SortOrder
    promptTokens?: SortOrder
    completionTokens?: SortOrder
    totalTokens?: SortOrder
    ipAddress?: SortOrder
    userAgent?: SortOrder
    createdAt?: SortOrder
  }

  export type TarotReadingSumOrderByAggregateInput = {
    cardCount?: SortOrder
    promptTokens?: SortOrder
    completionTokens?: SortOrder
    totalTokens?: SortOrder
  }

  export type UserQuestionHistoryCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    question?: SortOrder
    questionType?: SortOrder
    spreadType?: SortOrder
    consultantName?: SortOrder
    nameType?: SortOrder
    geminiResponse?: SortOrder
    responseSummary?: SortOrder
    responseDate?: SortOrder
    ipAddress?: SortOrder
    userAgent?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserQuestionHistoryMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    question?: SortOrder
    questionType?: SortOrder
    spreadType?: SortOrder
    consultantName?: SortOrder
    nameType?: SortOrder
    geminiResponse?: SortOrder
    responseSummary?: SortOrder
    responseDate?: SortOrder
    ipAddress?: SortOrder
    userAgent?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserQuestionHistoryMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    question?: SortOrder
    questionType?: SortOrder
    spreadType?: SortOrder
    consultantName?: SortOrder
    nameType?: SortOrder
    geminiResponse?: SortOrder
    responseSummary?: SortOrder
    responseDate?: SortOrder
    ipAddress?: SortOrder
    userAgent?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserFortuneCooldownCreateNestedManyWithoutUserInput = {
    create?: XOR<UserFortuneCooldownCreateWithoutUserInput, UserFortuneCooldownUncheckedCreateWithoutUserInput> | UserFortuneCooldownCreateWithoutUserInput[] | UserFortuneCooldownUncheckedCreateWithoutUserInput[]
    connectOrCreate?: UserFortuneCooldownCreateOrConnectWithoutUserInput | UserFortuneCooldownCreateOrConnectWithoutUserInput[]
    createMany?: UserFortuneCooldownCreateManyUserInputEnvelope
    connect?: UserFortuneCooldownWhereUniqueInput | UserFortuneCooldownWhereUniqueInput[]
  }

  export type TarotReadingCreateNestedManyWithoutUserInput = {
    create?: XOR<TarotReadingCreateWithoutUserInput, TarotReadingUncheckedCreateWithoutUserInput> | TarotReadingCreateWithoutUserInput[] | TarotReadingUncheckedCreateWithoutUserInput[]
    connectOrCreate?: TarotReadingCreateOrConnectWithoutUserInput | TarotReadingCreateOrConnectWithoutUserInput[]
    createMany?: TarotReadingCreateManyUserInputEnvelope
    connect?: TarotReadingWhereUniqueInput | TarotReadingWhereUniqueInput[]
  }

  export type UserQuestionHistoryCreateNestedManyWithoutUserInput = {
    create?: XOR<UserQuestionHistoryCreateWithoutUserInput, UserQuestionHistoryUncheckedCreateWithoutUserInput> | UserQuestionHistoryCreateWithoutUserInput[] | UserQuestionHistoryUncheckedCreateWithoutUserInput[]
    connectOrCreate?: UserQuestionHistoryCreateOrConnectWithoutUserInput | UserQuestionHistoryCreateOrConnectWithoutUserInput[]
    createMany?: UserQuestionHistoryCreateManyUserInputEnvelope
    connect?: UserQuestionHistoryWhereUniqueInput | UserQuestionHistoryWhereUniqueInput[]
  }

  export type UserFortuneCooldownUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<UserFortuneCooldownCreateWithoutUserInput, UserFortuneCooldownUncheckedCreateWithoutUserInput> | UserFortuneCooldownCreateWithoutUserInput[] | UserFortuneCooldownUncheckedCreateWithoutUserInput[]
    connectOrCreate?: UserFortuneCooldownCreateOrConnectWithoutUserInput | UserFortuneCooldownCreateOrConnectWithoutUserInput[]
    createMany?: UserFortuneCooldownCreateManyUserInputEnvelope
    connect?: UserFortuneCooldownWhereUniqueInput | UserFortuneCooldownWhereUniqueInput[]
  }

  export type TarotReadingUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<TarotReadingCreateWithoutUserInput, TarotReadingUncheckedCreateWithoutUserInput> | TarotReadingCreateWithoutUserInput[] | TarotReadingUncheckedCreateWithoutUserInput[]
    connectOrCreate?: TarotReadingCreateOrConnectWithoutUserInput | TarotReadingCreateOrConnectWithoutUserInput[]
    createMany?: TarotReadingCreateManyUserInputEnvelope
    connect?: TarotReadingWhereUniqueInput | TarotReadingWhereUniqueInput[]
  }

  export type UserQuestionHistoryUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<UserQuestionHistoryCreateWithoutUserInput, UserQuestionHistoryUncheckedCreateWithoutUserInput> | UserQuestionHistoryCreateWithoutUserInput[] | UserQuestionHistoryUncheckedCreateWithoutUserInput[]
    connectOrCreate?: UserQuestionHistoryCreateOrConnectWithoutUserInput | UserQuestionHistoryCreateOrConnectWithoutUserInput[]
    createMany?: UserQuestionHistoryCreateManyUserInputEnvelope
    connect?: UserQuestionHistoryWhereUniqueInput | UserQuestionHistoryWhereUniqueInput[]
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type UserFortuneCooldownUpdateManyWithoutUserNestedInput = {
    create?: XOR<UserFortuneCooldownCreateWithoutUserInput, UserFortuneCooldownUncheckedCreateWithoutUserInput> | UserFortuneCooldownCreateWithoutUserInput[] | UserFortuneCooldownUncheckedCreateWithoutUserInput[]
    connectOrCreate?: UserFortuneCooldownCreateOrConnectWithoutUserInput | UserFortuneCooldownCreateOrConnectWithoutUserInput[]
    upsert?: UserFortuneCooldownUpsertWithWhereUniqueWithoutUserInput | UserFortuneCooldownUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: UserFortuneCooldownCreateManyUserInputEnvelope
    set?: UserFortuneCooldownWhereUniqueInput | UserFortuneCooldownWhereUniqueInput[]
    disconnect?: UserFortuneCooldownWhereUniqueInput | UserFortuneCooldownWhereUniqueInput[]
    delete?: UserFortuneCooldownWhereUniqueInput | UserFortuneCooldownWhereUniqueInput[]
    connect?: UserFortuneCooldownWhereUniqueInput | UserFortuneCooldownWhereUniqueInput[]
    update?: UserFortuneCooldownUpdateWithWhereUniqueWithoutUserInput | UserFortuneCooldownUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: UserFortuneCooldownUpdateManyWithWhereWithoutUserInput | UserFortuneCooldownUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: UserFortuneCooldownScalarWhereInput | UserFortuneCooldownScalarWhereInput[]
  }

  export type TarotReadingUpdateManyWithoutUserNestedInput = {
    create?: XOR<TarotReadingCreateWithoutUserInput, TarotReadingUncheckedCreateWithoutUserInput> | TarotReadingCreateWithoutUserInput[] | TarotReadingUncheckedCreateWithoutUserInput[]
    connectOrCreate?: TarotReadingCreateOrConnectWithoutUserInput | TarotReadingCreateOrConnectWithoutUserInput[]
    upsert?: TarotReadingUpsertWithWhereUniqueWithoutUserInput | TarotReadingUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: TarotReadingCreateManyUserInputEnvelope
    set?: TarotReadingWhereUniqueInput | TarotReadingWhereUniqueInput[]
    disconnect?: TarotReadingWhereUniqueInput | TarotReadingWhereUniqueInput[]
    delete?: TarotReadingWhereUniqueInput | TarotReadingWhereUniqueInput[]
    connect?: TarotReadingWhereUniqueInput | TarotReadingWhereUniqueInput[]
    update?: TarotReadingUpdateWithWhereUniqueWithoutUserInput | TarotReadingUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: TarotReadingUpdateManyWithWhereWithoutUserInput | TarotReadingUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: TarotReadingScalarWhereInput | TarotReadingScalarWhereInput[]
  }

  export type UserQuestionHistoryUpdateManyWithoutUserNestedInput = {
    create?: XOR<UserQuestionHistoryCreateWithoutUserInput, UserQuestionHistoryUncheckedCreateWithoutUserInput> | UserQuestionHistoryCreateWithoutUserInput[] | UserQuestionHistoryUncheckedCreateWithoutUserInput[]
    connectOrCreate?: UserQuestionHistoryCreateOrConnectWithoutUserInput | UserQuestionHistoryCreateOrConnectWithoutUserInput[]
    upsert?: UserQuestionHistoryUpsertWithWhereUniqueWithoutUserInput | UserQuestionHistoryUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: UserQuestionHistoryCreateManyUserInputEnvelope
    set?: UserQuestionHistoryWhereUniqueInput | UserQuestionHistoryWhereUniqueInput[]
    disconnect?: UserQuestionHistoryWhereUniqueInput | UserQuestionHistoryWhereUniqueInput[]
    delete?: UserQuestionHistoryWhereUniqueInput | UserQuestionHistoryWhereUniqueInput[]
    connect?: UserQuestionHistoryWhereUniqueInput | UserQuestionHistoryWhereUniqueInput[]
    update?: UserQuestionHistoryUpdateWithWhereUniqueWithoutUserInput | UserQuestionHistoryUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: UserQuestionHistoryUpdateManyWithWhereWithoutUserInput | UserQuestionHistoryUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: UserQuestionHistoryScalarWhereInput | UserQuestionHistoryScalarWhereInput[]
  }

  export type UserFortuneCooldownUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<UserFortuneCooldownCreateWithoutUserInput, UserFortuneCooldownUncheckedCreateWithoutUserInput> | UserFortuneCooldownCreateWithoutUserInput[] | UserFortuneCooldownUncheckedCreateWithoutUserInput[]
    connectOrCreate?: UserFortuneCooldownCreateOrConnectWithoutUserInput | UserFortuneCooldownCreateOrConnectWithoutUserInput[]
    upsert?: UserFortuneCooldownUpsertWithWhereUniqueWithoutUserInput | UserFortuneCooldownUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: UserFortuneCooldownCreateManyUserInputEnvelope
    set?: UserFortuneCooldownWhereUniqueInput | UserFortuneCooldownWhereUniqueInput[]
    disconnect?: UserFortuneCooldownWhereUniqueInput | UserFortuneCooldownWhereUniqueInput[]
    delete?: UserFortuneCooldownWhereUniqueInput | UserFortuneCooldownWhereUniqueInput[]
    connect?: UserFortuneCooldownWhereUniqueInput | UserFortuneCooldownWhereUniqueInput[]
    update?: UserFortuneCooldownUpdateWithWhereUniqueWithoutUserInput | UserFortuneCooldownUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: UserFortuneCooldownUpdateManyWithWhereWithoutUserInput | UserFortuneCooldownUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: UserFortuneCooldownScalarWhereInput | UserFortuneCooldownScalarWhereInput[]
  }

  export type TarotReadingUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<TarotReadingCreateWithoutUserInput, TarotReadingUncheckedCreateWithoutUserInput> | TarotReadingCreateWithoutUserInput[] | TarotReadingUncheckedCreateWithoutUserInput[]
    connectOrCreate?: TarotReadingCreateOrConnectWithoutUserInput | TarotReadingCreateOrConnectWithoutUserInput[]
    upsert?: TarotReadingUpsertWithWhereUniqueWithoutUserInput | TarotReadingUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: TarotReadingCreateManyUserInputEnvelope
    set?: TarotReadingWhereUniqueInput | TarotReadingWhereUniqueInput[]
    disconnect?: TarotReadingWhereUniqueInput | TarotReadingWhereUniqueInput[]
    delete?: TarotReadingWhereUniqueInput | TarotReadingWhereUniqueInput[]
    connect?: TarotReadingWhereUniqueInput | TarotReadingWhereUniqueInput[]
    update?: TarotReadingUpdateWithWhereUniqueWithoutUserInput | TarotReadingUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: TarotReadingUpdateManyWithWhereWithoutUserInput | TarotReadingUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: TarotReadingScalarWhereInput | TarotReadingScalarWhereInput[]
  }

  export type UserQuestionHistoryUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<UserQuestionHistoryCreateWithoutUserInput, UserQuestionHistoryUncheckedCreateWithoutUserInput> | UserQuestionHistoryCreateWithoutUserInput[] | UserQuestionHistoryUncheckedCreateWithoutUserInput[]
    connectOrCreate?: UserQuestionHistoryCreateOrConnectWithoutUserInput | UserQuestionHistoryCreateOrConnectWithoutUserInput[]
    upsert?: UserQuestionHistoryUpsertWithWhereUniqueWithoutUserInput | UserQuestionHistoryUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: UserQuestionHistoryCreateManyUserInputEnvelope
    set?: UserQuestionHistoryWhereUniqueInput | UserQuestionHistoryWhereUniqueInput[]
    disconnect?: UserQuestionHistoryWhereUniqueInput | UserQuestionHistoryWhereUniqueInput[]
    delete?: UserQuestionHistoryWhereUniqueInput | UserQuestionHistoryWhereUniqueInput[]
    connect?: UserQuestionHistoryWhereUniqueInput | UserQuestionHistoryWhereUniqueInput[]
    update?: UserQuestionHistoryUpdateWithWhereUniqueWithoutUserInput | UserQuestionHistoryUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: UserQuestionHistoryUpdateManyWithWhereWithoutUserInput | UserQuestionHistoryUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: UserQuestionHistoryScalarWhereInput | UserQuestionHistoryScalarWhereInput[]
  }

  export type UserCreateNestedOneWithoutCooldownsInput = {
    create?: XOR<UserCreateWithoutCooldownsInput, UserUncheckedCreateWithoutCooldownsInput>
    connectOrCreate?: UserCreateOrConnectWithoutCooldownsInput
    connect?: UserWhereUniqueInput
  }

  export type UserUpdateOneRequiredWithoutCooldownsNestedInput = {
    create?: XOR<UserCreateWithoutCooldownsInput, UserUncheckedCreateWithoutCooldownsInput>
    connectOrCreate?: UserCreateOrConnectWithoutCooldownsInput
    upsert?: UserUpsertWithoutCooldownsInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutCooldownsInput, UserUpdateWithoutCooldownsInput>, UserUncheckedUpdateWithoutCooldownsInput>
  }

  export type BoolFieldUpdateOperationsInput = {
    set?: boolean
  }

  export type UserCreateNestedOneWithoutTarotReadingsInput = {
    create?: XOR<UserCreateWithoutTarotReadingsInput, UserUncheckedCreateWithoutTarotReadingsInput>
    connectOrCreate?: UserCreateOrConnectWithoutTarotReadingsInput
    connect?: UserWhereUniqueInput
  }

  export type UserUpdateOneWithoutTarotReadingsNestedInput = {
    create?: XOR<UserCreateWithoutTarotReadingsInput, UserUncheckedCreateWithoutTarotReadingsInput>
    connectOrCreate?: UserCreateOrConnectWithoutTarotReadingsInput
    upsert?: UserUpsertWithoutTarotReadingsInput
    disconnect?: UserWhereInput | boolean
    delete?: UserWhereInput | boolean
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutTarotReadingsInput, UserUpdateWithoutTarotReadingsInput>, UserUncheckedUpdateWithoutTarotReadingsInput>
  }

  export type UserCreateNestedOneWithoutQuestionHistoryInput = {
    create?: XOR<UserCreateWithoutQuestionHistoryInput, UserUncheckedCreateWithoutQuestionHistoryInput>
    connectOrCreate?: UserCreateOrConnectWithoutQuestionHistoryInput
    connect?: UserWhereUniqueInput
  }

  export type UserUpdateOneWithoutQuestionHistoryNestedInput = {
    create?: XOR<UserCreateWithoutQuestionHistoryInput, UserUncheckedCreateWithoutQuestionHistoryInput>
    connectOrCreate?: UserCreateOrConnectWithoutQuestionHistoryInput
    upsert?: UserUpsertWithoutQuestionHistoryInput
    disconnect?: UserWhereInput | boolean
    delete?: UserWhereInput | boolean
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutQuestionHistoryInput, UserUpdateWithoutQuestionHistoryInput>, UserUncheckedUpdateWithoutQuestionHistoryInput>
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedBoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type UserFortuneCooldownCreateWithoutUserInput = {
    id?: string
    fortuneType: string
    cooldownExpiresAt: Date | string
    createdAt?: Date | string
  }

  export type UserFortuneCooldownUncheckedCreateWithoutUserInput = {
    id?: string
    fortuneType: string
    cooldownExpiresAt: Date | string
    createdAt?: Date | string
  }

  export type UserFortuneCooldownCreateOrConnectWithoutUserInput = {
    where: UserFortuneCooldownWhereUniqueInput
    create: XOR<UserFortuneCooldownCreateWithoutUserInput, UserFortuneCooldownUncheckedCreateWithoutUserInput>
  }

  export type UserFortuneCooldownCreateManyUserInputEnvelope = {
    data: UserFortuneCooldownCreateManyUserInput | UserFortuneCooldownCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type TarotReadingCreateWithoutUserInput = {
    id?: string
    readingType: string
    spreadType?: string | null
    cardCount?: number
    promptTokens?: number
    completionTokens?: number
    totalTokens?: number
    ipAddress?: string | null
    userAgent?: string | null
    createdAt?: Date | string
  }

  export type TarotReadingUncheckedCreateWithoutUserInput = {
    id?: string
    readingType: string
    spreadType?: string | null
    cardCount?: number
    promptTokens?: number
    completionTokens?: number
    totalTokens?: number
    ipAddress?: string | null
    userAgent?: string | null
    createdAt?: Date | string
  }

  export type TarotReadingCreateOrConnectWithoutUserInput = {
    where: TarotReadingWhereUniqueInput
    create: XOR<TarotReadingCreateWithoutUserInput, TarotReadingUncheckedCreateWithoutUserInput>
  }

  export type TarotReadingCreateManyUserInputEnvelope = {
    data: TarotReadingCreateManyUserInput | TarotReadingCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type UserQuestionHistoryCreateWithoutUserInput = {
    id?: string
    question: string
    questionType: string
    spreadType?: string | null
    consultantName?: string | null
    nameType?: string | null
    geminiResponse: string
    responseSummary: string
    responseDate: string
    ipAddress?: string | null
    userAgent?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserQuestionHistoryUncheckedCreateWithoutUserInput = {
    id?: string
    question: string
    questionType: string
    spreadType?: string | null
    consultantName?: string | null
    nameType?: string | null
    geminiResponse: string
    responseSummary: string
    responseDate: string
    ipAddress?: string | null
    userAgent?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserQuestionHistoryCreateOrConnectWithoutUserInput = {
    where: UserQuestionHistoryWhereUniqueInput
    create: XOR<UserQuestionHistoryCreateWithoutUserInput, UserQuestionHistoryUncheckedCreateWithoutUserInput>
  }

  export type UserQuestionHistoryCreateManyUserInputEnvelope = {
    data: UserQuestionHistoryCreateManyUserInput | UserQuestionHistoryCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type UserFortuneCooldownUpsertWithWhereUniqueWithoutUserInput = {
    where: UserFortuneCooldownWhereUniqueInput
    update: XOR<UserFortuneCooldownUpdateWithoutUserInput, UserFortuneCooldownUncheckedUpdateWithoutUserInput>
    create: XOR<UserFortuneCooldownCreateWithoutUserInput, UserFortuneCooldownUncheckedCreateWithoutUserInput>
  }

  export type UserFortuneCooldownUpdateWithWhereUniqueWithoutUserInput = {
    where: UserFortuneCooldownWhereUniqueInput
    data: XOR<UserFortuneCooldownUpdateWithoutUserInput, UserFortuneCooldownUncheckedUpdateWithoutUserInput>
  }

  export type UserFortuneCooldownUpdateManyWithWhereWithoutUserInput = {
    where: UserFortuneCooldownScalarWhereInput
    data: XOR<UserFortuneCooldownUpdateManyMutationInput, UserFortuneCooldownUncheckedUpdateManyWithoutUserInput>
  }

  export type UserFortuneCooldownScalarWhereInput = {
    AND?: UserFortuneCooldownScalarWhereInput | UserFortuneCooldownScalarWhereInput[]
    OR?: UserFortuneCooldownScalarWhereInput[]
    NOT?: UserFortuneCooldownScalarWhereInput | UserFortuneCooldownScalarWhereInput[]
    id?: StringFilter<"UserFortuneCooldown"> | string
    userId?: StringFilter<"UserFortuneCooldown"> | string
    fortuneType?: StringFilter<"UserFortuneCooldown"> | string
    cooldownExpiresAt?: DateTimeFilter<"UserFortuneCooldown"> | Date | string
    createdAt?: DateTimeFilter<"UserFortuneCooldown"> | Date | string
  }

  export type TarotReadingUpsertWithWhereUniqueWithoutUserInput = {
    where: TarotReadingWhereUniqueInput
    update: XOR<TarotReadingUpdateWithoutUserInput, TarotReadingUncheckedUpdateWithoutUserInput>
    create: XOR<TarotReadingCreateWithoutUserInput, TarotReadingUncheckedCreateWithoutUserInput>
  }

  export type TarotReadingUpdateWithWhereUniqueWithoutUserInput = {
    where: TarotReadingWhereUniqueInput
    data: XOR<TarotReadingUpdateWithoutUserInput, TarotReadingUncheckedUpdateWithoutUserInput>
  }

  export type TarotReadingUpdateManyWithWhereWithoutUserInput = {
    where: TarotReadingScalarWhereInput
    data: XOR<TarotReadingUpdateManyMutationInput, TarotReadingUncheckedUpdateManyWithoutUserInput>
  }

  export type TarotReadingScalarWhereInput = {
    AND?: TarotReadingScalarWhereInput | TarotReadingScalarWhereInput[]
    OR?: TarotReadingScalarWhereInput[]
    NOT?: TarotReadingScalarWhereInput | TarotReadingScalarWhereInput[]
    id?: StringFilter<"TarotReading"> | string
    userId?: StringNullableFilter<"TarotReading"> | string | null
    readingType?: StringFilter<"TarotReading"> | string
    spreadType?: StringNullableFilter<"TarotReading"> | string | null
    cardCount?: IntFilter<"TarotReading"> | number
    promptTokens?: IntFilter<"TarotReading"> | number
    completionTokens?: IntFilter<"TarotReading"> | number
    totalTokens?: IntFilter<"TarotReading"> | number
    ipAddress?: StringNullableFilter<"TarotReading"> | string | null
    userAgent?: StringNullableFilter<"TarotReading"> | string | null
    createdAt?: DateTimeFilter<"TarotReading"> | Date | string
  }

  export type UserQuestionHistoryUpsertWithWhereUniqueWithoutUserInput = {
    where: UserQuestionHistoryWhereUniqueInput
    update: XOR<UserQuestionHistoryUpdateWithoutUserInput, UserQuestionHistoryUncheckedUpdateWithoutUserInput>
    create: XOR<UserQuestionHistoryCreateWithoutUserInput, UserQuestionHistoryUncheckedCreateWithoutUserInput>
  }

  export type UserQuestionHistoryUpdateWithWhereUniqueWithoutUserInput = {
    where: UserQuestionHistoryWhereUniqueInput
    data: XOR<UserQuestionHistoryUpdateWithoutUserInput, UserQuestionHistoryUncheckedUpdateWithoutUserInput>
  }

  export type UserQuestionHistoryUpdateManyWithWhereWithoutUserInput = {
    where: UserQuestionHistoryScalarWhereInput
    data: XOR<UserQuestionHistoryUpdateManyMutationInput, UserQuestionHistoryUncheckedUpdateManyWithoutUserInput>
  }

  export type UserQuestionHistoryScalarWhereInput = {
    AND?: UserQuestionHistoryScalarWhereInput | UserQuestionHistoryScalarWhereInput[]
    OR?: UserQuestionHistoryScalarWhereInput[]
    NOT?: UserQuestionHistoryScalarWhereInput | UserQuestionHistoryScalarWhereInput[]
    id?: StringFilter<"UserQuestionHistory"> | string
    userId?: StringNullableFilter<"UserQuestionHistory"> | string | null
    question?: StringFilter<"UserQuestionHistory"> | string
    questionType?: StringFilter<"UserQuestionHistory"> | string
    spreadType?: StringNullableFilter<"UserQuestionHistory"> | string | null
    consultantName?: StringNullableFilter<"UserQuestionHistory"> | string | null
    nameType?: StringNullableFilter<"UserQuestionHistory"> | string | null
    geminiResponse?: StringFilter<"UserQuestionHistory"> | string
    responseSummary?: StringFilter<"UserQuestionHistory"> | string
    responseDate?: StringFilter<"UserQuestionHistory"> | string
    ipAddress?: StringNullableFilter<"UserQuestionHistory"> | string | null
    userAgent?: StringNullableFilter<"UserQuestionHistory"> | string | null
    createdAt?: DateTimeFilter<"UserQuestionHistory"> | Date | string
    updatedAt?: DateTimeFilter<"UserQuestionHistory"> | Date | string
  }

  export type UserCreateWithoutCooldownsInput = {
    id?: string
    email: string
    hashedPassword?: string | null
    name?: string | null
    credits?: number
    lastCreditDeduction?: Date | string | null
    googleId?: string | null
    kakaoId?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    tarotReadings?: TarotReadingCreateNestedManyWithoutUserInput
    questionHistory?: UserQuestionHistoryCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutCooldownsInput = {
    id?: string
    email: string
    hashedPassword?: string | null
    name?: string | null
    credits?: number
    lastCreditDeduction?: Date | string | null
    googleId?: string | null
    kakaoId?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    tarotReadings?: TarotReadingUncheckedCreateNestedManyWithoutUserInput
    questionHistory?: UserQuestionHistoryUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutCooldownsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutCooldownsInput, UserUncheckedCreateWithoutCooldownsInput>
  }

  export type UserUpsertWithoutCooldownsInput = {
    update: XOR<UserUpdateWithoutCooldownsInput, UserUncheckedUpdateWithoutCooldownsInput>
    create: XOR<UserCreateWithoutCooldownsInput, UserUncheckedCreateWithoutCooldownsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutCooldownsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutCooldownsInput, UserUncheckedUpdateWithoutCooldownsInput>
  }

  export type UserUpdateWithoutCooldownsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    hashedPassword?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    credits?: IntFieldUpdateOperationsInput | number
    lastCreditDeduction?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    googleId?: NullableStringFieldUpdateOperationsInput | string | null
    kakaoId?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    tarotReadings?: TarotReadingUpdateManyWithoutUserNestedInput
    questionHistory?: UserQuestionHistoryUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutCooldownsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    hashedPassword?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    credits?: IntFieldUpdateOperationsInput | number
    lastCreditDeduction?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    googleId?: NullableStringFieldUpdateOperationsInput | string | null
    kakaoId?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    tarotReadings?: TarotReadingUncheckedUpdateManyWithoutUserNestedInput
    questionHistory?: UserQuestionHistoryUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserCreateWithoutTarotReadingsInput = {
    id?: string
    email: string
    hashedPassword?: string | null
    name?: string | null
    credits?: number
    lastCreditDeduction?: Date | string | null
    googleId?: string | null
    kakaoId?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    cooldowns?: UserFortuneCooldownCreateNestedManyWithoutUserInput
    questionHistory?: UserQuestionHistoryCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutTarotReadingsInput = {
    id?: string
    email: string
    hashedPassword?: string | null
    name?: string | null
    credits?: number
    lastCreditDeduction?: Date | string | null
    googleId?: string | null
    kakaoId?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    cooldowns?: UserFortuneCooldownUncheckedCreateNestedManyWithoutUserInput
    questionHistory?: UserQuestionHistoryUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutTarotReadingsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutTarotReadingsInput, UserUncheckedCreateWithoutTarotReadingsInput>
  }

  export type UserUpsertWithoutTarotReadingsInput = {
    update: XOR<UserUpdateWithoutTarotReadingsInput, UserUncheckedUpdateWithoutTarotReadingsInput>
    create: XOR<UserCreateWithoutTarotReadingsInput, UserUncheckedCreateWithoutTarotReadingsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutTarotReadingsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutTarotReadingsInput, UserUncheckedUpdateWithoutTarotReadingsInput>
  }

  export type UserUpdateWithoutTarotReadingsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    hashedPassword?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    credits?: IntFieldUpdateOperationsInput | number
    lastCreditDeduction?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    googleId?: NullableStringFieldUpdateOperationsInput | string | null
    kakaoId?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    cooldowns?: UserFortuneCooldownUpdateManyWithoutUserNestedInput
    questionHistory?: UserQuestionHistoryUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutTarotReadingsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    hashedPassword?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    credits?: IntFieldUpdateOperationsInput | number
    lastCreditDeduction?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    googleId?: NullableStringFieldUpdateOperationsInput | string | null
    kakaoId?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    cooldowns?: UserFortuneCooldownUncheckedUpdateManyWithoutUserNestedInput
    questionHistory?: UserQuestionHistoryUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserCreateWithoutQuestionHistoryInput = {
    id?: string
    email: string
    hashedPassword?: string | null
    name?: string | null
    credits?: number
    lastCreditDeduction?: Date | string | null
    googleId?: string | null
    kakaoId?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    cooldowns?: UserFortuneCooldownCreateNestedManyWithoutUserInput
    tarotReadings?: TarotReadingCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutQuestionHistoryInput = {
    id?: string
    email: string
    hashedPassword?: string | null
    name?: string | null
    credits?: number
    lastCreditDeduction?: Date | string | null
    googleId?: string | null
    kakaoId?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    cooldowns?: UserFortuneCooldownUncheckedCreateNestedManyWithoutUserInput
    tarotReadings?: TarotReadingUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutQuestionHistoryInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutQuestionHistoryInput, UserUncheckedCreateWithoutQuestionHistoryInput>
  }

  export type UserUpsertWithoutQuestionHistoryInput = {
    update: XOR<UserUpdateWithoutQuestionHistoryInput, UserUncheckedUpdateWithoutQuestionHistoryInput>
    create: XOR<UserCreateWithoutQuestionHistoryInput, UserUncheckedCreateWithoutQuestionHistoryInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutQuestionHistoryInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutQuestionHistoryInput, UserUncheckedUpdateWithoutQuestionHistoryInput>
  }

  export type UserUpdateWithoutQuestionHistoryInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    hashedPassword?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    credits?: IntFieldUpdateOperationsInput | number
    lastCreditDeduction?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    googleId?: NullableStringFieldUpdateOperationsInput | string | null
    kakaoId?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    cooldowns?: UserFortuneCooldownUpdateManyWithoutUserNestedInput
    tarotReadings?: TarotReadingUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutQuestionHistoryInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    hashedPassword?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    credits?: IntFieldUpdateOperationsInput | number
    lastCreditDeduction?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    googleId?: NullableStringFieldUpdateOperationsInput | string | null
    kakaoId?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    cooldowns?: UserFortuneCooldownUncheckedUpdateManyWithoutUserNestedInput
    tarotReadings?: TarotReadingUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserFortuneCooldownCreateManyUserInput = {
    id?: string
    fortuneType: string
    cooldownExpiresAt: Date | string
    createdAt?: Date | string
  }

  export type TarotReadingCreateManyUserInput = {
    id?: string
    readingType: string
    spreadType?: string | null
    cardCount?: number
    promptTokens?: number
    completionTokens?: number
    totalTokens?: number
    ipAddress?: string | null
    userAgent?: string | null
    createdAt?: Date | string
  }

  export type UserQuestionHistoryCreateManyUserInput = {
    id?: string
    question: string
    questionType: string
    spreadType?: string | null
    consultantName?: string | null
    nameType?: string | null
    geminiResponse: string
    responseSummary: string
    responseDate: string
    ipAddress?: string | null
    userAgent?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserFortuneCooldownUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    fortuneType?: StringFieldUpdateOperationsInput | string
    cooldownExpiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserFortuneCooldownUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    fortuneType?: StringFieldUpdateOperationsInput | string
    cooldownExpiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserFortuneCooldownUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    fortuneType?: StringFieldUpdateOperationsInput | string
    cooldownExpiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TarotReadingUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    readingType?: StringFieldUpdateOperationsInput | string
    spreadType?: NullableStringFieldUpdateOperationsInput | string | null
    cardCount?: IntFieldUpdateOperationsInput | number
    promptTokens?: IntFieldUpdateOperationsInput | number
    completionTokens?: IntFieldUpdateOperationsInput | number
    totalTokens?: IntFieldUpdateOperationsInput | number
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TarotReadingUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    readingType?: StringFieldUpdateOperationsInput | string
    spreadType?: NullableStringFieldUpdateOperationsInput | string | null
    cardCount?: IntFieldUpdateOperationsInput | number
    promptTokens?: IntFieldUpdateOperationsInput | number
    completionTokens?: IntFieldUpdateOperationsInput | number
    totalTokens?: IntFieldUpdateOperationsInput | number
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TarotReadingUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    readingType?: StringFieldUpdateOperationsInput | string
    spreadType?: NullableStringFieldUpdateOperationsInput | string | null
    cardCount?: IntFieldUpdateOperationsInput | number
    promptTokens?: IntFieldUpdateOperationsInput | number
    completionTokens?: IntFieldUpdateOperationsInput | number
    totalTokens?: IntFieldUpdateOperationsInput | number
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserQuestionHistoryUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    question?: StringFieldUpdateOperationsInput | string
    questionType?: StringFieldUpdateOperationsInput | string
    spreadType?: NullableStringFieldUpdateOperationsInput | string | null
    consultantName?: NullableStringFieldUpdateOperationsInput | string | null
    nameType?: NullableStringFieldUpdateOperationsInput | string | null
    geminiResponse?: StringFieldUpdateOperationsInput | string
    responseSummary?: StringFieldUpdateOperationsInput | string
    responseDate?: StringFieldUpdateOperationsInput | string
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserQuestionHistoryUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    question?: StringFieldUpdateOperationsInput | string
    questionType?: StringFieldUpdateOperationsInput | string
    spreadType?: NullableStringFieldUpdateOperationsInput | string | null
    consultantName?: NullableStringFieldUpdateOperationsInput | string | null
    nameType?: NullableStringFieldUpdateOperationsInput | string | null
    geminiResponse?: StringFieldUpdateOperationsInput | string
    responseSummary?: StringFieldUpdateOperationsInput | string
    responseDate?: StringFieldUpdateOperationsInput | string
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserQuestionHistoryUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    question?: StringFieldUpdateOperationsInput | string
    questionType?: StringFieldUpdateOperationsInput | string
    spreadType?: NullableStringFieldUpdateOperationsInput | string | null
    consultantName?: NullableStringFieldUpdateOperationsInput | string | null
    nameType?: NullableStringFieldUpdateOperationsInput | string | null
    geminiResponse?: StringFieldUpdateOperationsInput | string
    responseSummary?: StringFieldUpdateOperationsInput | string
    responseDate?: StringFieldUpdateOperationsInput | string
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}