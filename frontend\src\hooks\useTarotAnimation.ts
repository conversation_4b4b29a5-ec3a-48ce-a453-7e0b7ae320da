import { useRef, useCallback } from 'react';
import { ManagerSpreadPosition } from './useTarotState';

export const useTarotAnimation = () => {
  const spreadContainerRef = useRef<HTMLDivElement>(null);
  const baseZIndex = 1000; // 기본 z-index

  // 스프레드 레이아웃 스타일 계산 함수
  const getSpreadLayoutStyles = useCallback(( 
    _fortuneType: string, 
    index: number,
    _totalCards: number,
    managerPositions?: ManagerSpreadPosition[]
  ): React.CSSProperties => {
    // 원래 카드 크기 및 분포 계산을 위한 창 크기 확인
    const windowWidth = window.innerWidth;
    
    // 창 크기에 따른 배율 계산 (화면 크기에 대한 퍼센트)
    const viewportRatio = windowWidth / 1200; // 1200px를 기준으로 한 비율

    // 화면 너비의 75%만 사용하도록 조정하는 계수
    const screenWidthFactor = 0.75;
    
    // 스케일 계수 계산 - 작은 화면일수록 카드가 작아짐
    // 카드 크기를 조금 더 작게 조정 (0.85 계수 적용)
    const scaleBase = Math.min(0.85, viewportRatio * 0.85);
    
    // 위치 확대 계수 - 작은 화면에서 더 멀리 분포시키기 위한 계수
    // 화면이 작아질수록 값이 커짐(1.5~2.5 범위)
    // screenWidthFactor를 적용하여 75% 범위 내에서 분포
    const spreadFactor = Math.max(2.0, 1.5 / viewportRatio) * screenWidthFactor;
    
    // 기본 스타일 설정
    const basePosition: React.CSSProperties = {
      position: 'absolute',
      transformOrigin: 'center',
      transition: 'all 0.5s ease-in-out',
    };
    
    // Manager 위치 데이터 사용
    if (managerPositions && managerPositions.length > index) {
      const managerPos = managerPositions[index];
      
      // 좌표값 추출 (단위 없이 숫자만 추출)
      const leftValue = parseFloat(managerPos.left);
      const topValue = parseFloat(managerPos.top);
      
      // 중앙을 기준(50%)으로 한 거리 계산
      const leftOffset = leftValue - 50;
      const topOffset = topValue - 50;
      
      // 화면 크기에 따라 조절된 새 좌표값 계산
      // spreadFactor를 적용하여 작은 화면에서 더 멀리 배치
      const adjustedLeft = 50 + (leftOffset * spreadFactor);
      const adjustedTop = 50 + (topOffset * spreadFactor);
      
      // 매니저 transform 처리
      const managerTransform = managerPos.transform || '';
      
      // managerTransform에 translate(-50%, -50%)가 있는지 확인
      const hasTranslate = managerTransform.includes('translate(-50%, -50%)');
      
      // 최종 transform 생성 - 중요: 순서가 중요함!
      let finalTransform = '';
      
      if (hasTranslate) {
        // 이미 translate가 있는 경우, 스케일만 추가
        finalTransform = `scale(${scaleBase}) ${managerTransform}`;
      } else {
        // translate가 없는 경우, 중앙 정렬 추가
        finalTransform = `scale(${scaleBase}) translate(-50%, -50%) ${managerTransform}`.trim();
      }
      
      // 결과 반환
      return {
        ...basePosition,
        left: `${adjustedLeft}%`,
        top: `${adjustedTop}%`,
        transform: finalTransform,
      };
    }
    
    // 매니저 데이터가 없는 경우 기본 중앙 정렬
    return {
      ...basePosition,
      left: '50%',
      top: '50%',
      transform: `scale(${scaleBase}) translate(-50%, -50%)`,
    };
  }, []);

  // 트랜지션 함수
  const transitionToView = useCallback((
    nextView: string,
    currentView: string,
    fullScreenViews: string[],
    isFortuneMusic: boolean,
    playSound: (path: string) => void,
    stopSound: () => void,
    setCurrentView: (view: any) => void,
    setContentOpacity: (opacity: number) => void,
    setShowTransitionOverlay: (show: boolean) => void,
    SOUND_PATHS: any
  ) => {
    // 초기 유형 선택화면으로 돌아갈 때는 배경 전체를 덮는 오버레이 숨기기
    const isReturningToNonFullscreen = 
      fullScreenViews.includes(currentView) && 
      !fullScreenViews.includes(nextView);

    // 운세 음악 재생 또는 중지
    const fortuneViews = ['meditationIntro', 'cardCountSelection', 'cardRevealAndInterpretation', 'finalInterpretation', 'resultEnd'];
    
    // 운세 관련 화면에서 다른 화면으로 이동하는 경우에만 음악 중지
    if (!fortuneViews.includes(nextView) && fortuneViews.includes(currentView)) {
      stopSound();
    } 
    // 운세 관련 화면으로 처음 진입할 때만 음악 재생 (이미 재생 중이면 중복해서 재생하지 않음)
    else if (fortuneViews.includes(nextView) && !isFortuneMusic) {
      playSound(SOUND_PATHS.fortuneMusic);
    }

    if (isReturningToNonFullscreen) {
      // 전체화면에서 일반 화면으로 돌아갈 때는 즉시 전환
      setCurrentView(nextView);
      setContentOpacity(1);
      setShowTransitionOverlay(false);
    } else {
      // 일반적인 화면 전환 (페이드 아웃 -> 페이드 인)
      setContentOpacity(0);
      
      setTimeout(() => {
        setCurrentView(nextView);
        setContentOpacity(1);
      }, 400); // 페이드 아웃 시간과 맞춤
    }
  }, []);

  // 카드 부채꼴 배치 계산 함수
  const calculateFanPosition = useCallback((
    index: number, 
    totalCards: number, 
    horizontalOffset: number = 0,
    isInitializing: boolean = false
  ) => {
    if (isInitializing) {
      // 초기화 상태에서는 모든 카드를 중앙에 겹쳐서 배치
      return {
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)',
        zIndex: 1000 + index,
      };
    }

    const centerIndex = (totalCards - 1) / 2;
    const angleStep = 8; // 각 카드 간의 각도 (도)
    const radiusX = 180; // 수평 반지름
    const radiusY = 50; // 수직 반지름 (타원 효과)
    
    // 카드의 각도 계산
    const angle = (index - centerIndex) * angleStep;
    const radian = (angle * Math.PI) / 180;
    
    // 타원 궤도 상의 위치 계산
    const x = Math.sin(radian) * radiusX + horizontalOffset;
    const y = Math.cos(radian) * radiusY - radiusY; // 아래쪽으로 배치
    
    return {
      left: `calc(50% + ${x}px)`,
      top: `calc(50% + ${y}px)`,
      transform: `translate(-50%, -50%) rotate(${angle}deg)`,
      zIndex: totalCards - Math.abs(index - centerIndex), // 중앙 카드가 가장 위에
    };
  }, []);

  return {
    // Refs
    spreadContainerRef,
    baseZIndex,
    
    // Functions
    getSpreadLayoutStyles,
    transitionToView,
    calculateFanPosition,
  };
};
