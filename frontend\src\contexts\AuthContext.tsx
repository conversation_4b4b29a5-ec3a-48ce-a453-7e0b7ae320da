import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';

interface User {
  id: string; // 또는 number, 백엔드 응답에 따라
  name: string;
  email: string;
  credits: number;
}

interface AuthContextType {
  isLoggedIn: boolean;
  user: User | null;
  token: string | null;
  login: (token: string, userData: User) => void;
  logout: () => void;
  updateUserCredits: (newCredits: number) => void;
  updateUserName: (newName: string) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const TOKEN_KEY = 'tarotToken';
const USER_KEY = 'userData'; // script.js의 userName, userCredits 대신 user 객체 전체를 저장

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(() => {
    const initialToken = localStorage.getItem(TOKEN_KEY);
    // console.log('[AuthContext-useState-isLoggedIn] Initial localStorage token:', initialToken);
    return !!initialToken;
  });
  const [user, setUser] = useState<User | null>(() => {
    const storedUser = localStorage.getItem(USER_KEY);
    // console.log('[AuthContext-useState-user] Initial localStorage user:', storedUser);
    try {
      return storedUser ? JSON.parse(storedUser) : null;
    } catch (e) {
      // console.error('[AuthContext-useState-user] Error parsing initial user:', e, storedUser);
      localStorage.removeItem(USER_KEY); 
      return null;
    }
  });
  const [token, setToken] = useState<string | null>(() => {
    const initialTokenVal = localStorage.getItem(TOKEN_KEY);
    // console.log('[AuthContext-useState-token] Initial localStorage token val:', initialTokenVal);
    return initialTokenVal;
  });

  useEffect(() => {
    // console.log('[AuthContext-useEffect] Setting up storage listener.');
    const storageEventListener = (event: StorageEvent) => {
      // console.log('[AuthContext-storageEventListener] Storage event. Key:', event.key, 'NewValue:', event.newValue, 'OldValue:', event.oldValue);
      if (event.key === TOKEN_KEY || event.key === USER_KEY) {
        const currentToken = localStorage.getItem(TOKEN_KEY);
        const storedUser = localStorage.getItem(USER_KEY);
        // console.log('[AuthContext-storageEventListener] Read from localStorage after event - token:', currentToken, 'user:', storedUser);

        setIsLoggedIn(!!currentToken);
        setToken(currentToken);
        try {
          setUser(storedUser ? JSON.parse(storedUser) : null);
        } catch (e) {
          // console.error('[AuthContext-storageEventListener] Error parsing user from event:', e, storedUser);
          setUser(null);
        }
        // console.log('[AuthContext-storageEventListener] State updated after event. isLoggedIn:', !!currentToken);
      }
    };

    window.addEventListener('storage', storageEventListener);

    return () => {
      // console.log('[AuthContext-useEffect] Cleaning up storage listener.');
      window.removeEventListener('storage', storageEventListener);
    };
  }, []);

  const login = (newToken: string, userData: User) => {
    // console.log('[AuthContext] Attempting to login. Token:', newToken);
    // console.log('[AuthContext] UserData for localStorage:', userData);
    try {
      localStorage.setItem(TOKEN_KEY, newToken);
      // console.log('[AuthContext] localStorage "', TOKEN_KEY, '" set with value:', localStorage.getItem(TOKEN_KEY));
      localStorage.setItem(USER_KEY, JSON.stringify(userData));
      // console.log('[AuthContext] localStorage "', USER_KEY, '" set with value:', localStorage.getItem(USER_KEY));
    } catch (e) {
      console.error('[AuthContext] Error setting localStorage:', e); // 에러 발생 시 로그는 유지
    }
    setIsLoggedIn(true);
    setUser(userData);
    setToken(newToken);
    // console.log('[AuthContext] State updated. isLoggedIn:', true, 'User:', userData, 'Token:', newToken);
  };

  const logout = () => {
    // console.log('[AuthContext] Logging out. Removing token and user from localStorage.');
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(USER_KEY);
    // console.log('[AuthContext] localStorage "', TOKEN_KEY, '" after removal:', localStorage.getItem(TOKEN_KEY));
    // console.log('[AuthContext] localStorage "', USER_KEY, '" after removal:', localStorage.getItem(USER_KEY));
    setIsLoggedIn(false);
    setUser(null);
    setToken(null);
    // console.log('[AuthContext] Logout state updated.');
  };

  const updateUserCredits = (newCredits: number) => {
    if (user) {
      const updatedUser = { ...user, credits: newCredits };
      setUser(updatedUser);
      localStorage.setItem(USER_KEY, JSON.stringify(updatedUser));
    }
  };

  const updateUserName = (newName: string) => {
    if (user) {
      const updatedUser = { ...user, name: newName };
      setUser(updatedUser);
      localStorage.setItem(USER_KEY, JSON.stringify(updatedUser));
    }
  };

  return (
    <AuthContext.Provider value={{ isLoggedIn, user, token, login, logout, updateUserCredits, updateUserName }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 