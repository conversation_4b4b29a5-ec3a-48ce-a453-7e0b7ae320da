import React, { lazy, Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './index.css'; // 통합된 CSS 파일 임포트
import Layout from './components/Layout'; // Layout 컴포넌트 임포트
import HomePage from './pages/HomePage';
// import { AuthProvider } from './contexts/AuthContext'; // App.tsx에서는 AuthProvider를 직접 사용하지 않음

const TarotCardsPage = lazy(() => import('./pages/TarotCardsPage'));
const TarotTutorialPage = lazy(() => import('./pages/TarotTutorialPage'));
const FortunePage = lazy(() => import('./pages/FortunePage')); // FortunePage 임포트
const TarotReadingPage = lazy(() => import('./pages/TarotReadingPage')); // TarotReadingPage 임포트
const ManagerPage = lazy(() => import('./pages/ManagerPage')); // ManagerPage 임포트

function App() {
  // Removed App-local modal states and handlers
  // const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  // const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  // const [isMyInfoModalOpen, setIsMyInfoModalOpen] = useState(false);

  // const openLoginModal = () => setIsLoginModalOpen(true);
  // const closeLoginModal = () => setIsLoginModalOpen(false);
  // const openRegisterModal = () => setIsRegisterModalOpen(true);
  // const closeRegisterModal = () => setIsRegisterModalOpen(false);
  // const openMyInfoModal = () => setIsMyInfoModalOpen(true);
  // const closeMyInfoModal = () => setIsMyInfoModalOpen(false);

  return (
    <Router>
      {/* <AuthProvider> // main.tsx에서 이미 제공하므로 여기서 제거 */}
        <Layout> {/* Layout component now wraps the routes and will provide Header, Footer, and Modals */}
          <Suspense fallback={<div className="loading-spinner"><div></div><div></div><div></div></div>}>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/tarot-cards" element={<TarotCardsPage />} />
              <Route path="/tarot-tutorial" element={<TarotTutorialPage />} />
              <Route path="/fortune" element={<FortunePage />} />
              <Route path="/tarot-reading" element={<TarotReadingPage />} />
              <Route path="/manager" element={<ManagerPage />} />
            </Routes>
          </Suspense>
        </Layout> {/* Closing Layout tag */}
        {/* 
          Removed Header and Footer from here as Layout will handle them.
          The comment about rendering modals here is also no longer relevant
          as Layout.tsx handles modal rendering.
        */}
      {/* </AuthProvider> */}
    </Router>
  );
}

export default App;
