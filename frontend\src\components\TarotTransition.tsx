import React from 'react';

interface TarotTransitionProps {
  showTransitionOverlay: boolean;
  isEnteringSpace?: boolean;
  children?: React.ReactNode;
}

const TarotTransition: React.FC<TarotTransitionProps> = ({
  showTransitionOverlay,
  isEnteringSpace = false,
  children
}) => {
  return (
    <>
      {/* 트랜지션 오버레이 */}
      <div className={`transition-overlay ${showTransitionOverlay ? 'active' : ''}`}>
        {children || (
          <div className="transition-content">
            <div className="cosmic-loader">
              <div className="cosmic-ring"></div>
              <div className="cosmic-ring"></div>
              <div className="cosmic-ring"></div>
            </div>
            <p className="transition-text">우주의 에너지가 흐르고 있습니다...</p>
          </div>
        )}
      </div>

      {/* 우주 진입 애니메이션 */}
      {isEnteringSpace && (
        <div className="space-entry-animation">
          <div className="stars-container">
            {[...Array(50)].map((_, i) => (
              <div 
                key={i} 
                className="star" 
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 3}s`,
                  animationDuration: `${2 + Math.random() * 2}s`
                }}
              />
            ))}
          </div>
          <div className="cosmic-portal">
            <div className="portal-ring"></div>
            <div className="portal-ring"></div>
            <div className="portal-ring"></div>
          </div>
          <div className="entry-text">
            <h2>신비로운 우주로의 여행이 시작됩니다...</h2>
          </div>
        </div>
      )}

      <style jsx>{`
        .transition-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
          z-index: 9999;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          pointer-events: none;
          transition: opacity 0.8s ease-in-out;
          visibility: hidden;
        }

        .transition-overlay.active {
          opacity: 1;
          pointer-events: all;
          visibility: visible;
        }

        .transition-content {
          text-align: center;
          color: #e0e0e0;
        }

        .cosmic-loader {
          position: relative;
          width: 120px;
          height: 120px;
          margin: 0 auto 30px;
        }

        .cosmic-ring {
          position: absolute;
          border: 2px solid transparent;
          border-top: 2px solid #fff59d;
          border-radius: 50%;
          animation: cosmicSpin 2s linear infinite;
        }

        .cosmic-ring:nth-child(1) {
          width: 120px;
          height: 120px;
          top: 0;
          left: 0;
          animation-duration: 2s;
        }

        .cosmic-ring:nth-child(2) {
          width: 90px;
          height: 90px;
          top: 15px;
          left: 15px;
          animation-duration: 1.5s;
          animation-direction: reverse;
          border-top-color: #a8d8ff;
        }

        .cosmic-ring:nth-child(3) {
          width: 60px;
          height: 60px;
          top: 30px;
          left: 30px;
          animation-duration: 1s;
          border-top-color: #dda0dd;
        }

        @keyframes cosmicSpin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .transition-text {
          font-size: 1.2rem;
          font-family: 'Poor Story', cursive;
          color: #c0c0c0;
          animation: textPulse 2s ease-in-out infinite;
        }

        @keyframes textPulse {
          0%, 100% { opacity: 0.7; }
          50% { opacity: 1; }
        }

        /* 우주 진입 애니메이션 스타일 */
        .space-entry-animation {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: radial-gradient(circle, #000428 0%, #004e92 100%);
          z-index: 10000;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          animation: spaceEntry 2.5s ease-out forwards;
        }

        @keyframes spaceEntry {
          0% {
            opacity: 0;
            transform: scale(0.8);
          }
          50% {
            opacity: 1;
            transform: scale(1.1);
          }
          100% {
            opacity: 0;
            transform: scale(1.2);
          }
        }

        .stars-container {
          position: absolute;
          width: 100%;
          height: 100%;
          overflow: hidden;
        }

        .star {
          position: absolute;
          width: 2px;
          height: 2px;
          background: white;
          border-radius: 50%;
          animation: starTwinkle 2s ease-in-out infinite;
        }

        @keyframes starTwinkle {
          0%, 100% { opacity: 0.3; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.5); }
        }

        .cosmic-portal {
          position: relative;
          width: 200px;
          height: 200px;
          margin-bottom: 40px;
        }

        .portal-ring {
          position: absolute;
          border: 3px solid transparent;
          border-radius: 50%;
          animation: portalSpin 3s linear infinite;
        }

        .portal-ring:nth-child(1) {
          width: 200px;
          height: 200px;
          border-color: rgba(255, 245, 157, 0.8);
          animation-duration: 3s;
        }

        .portal-ring:nth-child(2) {
          width: 150px;
          height: 150px;
          top: 25px;
          left: 25px;
          border-color: rgba(168, 216, 255, 0.6);
          animation-duration: 2s;
          animation-direction: reverse;
        }

        .portal-ring:nth-child(3) {
          width: 100px;
          height: 100px;
          top: 50px;
          left: 50px;
          border-color: rgba(221, 160, 221, 0.4);
          animation-duration: 1.5s;
        }

        @keyframes portalSpin {
          0% { transform: rotate(0deg) scale(1); opacity: 0.8; }
          50% { transform: rotate(180deg) scale(1.1); opacity: 1; }
          100% { transform: rotate(360deg) scale(1); opacity: 0.8; }
        }

        .entry-text {
          text-align: center;
          animation: entryTextFade 2.5s ease-out;
        }

        .entry-text h2 {
          font-size: 2rem;
          font-family: 'Cute Font', 'Poor Story', cursive;
          color: #fff59d;
          text-shadow: 0 0 20px rgba(255, 245, 157, 0.5);
          margin: 0;
        }

        @keyframes entryTextFade {
          0% { opacity: 0; transform: translateY(30px); }
          30% { opacity: 1; transform: translateY(0); }
          70% { opacity: 1; transform: translateY(0); }
          100% { opacity: 0; transform: translateY(-30px); }
        }

        /* 모바일 반응형 */
        @media (max-width: 768px) {
          .cosmic-loader {
            width: 80px;
            height: 80px;
          }

          .cosmic-ring:nth-child(1) {
            width: 80px;
            height: 80px;
          }

          .cosmic-ring:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 10px;
            left: 10px;
          }

          .cosmic-ring:nth-child(3) {
            width: 40px;
            height: 40px;
            top: 20px;
            left: 20px;
          }

          .transition-text {
            font-size: 1rem;
          }

          .cosmic-portal {
            width: 150px;
            height: 150px;
          }

          .portal-ring:nth-child(1) {
            width: 150px;
            height: 150px;
          }

          .portal-ring:nth-child(2) {
            width: 110px;
            height: 110px;
            top: 20px;
            left: 20px;
          }

          .portal-ring:nth-child(3) {
            width: 70px;
            height: 70px;
            top: 40px;
            left: 40px;
          }

          .entry-text h2 {
            font-size: 1.5rem;
          }
        }
      `}</style>
    </>
  );
};

export default TarotTransition;
