export interface TarotSpread {
  id: string;
  name: string;
  description: string;
  cardCount: number;
  cost: number;
  iconLayout: string; // 버튼에 표시될 스프레드 레이아웃의 시각적 표현 (CSS Grid 스타일)
  spreadType: string; // FortuneType과 매핑될 타입
  layoutDescription: string; // 각 카드 위치의 의미에 대한 설명
  className: string; // CSS 클래스 이름
}

// 스프레드 클래스 이름 정의
export const SPREAD_CLASS_NAMES = {
  THREE_CARD: 'spread-three-card',
  FOUR_CARD: 'spread-four-card',
  FIVE_CARD: 'spread-five-card',
  DOUBLE_LINE: 'spread-double-line',
  CELTIC_CROSS: 'spread-celtic-cross',
  MAGIC_SEVEN: 'spread-magic-seven',
  CROSS: 'spread-cross',
  PYRAMID: 'spread-pyramid',
  HORSESHOE: 'spread-horseshoe',
  AWEN: 'spread-awen',
  DRAGON_RAZIEL: 'spread-dragon-raziel',
  BINARY_CHOICE: 'spread-binary-choice',
  RELATIONSHIP: 'spread-relationship',
  CUP_OF_RELATIONSHIP: 'spread-cup-of-relationship',
  YIN_YANG: 'spread-yin-yang',
  READING_MIND: 'spread-reading-mind',
  HOROSCOPE: 'spread-horoscope'
};



// 타로 스프레드 데이터
export const tarotSpreads: TarotSpread[] = [
  {
    id: 'three_card',
    name: '3카드',
    description: '과거, 현재, 미래를 나타내는 기본적인 3장 스프레드입니다.',
    cardCount: 3,
    cost: 10,
    iconLayout: '',
    spreadType: 'customThreeCard',
    layoutDescription: '첫 번째 카드는 과거, 두 번째 카드는 현재, 세 번째 카드는 미래를 나타냅니다.',
    className: SPREAD_CLASS_NAMES.THREE_CARD
  },
  {
    id: 'four_card',
    name: '4카드',
    description: '문제의 현재 상황, 과제, 행동 방향, 결과를 보여주는 4장 스프레드입니다.',
    cardCount: 4,
    cost: 15,
    iconLayout: '',
    spreadType: 'customFourCard',
    layoutDescription: '첫 번째 카드는 현재 상황, 두 번째 카드는 당면 과제, 세 번째 카드는 권장되는 행동, 네 번째 카드는 잠재적 결과를 나타냅니다.',
    className: SPREAD_CLASS_NAMES.FOUR_CARD
  },
  {
    id: 'five_card',
    name: '5카드',
    description: '현재 상황의 다섯 가지 측면을 탐색하는 스프레드입니다.',
    cardCount: 5,
    cost: 20,
    iconLayout: '',
    spreadType: 'customFiveCard',
    layoutDescription: '중앙 카드는 현재 상황, 상단 카드는 영향력, 좌측 카드는 과거, 우측 카드는 미래, 하단 카드는 잠재적 결과를 나타냅니다.',
    className: SPREAD_CLASS_NAMES.FIVE_CARD
  },
  {
    id: 'double_line',
    name: '더블 라인',
    description: '두 줄로 나열된 6장의 카드로 현재와 미래의 에너지를 읽는 스프레드입니다.',
    cardCount: 6,
    cost: 25,
    iconLayout: '',
    spreadType: 'customDoubleLine',
    layoutDescription: '상단 3장은 현재의 에너지, 하단 3장은 미래의 발전 가능성을 나타냅니다.',
    className: SPREAD_CLASS_NAMES.DOUBLE_LINE
  },
  {
    id: 'celtic_cross',
    name: '켈틱 크로스',
    description: '복잡한 상황을 심층적으로 분석하는 가장 유명한 10장 스프레드입니다.',
    cardCount: 10,
    cost: 40,
    iconLayout: '',
    spreadType: 'customCelticCross',
    layoutDescription: '중앙 2장은 현재 상황과 도전, 주변 4장은 주요 영향, 우측 4장은 추가 통찰을 제공합니다.',
    className: SPREAD_CLASS_NAMES.CELTIC_CROSS
  },
  {
    id: 'magic_seven',
    name: '매직 세븐',
    description: '7개의 카드를 사용하여 상황의 다양한 측면을 탐색하는 스프레드입니다.',
    cardCount: 7,
    cost: 30,
    iconLayout: '',
    spreadType: 'customSevenCard',
    layoutDescription: '7개의 카드가 함께 작용하여 과거, 현재, 미래, 잠재적 결과 및 숨겨진 요소들을 보여줍니다.',
    className: SPREAD_CLASS_NAMES.MAGIC_SEVEN
  },
  {
    id: 'cross',
    name: '십자',
    description: '중앙 카드를 기준으로 십자가 형태로 배열된 5장 스프레드입니다.',
    cardCount: 5,
    cost: 20,
    iconLayout: '',
    spreadType: 'customCrossSpread',
    layoutDescription: '중앙 카드는 현재 상황, 상단은 이상, 좌측은 과거, 우측은 미래, 하단은 잠재적 결과를 나타냅니다.',
    className: SPREAD_CLASS_NAMES.CROSS
  },
  {
    id: 'pyramid',
    name: '피라미드',
    description: '피라미드 형태로 배열된 10장의 카드로 상황을 분석하는 스프레드입니다.',
    cardCount: 10,
    cost: 40,
    iconLayout: '',
    spreadType: 'customPyramid',
    layoutDescription: '하단에서 상단으로 올라가면서 상황의 기초부터 최종 결과까지 살펴봅니다.',
    className: SPREAD_CLASS_NAMES.PYRAMID
  },
  {
    id: 'horseshoe',
    name: '말발굽',
    description: '말발굽 형태로 배열된 7장의 카드로 과거에서 미래까지의 흐름을 읽는 스프레드입니다.',
    cardCount: 7,
    cost: 30,
    iconLayout: '',
    spreadType: 'customHorseshoe',
    layoutDescription: '과거부터 미래까지의 시간 흐름에 따라 카드를 배치하여 상황의 발전을 보여줍니다.',
    className: SPREAD_CLASS_NAMES.HORSESHOE
  },
  {
    id: 'awen',
    name: '어웬',
    description: '3개의 광선을 상징하는 켈트 신성 기호 어웬 형태의 7장 스프레드입니다.',
    cardCount: 7,
    cost: 30,
    iconLayout: '',
    spreadType: 'customAwen',
    layoutDescription: '상단 3장은 영감의 원천, 중간 3장은 현재의 영향, 하단 1장은 결과를 나타냅니다.',
    className: SPREAD_CLASS_NAMES.AWEN
  },
  {
    id: 'dragon_raziel',
    name: '드래곤 라자',
    description: '10장의 카드로 천사 라지엘의 지혜를 통해 상황을 통찰하는 스프레드입니다.',
    cardCount: 10,
    cost: 40,
    iconLayout: '',
    spreadType: 'customDragonRaziel',
    layoutDescription: '우주적 지혜와 연결하여 현재 상황에 대한 심층적인 통찰을 제공합니다.',
    className: SPREAD_CLASS_NAMES.DRAGON_RAZIEL
  },
  {
    id: 'binary_choice',
    name: '양자택일',
    description: '두 가지 선택지 사이에서 결정을 내려야 할 때 유용한 5장 스프레드입니다.',
    cardCount: 5,
    cost: 20,
    iconLayout: '',
    spreadType: 'customBinaryChoice',
    layoutDescription: '두 선택지의 장단점과 최종 결과를 비교하여 보여줍니다.',
    className: SPREAD_CLASS_NAMES.BINARY_CHOICE
  },
  {
    id: 'relationship',
    name: '릴레이션십',
    description: '두 사람 사이의 관계를 탐색하는 7장 스프레드입니다.',
    cardCount: 7,
    cost: 30,
    iconLayout: '',
    spreadType: 'customRelationship',
    layoutDescription: '각 개인의 느낌, 관계의 강점과 약점, 그리고 잠재적 발전 방향을 살펴봅니다.',
    className: SPREAD_CLASS_NAMES.RELATIONSHIP
  },
  {
    id: 'cup_of_relationship',
    name: '컵오브릴레이션십',
    description: '컵 모양으로 배열된 8장의 카드로 관계의 정서적 측면을 탐색하는 스프레드입니다.',
    cardCount: 8,
    cost: 35,
    iconLayout: '',
    spreadType: 'customCupOfRelationship',
    layoutDescription: '관계의 감정적 기반, 현재 감정, 미래의 감정적 발전을 보여줍니다.',
    className: SPREAD_CLASS_NAMES.CUP_OF_RELATIONSHIP
  },
  {
    id: 'yin_yang',
    name: '음양',
    description: '음과 양의 에너지 균형을 탐색하는 6장 스프레드입니다.',
    cardCount: 6,
    cost: 25,
    iconLayout: '',
    spreadType: 'customYinYang',
    layoutDescription: '상황의 음적(수동적) 측면과 양적(능동적) 측면의 균형을 살펴봅니다.',
    className: SPREAD_CLASS_NAMES.YIN_YANG
  },
  {
    id: 'reading_mind',
    name: '리딩 마인드',
    description: '내면의 생각과 감정을 탐색하는 7장 스프레드입니다.',
    cardCount: 7,
    cost: 30,
    iconLayout: '',
    spreadType: 'customReadingMind',
    layoutDescription: '의식적 사고, 무의식적 영향, 숨겨진 감정, 미래 방향성을 보여줍니다.',
    className: SPREAD_CLASS_NAMES.READING_MIND
  },
  {
    id: 'horoscope',
    name: '호로스코프',
    description: '12개의 점성 하우스를 기반으로 한 12장 스프레드입니다.',
    cardCount: 12,
    cost: 50,
    iconLayout: '',
    spreadType: 'customHoroscope',
    layoutDescription: '12개의 점성학적 하우스에 카드를 배치하여 삶의 다양한 영역에 대한 포괄적인 읽기를 제공합니다.',
    className: SPREAD_CLASS_NAMES.HOROSCOPE
  }
];

// 선택한 스프레드 ID로 스프레드 정보를 찾는 함수
export const getSpreadById = (id: string): TarotSpread | undefined => {
  return tarotSpreads.find(spread => spread.id === id);
};

// 선택한 스프레드 타입으로 스프레드 정보를 찾는 함수
export const getSpreadByType = (type: string): TarotSpread | undefined => {
  return tarotSpreads.find(spread => spread.spreadType === type);
};

// 기본 요금 정의 (필요에 따라 사용)
export const TAROT_COSTS = {
  THREE_CARD: 10,
  FOUR_CARD: 15,
  FIVE_CARD: 20,
  SIX_CARD: 25,
  SEVEN_CARD: 30,
  EIGHT_CARD: 35,
  NINE_CARD: 35,
  TEN_CARD: 40,
  TWELVE_CARD: 45
}; 