// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL") // 환경 변수에서 데이터베이스 URL을 가져옵니다.
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id                  String    @id @default(cuid())
  email               String    @unique
  hashedPassword      String? // 이메일/비밀번호 가입 시 사용 (암호화된 비밀번호)
  name                String? // 선택적 사용자 이름
  credits             Int       @default(0) // 타로 크레딧
  lastCreditDeduction DateTime? // Track the last time credits were deducted to prevent duplicates

  // 소셜 로그인 정보
  googleId String? @unique
  kakaoId  String? @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 사용자가 본 타로 기록 등 다른 모델과의 관계를 나중에 추가할 수 있습니다.
  // tarotReadings TarotReading[] 
  cooldowns UserFortuneCooldown[] // Relation to UserFortuneCooldown model
}

model UserFortuneCooldown {
  id                String   @id @default(cuid())
  userId            String
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  fortuneType       String // e.g., "today", "year_general", "year_love"
  cooldownExpiresAt DateTime

  createdAt DateTime @default(now())

  @@unique([userId, fortuneType]) // 특정 사용자는 각 운세 종류별로 하나의 쿨타임만 가짐
  @@index([userId])
  @@index([fortuneType])
}

model IPFortuneCooldown {
  id                String   @id @default(cuid())
  ipAddress         String // Store the IP address
  fortuneType       String // e.g., "today"
  cooldownExpiresAt DateTime

  createdAt DateTime @default(now())

  @@unique([ipAddress, fortuneType]) // Unique cooldown per IP and fortune type
  @@index([ipAddress])
}

model TarotSpread {
  id                String    @id @default(cuid())
  name              String // 스프레드 이름 (예: "3카드", "켈틱 크로스")
  description       String // 스프레드 설명
  cardCount         Int // 카드 수
  cost              Int // 크레딧 비용
  iconLayout        String    @default("") // 아이콘 레이아웃 (향후 확장용)
  spreadType        String    @unique // 스프레드 타입 (고유)
  layoutDescription String    @default("") // 레이아웃 설명
  className         String    @default("") // CSS 클래스명
  positions         String    @default("[]") // JSON 형태의 카드 위치 정보
  discount          Int       @default(0) // 할인률 (0-100)
  discountStartDate DateTime? // 할인 시작일 (옵션)
  discountEndDate   DateTime? // 할인 종료일 (옵션)
  isActive          Boolean   @default(true) // 활성화 여부
  order             Int       @default(0) // 표시 순서 (낮을수록 먼저 표시)

  // 프롬프트 템플릿 관련 필드
  promptTemplate     String @default("") // 기본 프롬프트 템플릿
  systemInstruction  String @default("") // 시스템 명령어/가이드라인 (개별 스프레드용, 글로벌 설정으로 이동 예정)
  cardPositionLabels String @default("[]") // JSON 형태의 카드 위치별 라벨
  customVariables    String @default("{}") // JSON 형태의 커스텀 변수

  // 개별 카드 소개 텍스트 템플릿 (새로 추가)
  cardIntroTemplates String @default("[]") // JSON 형태의 카드별 소개 텍스트 배열

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([isActive])
  @@index([cardCount])
  @@index([cost])
  @@index([order])
}

// 글로벌 타로 설정 모델 (새로 추가)
model TarotGlobalSettings {
  id           String  @id @default(cuid())
  settingKey   String  @unique // 설정 키 (예: "global_system_instruction")
  settingValue String // 설정 값
  description  String  @default("") // 설정 설명
  isActive     Boolean @default(true) // 활성화 여부

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([settingKey])
  @@index([isActive])
}

// 예시: 타로 리딩 기록 모델 (나중에 필요시 확장)
// model TarotReading {
//   id        String   @id @default(cuid())
//   userId    String
//   user      User     @relation(fields: [userId], references: [id])
//   cards     Json     // 선택된 카드 정보
//   query     String?  // 사용자 질문
//   interpretation Json // AI 해석 결과
//   createdAt DateTime @default(now())
// } 
