const jwt = require('jsonwebtoken');

function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // "Bearer TOKEN" 형식

    if (token == null) {
        return res.sendStatus(401); // 토큰이 없음 (Unauthorized)
    }

    jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
        if (err) {
            console.error('JWT verification error:', err.message);
            return res.sendStatus(403); // 토큰이 유효하지 않음 (Forbidden)
        }
        req.user = user; // req 객체에 사용자 정보(payload) 저장 (예: { userId: ... })
        next(); // 다음 미들웨어 또는 라우트 핸들러로 진행
    });
}

module.exports = authenticateToken; 