const axios = require('axios');

async function testFortuneAPI() {
  try {
    console.log('운세 API 테스트 시작...');
    
    // 통계 확인 (이전)
    const statsBefore = await axios.get('http://localhost:3000/api/stats/overview');
    console.log('테스트 전 통계:', statsBefore.data.stats);
    
    // 오늘의 운세 요청 (무료)
    const fortuneRequest = {
      userName: '테스트유저',
      fortuneType: 'todayFiveCard',
      selectedCards: [
        { name: '바보', description: '새로운 시작' },
        { name: '마법사', description: '의지와 창조' },
        { name: '여제', description: '풍요와 창조성' },
        { name: '황제', description: '권위와 안정' },
        { name: '교황', description: '지혜와 가르침' }
      ]
    };
    
    console.log('운세 요청 중...');
    const fortuneResponse = await axios.post('http://localhost:3000/api/fortune/generate', fortuneRequest);
    
    console.log('운세 응답 성공! 해석 길이:', fortuneResponse.data.interpretation?.length || 0);
    
    // 잠시 대기 (로깅 처리 시간)
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 통계 확인 (이후)
    const statsAfter = await axios.get('http://localhost:3000/api/stats/overview');
    console.log('테스트 후 통계:', statsAfter.data.stats);
    
    const readingIncrease = statsAfter.data.stats.tarotReadings - statsBefore.data.stats.tarotReadings;
    console.log(`리딩 횟수 증가: ${readingIncrease}개`);
    
  } catch (error) {
    console.error('테스트 실패:', error.message);
    if (error.response) {
      console.error('응답 상태:', error.response.status);
      console.error('응답 데이터:', error.response.data);
    }
  }
}

testFortuneAPI(); 