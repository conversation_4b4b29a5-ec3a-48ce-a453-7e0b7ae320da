/* style.css */

/* 기본 폰트 및 전체 스타일 */
body {
    font-family: 'Poor Story', 'Cute Font', cursive, sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 30%, #2d4a5a 70%, #3e5b7a 100%);
    color: #f8f9fa;
    line-height: 1.7;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    min-height: 100vh;
}

/* 링크 및 기본 요소 */
a {
    text-decoration: none;
    color: #FF69B4; /* 기본 링크 색상을 귀여운 핑크로 */
    transition: color 0.3s ease;
}
a:hover {
    color: #E91E63; /* 호버 시 좀 더 진한 핑크 */
}

ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

/* 공통 클래스 */
.icon {
    margin-right: 8px;
    vertical-align: middle;
    width: 1.1em; /* Default width for icons */
    height: 1.1em; /* Default height for icons */
    fill: currentColor; /* Inherit color from parent text for monochrome SVGs */
}

/* 공통 아이콘 스타일 (구체적인 svg 태그 대상) */
svg.icon {
    /* You can add more specific styles for SVGs if needed */
    /* For example, if some SVGs need different default colors */
}

.section-title {
    font-family: 'Cute Font', cursive;
    font-size: 2.8em; /* 살짝 더 크게 */
    color: #795548;
    text-align: center;
    margin-top: 70px;
    margin-bottom: 50px;
    text-shadow: 1px 1px 3px rgba(121, 85, 72, 0.2); /* 그림자 부드럽게 */
}

.cta-button {
    display: inline-block;
    padding: 14px 30px; /* 패딩 살짝 늘림 */
    border-radius: 30px; /* 더 둥글게 */
    font-family: 'Cute Font', cursive;
    font-size: 1.3em;
    text-align: center;
    cursor: pointer;
    transition: transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275), box-shadow 0.2s ease; /* 통통 튀는 효과 */
    border: none;
    box-shadow: 3px 3px 7px rgba(0,0,0,0.1);
}

.cta-button:hover {
    transform: translateY(-3px) scale(1.05); /* 호버 시 살짝 커지며 위로 */
    box-shadow: 5px 5px 10px rgba(0,0,0,0.15);
}

/* 헤더 - 접기/펼치기 기능 포함 */
header.main-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 40px;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    position: fixed;
    width: calc(100% - 80px);
    top: 0;
    left: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    min-height: 80px;
}

/* 헤더 접힌 상태 */
header.main-header.collapsed {
    padding: 20px 40px;
    min-height: 50px;
    background: rgba(26, 26, 46, 0.98);
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);
}

/* 헤더 콘텐츠 컨테이너 */
.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    transition: all 0.4s ease;
}

header.main-header .logo h1 a {
    font-family: 'Cute Font', cursive;
    font-size: 2.4em;
    background: linear-gradient(45deg, #ff8a80, #f093fb, #4fc3f7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(255, 138, 128, 0.3);
    transition: all 0.4s ease;
}

/* 접힌 상태에서 로고 크기 조정 */
header.main-header.collapsed .logo h1 a {
    font-size: 1.8em;
}

/* 내비게이션 스타일 */
header.main-header nav.main-nav {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    max-height: 100px;
    overflow: hidden;
}

header.main-header nav.main-nav.hidden {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-20px);
    max-height: 0;
}

header.main-header nav ul {
    display: flex;
    gap: 15px;
    align-items: center;
    transition: all 0.3s ease;
}

header.main-header nav ul li {
    list-style: none;
}

header.main-header nav ul li a {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    font-size: 1em;
    font-weight: 500;
    color: #d1d9e0;
    text-decoration: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    letter-spacing: 0.3px;
}

header.main-header nav ul li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 138, 128, 0.15), rgba(240, 147, 251, 0.15));
    transition: left 0.3s ease;
    z-index: -1;
}

header.main-header nav ul li a:hover::before {
    left: 0;
}

header.main-header nav ul li a:hover {
    color: #ffffff;
    background: rgba(255, 138, 128, 0.2);
    border-color: rgba(255, 138, 128, 0.3);
    transform: translateY(-2px) scale(1.03);
    box-shadow: 0 8px 25px rgba(255, 138, 128, 0.25);
}

header.main-header nav ul li a svg.icon {
    width: 18px;
    height: 18px;
    fill: currentColor;
    transition: all 0.3s ease;
}

header.main-header nav ul li a:hover svg.icon {
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));
    transform: scale(1.1);
}

/* 사용자 메뉴 스타일 */
header.main-header .user-menu {
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

header.main-header .user-menu.hidden {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-20px);
}

header.main-header .user-menu a {
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 0.95em;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.3px;
}

header.main-header .user-menu .login-btn {
    background: rgba(79, 195, 247, 0.15);
    color: #4fc3f7;
    border: 1px solid rgba(79, 195, 247, 0.3);
    backdrop-filter: blur(10px);
}

header.main-header .user-menu .login-btn:hover {
    background: rgba(79, 195, 247, 0.25);
    color: white;
    border-color: rgba(79, 195, 247, 0.5);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 20px rgba(79, 195, 247, 0.3);
}

header.main-header .user-menu .signup-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

header.main-header .user-menu .signup-btn:hover {
    background: linear-gradient(45deg, #764ba2, #f093fb);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(118, 75, 162, 0.4);
}

header.main-header .user-menu .user-info-btn,
header.main-header .user-menu .logout-btn {
    background: rgba(255, 255, 255, 0.1);
    color: #d1d9e0;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

header.main-header .user-menu .user-info-btn:hover,
header.main-header .user-menu .logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.15);
}

header.main-header .user-menu .user-greeting {
    color: #d1d9e0;
    font-size: 0.9em;
    margin-right: 10px;
}

header.main-header .user-menu .user-greeting strong {
    color: #7E57C2;
    font-weight: 600;
}

header.main-header .user-menu .user-greeting span {
    color: #FFB74D;
    font-weight: bold;
}

/* 헤더 토글 버튼 */
.header-toggle-btn {
    position: absolute;
    bottom: -15px;
    right: 30px;
    background: linear-gradient(145deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.9));
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px 20px 20px 20px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    z-index: 1001;
}

.header-toggle-btn:hover {
    background: linear-gradient(145deg, rgba(79, 195, 247, 0.2), rgba(240, 147, 251, 0.2));
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 195, 247, 0.3);
}

.header-toggle-icon {
    color: #4fc3f7;
    transition: all 0.3s ease;
    filter: drop-shadow(0 0 6px rgba(79, 195, 247, 0.3));
}

.header-toggle-btn:hover .header-toggle-icon {
    color: #ffffff;
    transform: scale(1.1);
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
}

/* Enhanced hover effects for icon buttons */
header.main-header .user-menu a svg.icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    transition: all 0.3s ease;
}

header.main-header .user-menu a:hover svg.icon {
    transform: scale(1.1);
    filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.4));
}

/* CSS 변수를 사용하여 동적 높이 관리 */
:root {
    --header-height: 95px;
    --header-collapsed-height: 65px;
}

header.main-header {
    height: var(--header-height);
}

header.main-header.collapsed {
    height: var(--header-collapsed-height);
}

/* 반응형 헤더 개선 */
@media (max-width: 1200px) {
    header.main-header {
        padding: 18px 30px;
        width: calc(100% - 60px);
    }
    
    header.main-header.collapsed {
        padding: 8px 30px;
    }
    
    header.main-header nav ul {
        gap: 10px;
    }
    
    header.main-header nav ul li a {
        padding: 10px 16px;
        font-size: 0.9em;
    }
    
    .header-toggle-btn {
        right: 20px;
    }
}

@media (max-width: 1024px) {
    header.main-header {
        flex-wrap: wrap;
        padding: 15px 25px;
        width: calc(100% - 50px);
    }
    
    header.main-header.collapsed {
        padding: 8px 25px;
    }
    
    header.main-header .logo h1 a {
        font-size: 2em;
    }
    
    header.main-header.collapsed .logo h1 a {
        font-size: 1.6em;
    }
    
    header.main-header nav ul {
        gap: 8px;
        flex-wrap: wrap;
    }
    
    header.main-header nav ul li a {
        padding: 8px 12px;
        font-size: 0.85em;
    }
    
    header.main-header .user-menu {
        gap: 8px;
    }
    
    .header-toggle-btn {
        right: 15px;
    }
}

@media (max-width: 768px) {
    header.main-header {
        flex-direction: column;
        gap: 15px;
        padding: 15px 20px;
        width: calc(100% - 40px);
        --header-height: 140px;
        --header-collapsed-height: 70px;
    }
    
    header.main-header.collapsed {
        padding: 10px 20px;
        gap: 0;
    }
    
    header.main-header nav ul {
        justify-content: center;
        gap: 6px;
    }
    
    header.main-header nav ul li a {
        padding: 8px 10px;
        font-size: 0.8em;
    }
    
    header.main-header .user-menu {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .header-toggle-btn {
        right: 10px;
        bottom: -10px;
    }
}

/* 메인 콘텐츠 영역 - 동적 헤더 높이 대응 */
main {
    padding-top: var(--header-height); /* 헤더 높이 및 경계선 고려 */
    min-height: calc(100vh - var(--header-height));
    transition: padding-top 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* JavaScript로 동적 조정될 클래스 */
.main-collapsed {
    padding-top: var(--header-collapsed-height) !important;
    min-height: calc(100vh - var(--header-collapsed-height)) !important;
}

/* 히어로 섹션 */
.hero {
    /* background-image: url('images/hero_background_cute.png'); */
    background: linear-gradient(135deg, #E1BEE7 0%, #D1C4E9 100%); /* 연보라 그라데이션 */
    background-size: cover;
    background-position: center;
    padding: 100px 20px 120px; /* 패딩 늘려 더 넓게 */
    text-align: center;
    color: white;
    position: relative; /* 애니메이션 요소 배치를 위해 */
    overflow: hidden; /* 내부 애니메이션 요소가 벗어나지 않도록 */
}
/* 히어로 섹션 별 애니메이션 (예시) */
.hero::before, .hero::after {
    content: '✨';
    position: absolute;
    font-size: 2em;
    opacity: 0.7;
    animation: sparkle 5s infinite linear;
}
.hero::before {
    top: 15%; left: 10%;
    animation-delay: 0s;
}
.hero::after {
    top: 60%; right: 15%; font-size: 3em;
    animation-delay: 1s;
}
@keyframes sparkle {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.5); opacity: 1; }
}

.hero h2 {
    font-family: 'Cute Font', cursive;
    font-size: 4em; /* 제목 더 강조 */
    margin-bottom: 25px;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.25);
    line-height: 1.2;
}

.hero p {
    font-size: 1.4em;
    margin-bottom: 40px;
    max-width: 650px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.8;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
}

.hero .primary-cta {
    background: linear-gradient(45deg, #FFAB91, #FF8A65, #FF7043); /* 좀 더 풍부한 복숭아 그라데이션 */
    color: white;
    font-size: 1.6em;
    padding: 18px 40px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

/* 서비스 소개 섹션 */
.services {
    padding: 70px 20px;
    background-color: #FFECB3; /* 연한 노랑/꿀색 배경 */
}

.service-cards-container {
    display: flex;
    justify-content: center;
    gap: 35px;
    flex-wrap: wrap;
}

.service-card {
    background-color: white;
    border-radius: 25px;
    padding: 35px;
    width: 310px;
    box-shadow: 0 8px 20px rgba(180, 141, 87, 0.15); /* 좀 더 부드럽고 넓은 그림자 */
    text-align: center;
    transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), box-shadow 0.3s ease;
    border: 2px dashed #FFD54F; /* 점선 테두리 추가 */
}

.service-card:hover {
    transform: translateY(-12px) rotate(1deg); /* 살짝 기울어지는 효과 추가 */
    box-shadow: 0 12px 25px rgba(180, 141, 87, 0.2);
}

.service-card .service-icon {
    width: 90px;
    height: 90px;
    margin-bottom: 25px;
    /* filter: drop-shadow(3px 3px 5px rgba(0,0,0,0.1)); 아이콘에 그림자 */
}

.service-card h4 {
    font-family: 'Cute Font', cursive;
    font-size: 2em;
    color: #A1887F; /* 연한 갈색 */
    margin-bottom: 15px;
}

.service-card p {
    font-size: 1.05em;
    margin-bottom: 25px;
    color: #78564B;
}

.service-card .cta-button {
    background-color: #FFB74D; /* 주황색 */
    color: #fff;
    font-size: 1.1em;
}

/* AI 마스터 소개 */
.ai-masters {
    padding: 70px 20px;
    background-color: #C5CAE9; /* 연한 인디고/라벤더 배경 */
}

.masters-container {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.master-card {
    background-color: white;
    border-radius: 20px; /* 원형 대신 둥근 사각형으로 변경 */
    width: 250px; /* 너비 살짝 키움 */
    /* height: auto; 콘텐츠에 맞게 자동 조절 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start; /* 위에서부터 정렬 */
    padding: 30px;
    text-align: center;
    box-shadow: 0 8px 20px rgba(92, 107, 192, 0.15);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.master-card:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 30px rgba(92, 107, 192, 0.2);
}

.master-card .master-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin-bottom: 20px;
    border: 4px solid #E8EAF6; /* 연한 라벤더 테두리 */
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.master-card h5 {
    font-family: 'Cute Font', cursive;
    font-size: 1.7em;
    color: #3F51B5; /* 인디고 */
    margin-bottom: 8px;
}

.master-card .master-intro {
    font-size: 0.95em;
    margin-bottom: 20px;
    color: #5C6BC0;
    min-height: 40px; /* 여러 줄 소개를 위해 최소 높이 */
    display: flex; /* 아이콘과 텍스트 정렬을 위해 추가 */
    align-items: center; /* 아이콘과 텍스트 세로 중앙 정렬 */
    justify-content: center; /* AI 마스터 카드 내 텍스트 중앙 정렬 유지 */
}

.master-specialty-icon {
    width: 1.3em; /* 전문 분야 아이콘은 살짝 더 크게 */
    height: 1.3em;
    margin-left: 5px; /* 텍스트와의 간격 */
    fill: #FF69B4; /* 전문 분야 아이콘은 핑크색으로 포인트 */
}

.master-card .small-cta {
    font-size: 1em;
    padding: 10px 20px;
    background-color: #FFEE58; /* 밝은 노랑 */
    color: #5D4037;
    border: 1px solid #FDD835;
}

/* 오늘의 타로 미리보기 */
.daily-preview {
    padding: 70px 20px;
    background-color: #DCEDC8; /* 연한 초록 */
    text-align: center;
}

.daily-tarot-box {
    background: url('images/paper_texture_cute.png'); /* 종이 질감 배경 (실제 이미지 필요) */
    background-color: #FFFFF0; /* 아이보리 백업 */
    padding: 45px;
    border-radius: 25px;
    display: inline-block;
    box-shadow: 0 8px 20px rgba(124, 179, 66, 0.2);
    max-width: 450px;
    border: 3px dashed #AED581; /* 점선 테두리 */
}

.daily-tarot-box #giftBoxIcon {
    width: 120px;
    margin-bottom: 25px;
    cursor: pointer; /* 클릭 가능 암시 */
    transition: transform 0.3s ease;
}
.daily-tarot-box #giftBoxIcon:hover {
    transform: rotate(5deg) scale(1.1);
}

.daily-tarot-box #dailyCardMessage {
    font-size: 1.3em;
    margin-bottom: 25px;
    color: #558B2F; /* 진한 초록 */
    font-weight: bold;
}

.daily-tarot-box #todayCardArea {
    border-top: 2px dotted #8BC34A; /* 구분선 */
    margin-top: 25px;
    padding-top: 25px;
}

.daily-tarot-box #todayCardArea #todayCardImage {
    width: 180px;
    margin-bottom: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
}

.daily-tarot-box #todayCardArea #todayCardInterpretation {
    font-size: 1.4em;
    font-family: 'Cute Font', cursive;
    color: #EF6C00; /* 진한 주황 */
    margin-bottom: 25px;
    line-height: 1.5;
}

.daily-preview .cta-button {
    background-color: #FF80AB; /* 밝은 핑크 */
    color: white;
    font-size: 1.2em;
}

/* 사용자 후기 */
.testimonials {
    padding: 70px 20px;
    background-color: #FCE4EC; /* 아주 연한 핑크 배경 */
}

.testimonial-bubbles {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 25px;
}

.bubble {
    background-color: white;
    padding: 25px 30px;
    border-radius: 20px; /* 말풍선 느낌으로 더 둥글게 */
    box-shadow: 0 5px 15px rgba(233, 30, 99, 0.1);
    max-width: 550px;
    position: relative;
    border: 2px solid #F8BBD0; /* 연핑크 테두리 */
}

.bubble::before { /* 말풍선 꼬리 */
    content: "";
    position: absolute;
    bottom: -10px;
    left: 30px;
    border-width: 15px 15px 0 0;
    border-style: solid;
    border-color: #F8BBD0 transparent transparent transparent; /* 테두리 색과 맞춤 */
    display: block;
    width: 0;
}
.bubble::after { /* 꼬리 안쪽 흰색으로 채우기 */
    content: "";
    position: absolute;
    bottom: -7px; /* 테두리 두께만큼 안으로 */
    left: 32px;  /* 테두리 두께만큼 안으로 */
    border-width: 13px 13px 0 0;
    border-style: solid;
    border-color: white transparent transparent transparent;
    display: block;
    width: 0;
}


.bubble p {
    font-size: 1.05em;
    margin: 0;
    font-style: normal;
    color: #C2185B; /* 진한 핑크 텍스트 */
}
.bubble p::after { /* 인용구 느낌 */
    content: '"'; /* 따옴표 수정 */
    font-size: 1.5em;
    color: #F48FB1;
    margin-left: 5px;
}
.bubble p::before {
    content: '"'; /* 따옴표 수정 */
    font-size: 1.5em;
    color: #F48FB1;
    margin-right: 5px;
}


/* 푸터 */
footer {
    background-color: rgba(30, 15, 60, 0.85); /* 새로운 우주 테마에 맞는 어두운 보라색 배경 */
    color: #e0e0ff; /* 연한 보라색/푸른빛 텍스트 */
    padding: 40px 20px;
    text-align: center;
    border-top: 1px solid rgba(170, 120, 255, 0.3); /* 보라색 테두리, 더 얇고 반투명하게 */
    backdrop-filter: blur(8px); /* 블러 효과 추가 */
    box-shadow: 0 -8px 20px rgba(0, 0, 0, 0.3); /* 상단에 그림자 효과 */
}

.footer-content {
    max-width: 800px;
    margin: 0 auto;
}

.footer-nav {
    margin-bottom: 20px; /* 하단 저작권과 간격 */
}

.footer-nav a {
    margin: 0 12px;
    font-size: 1em;
    color: rgba(170, 160, 255, 0.9); /* 연한 라벤더 색상 */
    transition: color 0.3s ease, text-shadow 0.3s ease;
    position: relative;
}

.footer-nav a:hover {
    color: #FFFFFF;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
}

/* 호버 시 밑줄 효과 추가 */
.footer-nav a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 1px;
    bottom: -2px;
    left: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.footer-nav a:hover::after {
    width: 100%;
}

.copyright {
    font-size: 0.95em;
    margin-top: 20px;
    margin-bottom: 20px;
    opacity: 0.8;
    color: rgba(200, 200, 255, 0.7); /* 더 연한 보라색 */
}

.social-links img {
    width: 35px;
    height: 35px;
    margin: 0 10px;
    opacity: 0.75;
    transition: all 0.3s ease;
    border-radius: 50%; /* 아이콘도 둥글게 */
    background-color: rgba(100, 80, 200, 0.15); /* 보라색 배경으로 변경 */
    padding: 5px;
    filter: drop-shadow(0 0 3px rgba(170, 120, 255, 0.5)); /* 글로우 효과 */
}

.social-links img:hover {
    opacity: 1;
    transform: scale(1.15);
    background-color: rgba(120, 100, 230, 0.3); /* 호버 시 더 강한 배경색 */
    filter: drop-shadow(0 0 5px rgba(200, 150, 255, 0.8));
}

.footer-mascot {
    width: 90px;
    margin-top: 25px;
    opacity: 0.8;
    filter: drop-shadow(0 0 8px rgba(150, 100, 255, 0.7)); /* 보라색 그림자로 변경 */
}

/* Modal Styles - Mystical Fortune Theme */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(8px);
    transition: all 0.3s ease-out;
}

.modal.modal-open {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: modalFadeIn 0.4s ease-out;
}

/* Modal cosmic background container */
.modal-cosmic-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

/* Celestial pulse animation for stars and cosmic elements */
@keyframes celestialPulse {
    0% { 
        transform: scale(1); 
        box-shadow: 0 0 15px rgba(255,255,255,0.2), 0 0 30px rgba(255,255,255,0.1); 
        opacity: 0.7;
    }
    50% { 
        transform: scale(1.05); 
        box-shadow: 0 0 25px rgba(255,255,255,0.3), 0 0 45px rgba(255,255,255,0.2); 
        opacity: 1;
    }
    100% { 
        transform: scale(1); 
        box-shadow: 0 0 15px rgba(255,255,255,0.2), 0 0 30px rgba(255,255,255,0.1); 
        opacity: 0.7;
    }
}

@keyframes modalFadeIn {
    from { 
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to { 
        opacity: 1;
        backdrop-filter: blur(8px);
    }
}

.modal-content {
    background: linear-gradient(135deg, rgba(20, 10, 40, 0.95) 0%, rgba(40, 20, 80, 0.95) 100%);
    backdrop-filter: blur(15px);
    margin: auto;
    padding: 40px 45px;
    border: 1px solid rgba(255, 235, 59, 0.3);
    border-radius: 20px;
    width: 90%;
    max-width: 450px;
    box-shadow: 
        0 0 30px rgba(255, 235, 59, 0.2),
        0 15px 50px rgba(0, 0, 0, 0.5),
        inset 0 1px 2px rgba(255, 255, 255, 0.1);
    position: relative;
    text-align: center;
    color: #e0e0e0;
    animation: modalSlideIn 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
    overflow: hidden;
    z-index: 10; /* Ensure modal content appears above cosmic background */
}

/* Mystical background pattern for modal */
.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 30%, rgba(255, 235, 59, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(160, 90, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(255, 180, 120, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

@keyframes modalSlideIn {
    from { 
        transform: translateY(-50px) scale(0.95); 
        opacity: 0; 
    }
    to { 
        transform: translateY(0) scale(1); 
        opacity: 1; 
    }
}

.close-btn {
    position: absolute;
    top: 15px;
    right: 20px;
    color: #ffeb3b;
    font-size: 32px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 0 0 10px rgba(255, 235, 59, 0.5);
    z-index: 1001;
}

.close-btn:hover,
.close-btn:focus {
    color: #fff59d;
    text-decoration: none;
    transform: rotate(90deg) scale(1.2);
    text-shadow: 0 0 15px rgba(255, 235, 59, 0.8);
}

.modal-content h3 {
    font-family: 'Cute Font', 'Poor Story', cursive;
    font-size: 2.5em;
    color: #fff59d;
    margin-bottom: 30px;
    text-shadow: 0 0 15px rgba(255, 235, 59, 0.4);
    letter-spacing: 1px;
}

.modal-content h4 {
    font-family: 'Poor Story', cursive;
    font-size: 1.4em;
    color: #e1e6ff;
    margin: 25px 0 15px 0;
    text-align: left;
}

/* Form Group Styles */
.form-group {
    margin-bottom: 20px;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-family: 'Poor Story', cursive;
    font-size: 1.1em;
    color: #c0c8ff;
    font-weight: 500;
}

.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="text"] {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid rgba(255, 235, 59, 0.3);
    border-radius: 12px;
    font-family: 'Poor Story', cursive;
    font-size: 1.1em;
    background: rgba(30, 15, 60, 0.5);
    color: #e0e0e0;
    transition: all 0.3s ease;
    box-sizing: border-box;
    backdrop-filter: blur(5px);
}

.form-group input[type="email"]:focus,
.form-group input[type="password"]:focus,
.form-group input[type="text"]:focus {
    border-color: #ffeb3b;
    box-shadow: 
        0 0 15px rgba(255, 235, 59, 0.3),
        inset 0 1px 3px rgba(0, 0, 0, 0.2);
    outline: none;
    background: rgba(30, 15, 60, 0.7);
}

.form-group input[type="email"]::placeholder,
.form-group input[type="password"]::placeholder,
.form-group input[type="text"]::placeholder {
    color: rgba(192, 200, 255, 0.6);
}

.form-group input[readonly] {
    background: rgba(50, 30, 80, 0.6);
    color: #a0a0a0;
    cursor: not-allowed;
}

.auth-button {
    width: 100%;
    padding: 16px;
    font-size: 1.3em;
    font-family: 'Poor Story', cursive;
    font-weight: bold;
    background: linear-gradient(145deg, #ffeb3b, #ff9800);
    color: #2E2E2E;
    border: none;
    border-radius: 15px;
    margin: 20px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow: 
        0 4px 15px rgba(255, 235, 59, 0.3),
        0 2px 5px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.5px;
}

.auth-button:hover {
    background: linear-gradient(145deg, #fff59d, #ffb74d);
    transform: translateY(-2px);
    box-shadow: 
        0 6px 20px rgba(255, 235, 59, 0.4),
        0 4px 8px rgba(0, 0, 0, 0.3);
}

.auth-button:active {
    transform: translateY(0);
    box-shadow: 
        0 2px 10px rgba(255, 235, 59, 0.3),
        0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Social Login Buttons */
.social-login {
    margin: 30px 0 20px 0;
    padding: 20px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.social-login p {
    font-size: 1em;
    color: #c0c8ff;
    margin-bottom: 15px;
    font-family: 'Poor Story', cursive;
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 14px;
    border-radius: 12px;
    font-family: 'Poor Story', cursive;
    font-size: 1.1em;
    margin-bottom: 10px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.social-btn img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
}

.google-btn {
    background: linear-gradient(145deg, rgba(255, 100, 100, 0.8), rgba(255, 150, 150, 0.8));
    color: #fff;
    border-color: rgba(255, 100, 100, 0.3);
}

.google-btn:hover {
    background: linear-gradient(145deg, rgba(255, 120, 120, 0.9), rgba(255, 170, 170, 0.9));
    border-color: rgba(255, 100, 100, 0.5);
}

.kakao-btn {
    background: linear-gradient(145deg, rgba(255, 235, 59, 0.8), rgba(255, 193, 7, 0.8));
    color: #2E2E2E;
    border-color: rgba(255, 235, 59, 0.3);
}

.kakao-btn:hover {
    background: linear-gradient(145deg, rgba(255, 245, 89, 0.9), rgba(255, 213, 47, 0.9));
    border-color: rgba(255, 235, 59, 0.5);
}

.switch-form {
    margin-top: 25px;
    font-size: 1em;
    color: #c0c8ff;
    font-family: 'Poor Story', cursive;
}

.switch-form a {
    color: #ffeb3b;
    font-weight: bold;
    text-decoration: none;
    transition: all 0.3s ease;
    text-shadow: 0 0 5px rgba(255, 235, 59, 0.3);
}

.switch-form a:hover {
    color: #fff59d;
    text-decoration: underline;
    text-shadow: 0 0 8px rgba(255, 235, 59, 0.5);
}

/* Separator line for my info modal */
hr {
    border: none;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 235, 59, 0.3), transparent);
    margin: 25px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modal-content {
        padding: 30px 25px;
        width: 95%;
        margin: 10px;
    }
    
    .modal-content h3 {
        font-size: 2.2em;
    }
    
    .auth-button {
        padding: 14px;
        font-size: 1.2em;
    }
}
