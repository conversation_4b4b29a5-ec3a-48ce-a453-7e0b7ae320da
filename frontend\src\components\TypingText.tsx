import React, { useState, useEffect } from 'react';

interface TypingTextProps {
  text: string;
  speed?: number; // Milliseconds per character
  onComplete?: () => void; // Callback when typing finishes
  startDelay?: number; // Milliseconds before starting
  className?: string; // Optional class name for styling
  useHtml?: boolean; // Whether to render the text as HTML
}

const TypingText: React.FC<TypingTextProps> = ({
  text,
  speed = 70, // Default speed
  onComplete,
  startDelay = 0,
  className = '',
  useHtml = false
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isStarted, setIsStarted] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  
  // Process text to convert newlines to <br /> tags when using HTML
  const processedText = useHtml ? text.replace(/\n/g, '<br />') : text;

  useEffect(() => {
    if (startDelay > 0) {
      const startTimer = setTimeout(() => {
        setIsStarted(true);
      }, startDelay);
      return () => clearTimeout(startTimer);
    } else {
      setIsStarted(true);
    }
  }, [startDelay]);

  useEffect(() => {
    if (!isStarted || currentIndex >= processedText.length) {
      if (currentIndex >= processedText.length) {
        setIsComplete(true);
        if (onComplete) {
          onComplete();
        }
      }
      return;
    }

    // HTML 태그를 처리하는 경우, 태그 내부를 빠르게 처리
    if (useHtml && processedText[currentIndex] === '<') {
      let tagEndIndex = processedText.indexOf('>', currentIndex);
      if (tagEndIndex !== -1) {
        const timer = setTimeout(() => {
          setDisplayedText((prev) => prev + processedText.substring(currentIndex, tagEndIndex + 1));
          setCurrentIndex(tagEndIndex + 1);
        }, 1); // 태그는 매우 빠르게 렌더링
        return () => clearTimeout(timer);
      }
    }

    const timer = setTimeout(() => {
      setDisplayedText((prev) => prev + processedText[currentIndex]);
      setCurrentIndex((prev) => prev + 1);
    }, speed);

    return () => clearTimeout(timer);
  }, [processedText, speed, currentIndex, onComplete, isStarted, useHtml]);

  // Reset when text changes
  useEffect(() => {
    setDisplayedText('');
    setCurrentIndex(0);
    setIsStarted(startDelay <= 0);
    setIsComplete(false);
  }, [text, startDelay]);

  // HTML 렌더링인지 일반 텍스트인지에 따라 다르게 반환
  if (useHtml) {
    return (
      <span 
        className={className} 
        dangerouslySetInnerHTML={{ __html: displayedText + (isComplete ? '' : '<span class="typing-cursor">|</span>') }}
      />
    );
  }

  // 일반 텍스트 렌더링 (handle newlines with CSS white-space)
  return <span className={className} style={{ whiteSpace: 'pre-line' }}>{displayedText}</span>;
};

export default TypingText; 