/* Manager Page Styles */
.manager-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #2c1810 0%, #1a0f1a 50%, #0f0520 100%);
  color: #e0e0e0;
  font-family: 'Nanum Gothic', 'Malgun Gothic', sans-serif;
  padding: 20px;
}

.manager-header {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.manager-header h1 {
  font-size: 2.5rem;
  margin: 0 0 20px 0;
  color: #d8bfd8;
  text-shadow: 0 0 15px rgba(216, 191, 216, 0.5);
  font-family: 'Gaegu', 'Cute Font', cursive;
}

.manager-nav {
  display: flex;
  gap: 15px;
}

.manager-nav button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  background: rgba(138, 43, 226, 0.2);
  color: #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  border: 1px solid rgba(138, 43, 226, 0.3);
}

.manager-nav button:hover:not(:disabled) {
  background: rgba(138, 43, 226, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(138, 43, 226, 0.3);
}

.manager-nav button.active {
  background: linear-gradient(145deg, #8a2be2, #4b0082);
  box-shadow: 0 5px 15px rgba(138, 43, 226, 0.4);
}

.manager-nav button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.manager-content {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 15px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Spread List Styles */
.spread-list {
  width: 100%;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.list-header h2 {
  font-size: 1.8rem;
  color: #d8bfd8;
  margin: 0;
}

.create-btn {
  padding: 12px 24px;
  background: linear-gradient(145deg, #daa520, #b8860b);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(218, 165, 32, 0.3);
}

.create-btn:hover {
  background: linear-gradient(145deg, #c7921d, #a0740a);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(218, 165, 32, 0.4);
}

.spread-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 25px;
}

.spread-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.spread-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.spread-preview {
  height: 120px;
  background: rgba(20, 5, 40, 0.6);
  border-radius: 8px;
  margin-bottom: 15px;
  position: relative;
  border: 1px solid rgba(120, 80, 180, 0.3);
}

.spread-visual-mini {
  width: 100%;
  height: 100%;
  position: relative;
}

.mini-dot {
  position: absolute;
  width: 8px;
  height: 12px;
  background-color: rgba(220, 180, 255, 0.9);
  border-radius: 2px;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 8px rgba(180, 120, 255, 0.6);
}

.spread-info h3 {
  font-size: 1.3rem;
  margin: 0 0 8px 0;
  color: #fff;
}

.spread-info p {
  color: #b0b0b0;
  margin: 0 0 15px 0;
}

.discount-badge {
  background: linear-gradient(145deg, #ff6b6b, #ee5a52);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
  margin-left: 10px;
}

.spread-actions {  display: flex;  gap: 8px;  align-items: center;  flex-wrap: wrap;}.order-controls {  display: flex;  flex-direction: column;  gap: 2px;  margin-right: 8px;}.order-btn {  background: rgba(255, 215, 0, 0.2);  border: 1px solid rgba(255, 215, 0, 0.5);  color: #ffd700;  width: 20px;  height: 18px;  font-size: 10px;  border-radius: 3px;  cursor: pointer;  transition: all 0.3s ease;  display: flex;  align-items: center;  justify-content: center;  line-height: 1;}.order-btn:hover:not(:disabled) {  background: rgba(255, 215, 0, 0.4);  border-color: #ffd700;  box-shadow: 0 2px 5px rgba(255, 215, 0, 0.3);}.order-btn:disabled {  opacity: 0.3;  cursor: not-allowed;}

.spread-actions button {
  padding: 6px 12px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.spread-actions button:first-child {
  background: rgba(138, 43, 226, 0.3);
  color: #e0e0e0;
  border: 1px solid rgba(138, 43, 226, 0.5);
}

.spread-actions button:first-child:hover {
  background: rgba(138, 43, 226, 0.5);
}

.layout-btn {
  background: rgba(34, 139, 34, 0.3) !important;
  color: #e0e0e0 !important;
  border: 1px solid rgba(34, 139, 34, 0.5) !important;
}

.layout-btn:hover {  background: rgba(34, 139, 34, 0.5) !important;}.prompt-btn {  background: rgba(255, 140, 0, 0.3) !important;  color: #e0e0e0 !important;  border: 1px solid rgba(255, 140, 0, 0.5) !important;}.prompt-btn:hover {  background: rgba(255, 140, 0, 0.5) !important;}

.delete-btn {
  background: rgba(220, 20, 60, 0.3) !important;
  color: #e0e0e0 !important;
  border: 1px solid rgba(220, 20, 60, 0.5) !important;
}

.delete-btn:hover {
  background: rgba(220, 20, 60, 0.5) !important;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  margin-left: auto;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.2);
  transition: 0.4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #8a2be2;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Editor Styles */
.spread-editor {
  width: 100%;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.editor-header h2 {
  font-size: 1.8rem;
  color: #d8bfd8;
  margin: 0;
}

.editor-actions {
  display: flex;
  gap: 12px;
}

.save-btn {
  padding: 10px 20px;
  background: linear-gradient(145deg, #32cd32, #228b22);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.save-btn:hover {
  background: linear-gradient(145deg, #2eb82e, #1e7e1e);
  transform: translateY(-2px);
}

.cancel-btn {
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.1);
  color: #e0e0e0;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.15);
}

.editor-form {
  max-width: 800px;
}

.form-section {
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.form-section h3 {
  font-size: 1.3rem;
  color: #d8bfd8;
  margin: 0 0 20px 0;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-size: 0.9rem;
  color: #b0b0b0;
  margin-bottom: 5px;
  font-weight: 600;
}

.form-group input,
.form-group textarea {
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.2);
  color: #e0e0e0;
  font-family: inherit;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: rgba(138, 43, 226, 0.5);
  box-shadow: 0 0 10px rgba(138, 43, 226, 0.3);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* Layout Editor Styles */
.layout-editor {
  width: 100%;
}

.layout-workspace {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  height: 600px;
}

.layout-canvas {
  background: rgba(20, 5, 40, 0.6);
  border-radius: 12px;
  border: 2px solid rgba(120, 80, 180, 0.3);
  position: relative;
  overflow: hidden;
}

.canvas-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.position-marker {
  position: absolute;
  width: 30px;
  height: 45px;
  background: linear-gradient(145deg, #daa520, #b8860b);
  border-radius: 6px;
  cursor: move;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(-50%, -50%);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.position-marker:hover {  transform: translate(-50%, -50%) scale(1.1);  box-shadow: 0 6px 15px rgba(218, 165, 32, 0.5);  border-color: rgba(255, 255, 255, 0.4);}.position-marker.selected {  border-color: #ff69b4;  background: linear-gradient(145deg, #ff69b4, #e60077);  box-shadow: 0 0 15px rgba(255, 105, 180, 0.8);}

.position-number {
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.position-controls {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow-y: auto;
}

.position-controls h3 {
  font-size: 1.2rem;
  color: #d8bfd8;
  margin: 0 0 20px 0;
}

.position-control {
  margin-bottom: 15px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.position-control label {
  font-size: 0.9rem;
  color: #b0b0b0;
  font-weight: 600;
  margin-bottom: 8px;
  display: block;
}

.control-inputs {
  display: flex;
  gap: 8px;
}

.control-inputs input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.2);
  color: #e0e0e0;
  font-size: 0.8rem;
}

.auto-layout-btn {
  background: linear-gradient(145deg, #ff8c00, #ff7f00) !important;
  color: white !important;
  border: none !important;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.auto-layout-btn:hover {
  background: linear-gradient(145deg, #e68200, #e67300) !important;
  transform: translateY(-2px);
}

.close-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #e0e0e0 !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {  background: rgba(255, 255, 255, 0.15) !important;}

/* 회전 및 선택 컨트롤 스타일 */
.selected-card-controls {
  background: rgba(255, 105, 180, 0.1);
  border: 1px solid rgba(255, 105, 180, 0.3);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.selected-card-controls h4 {
  color: #ff69b4;
  margin: 0 0 15px 0;
  font-size: 1.1rem;
}

.control-panel {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.position-inputs {
  display: flex;
  gap: 10px;
}

.input-group {
  flex: 1;
}

.input-group label {
  display: block;
  font-size: 0.8rem;
  color: #b0b0b0;
  margin-bottom: 5px;
}

.input-group input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.2);
  color: #e0e0e0;
  font-size: 0.9rem;
}

.rotation-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rotation-controls label {
  font-size: 0.9rem;
  color: #b0b0b0;
  font-weight: 600;
}

.rotation-buttons {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.rotation-buttons button {
  padding: 5px 10px;
  border: 1px solid rgba(255, 105, 180, 0.5);
  border-radius: 4px;
  background: rgba(255, 105, 180, 0.2);
  color: #ff69b4;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.rotation-buttons button:hover {
  background: rgba(255, 105, 180, 0.4);
  border-color: #ff69b4;
}

.deselect-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #e0e0e0;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
  transition: all 0.3s ease;
}

.deselect-btn:hover {
  background: rgba(255, 255, 255, 0.15);
}

.all-positions {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.all-positions h4 {
  color: #d8bfd8;
  margin: 0 0 10px 0;
  font-size: 1rem;
}

.position-summary {
  padding: 5px 10px;
  margin-bottom: 5px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.position-summary:hover {
  background: rgba(255, 255, 255, 0.05);
}

.position-summary span {
  color: #b0b0b0;
  font-size: 0.9rem;
}

.advanced-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.toggle-advanced-btn {
  background: rgba(138, 43, 226, 0.2);
  border: 1px solid rgba(138, 43, 226, 0.5);
  color: #8a2be2;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.toggle-advanced-btn:hover {
  background: rgba(138, 43, 226, 0.4);
  border-color: #8a2be2;
}

.advanced-info {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
}

.advanced-info p {
  margin: 0 0 8px 0;
  color: #e0e0e0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.advanced-info p:last-child {
  margin-bottom: 0;
  color: #ffc107;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .manager-page {
    padding: 10px;
  }
  
  .manager-header {
    padding: 20px;
  }
  
  .manager-header h1 {
    font-size: 2rem;
  }
  
  .manager-nav {
    flex-direction: column;
    gap: 10px;
  }
  
  .spread-grid {
    grid-template-columns: 1fr;
  }
  
  .layout-workspace {
    grid-template-columns: 1fr;
    grid-template-rows: 400px auto;
    height: auto;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .editor-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .list-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .spread-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .spread-actions button {
    width: 100%;
    margin-bottom: 5px;
  }
  
    .toggle-switch {    margin-left: 0;    margin-top: 10px;  }} /* Prompt Editor Styles */.prompt-editor {  width: 100%;}.prompt-form {  margin-top: 20px;}.error-messages {  background: rgba(220, 53, 69, 0.1);  border: 1px solid rgba(220, 53, 69, 0.3);  border-radius: 8px;  padding: 15px;  margin-bottom: 20px;}.error-message {  color: #ff6b6b;  font-size: 0.9rem;  margin-bottom: 5px;}.error-message:last-child {  margin-bottom: 0;}.field-description {  color: #b0b0b0;  font-size: 0.9rem;  margin-bottom: 10px;  line-height: 1.4;}.prompt-textarea,.instruction-textarea,.json-textarea {  width: 100%;  background: rgba(255, 255, 255, 0.05);  border: 1px solid rgba(255, 255, 255, 0.2);  border-radius: 8px;  color: #e0e0e0;  padding: 15px;  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;  font-size: 0.9rem;  line-height: 1.5;  resize: vertical;  transition: all 0.3s ease;}.prompt-textarea:focus,.instruction-textarea:focus,.json-textarea:focus {  outline: none;  border-color: rgba(138, 43, 226, 0.5);  background: rgba(255, 255, 255, 0.08);  box-shadow: 0 0 10px rgba(138, 43, 226, 0.2);}.json-textarea.error {  border-color: rgba(220, 53, 69, 0.5);  background: rgba(220, 53, 69, 0.05);}.improve-btn {  background: linear-gradient(145deg, #17a2b8, #138496) !important;  color: white !important;  border: none !important;  padding: 10px 20px;  border-radius: 6px;  font-weight: 600;  transition: all 0.3s ease;  box-shadow: 0 4px 10px rgba(23, 162, 184, 0.3);}.improve-btn:hover {  background: linear-gradient(145deg, #138496, #117a8b) !important;  transform: translateY(-2px);  box-shadow: 0 6px 15px rgba(23, 162, 184, 0.4);}.variables-help {  background: rgba(255, 255, 255, 0.05);  border-radius: 8px;  padding: 20px;  border: 1px solid rgba(255, 255, 255, 0.1);}.variable-item {  display: flex;  align-items: center;  margin-bottom: 10px;  padding: 8px 0;  border-bottom: 1px solid rgba(255, 255, 255, 0.1);}.variable-item:last-child {  border-bottom: none;  margin-bottom: 0;}.variable-item code {  background: rgba(138, 43, 226, 0.2);  color: #d8bfd8;  padding: 4px 8px;  border-radius: 4px;  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;  font-size: 0.85rem;  margin-right: 15px;  min-width: 120px;  border: 1px solid rgba(138, 43, 226, 0.3);}.variable-item span {  color: #b0b0b0;  font-size: 0.9rem;}/* Responsive adjustments for prompt editor */@media (max-width: 768px) {  .prompt-textarea,  .instruction-textarea,  .json-textarea {    font-size: 0.8rem;    padding: 12px;  }  .variables-help {    padding: 15px;  }  .variable-item {    flex-direction: column;    align-items: flex-start;    gap: 5px;  }  .variable-item code {    min-width: auto;    margin-right: 0;  }}

.card-intro-btn {
  background: rgba(75, 0, 130, 0.3) !important;
  color: #e0e0e0 !important;
  border: 1px solid rgba(75, 0, 130, 0.5) !important;
}

.card-intro-btn:hover {
  background: rgba(75, 0, 130, 0.5) !important;
}

/* Card Introduction Editor Styles */
.card-intro-editor {
  width: 100%;
}

.card-intro-form {
  margin-top: 20px;
}

.template-help {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.template-help h4 {
  color: #d8bfd8;
  margin: 0 0 15px 0;
  font-size: 1.1rem;
}

.template-help ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.template-help li {
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #b0b0b0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.template-help li:last-child {
  border-bottom: none;
}

.template-help li strong {
  color: #ff69b4;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  background: rgba(255, 105, 180, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  margin-right: 10px;
}

/* Global Settings Styles */
.global-settings {
  width: 100%;
}

.global-settings-form {
  margin-top: 20px;
}

.global-settings .form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.global-settings .form-group select {
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.2);
  color: #e0e0e0;
  font-family: inherit;
  transition: all 0.3s ease;
  width: 100%;
}

.global-settings .form-group select:focus {
  outline: none;
  border-color: rgba(138, 43, 226, 0.5);
  box-shadow: 0 0 10px rgba(138, 43, 226, 0.3);
}

.global-settings .form-group select option {
  background: #2c1810;
  color: #e0e0e0;
}

/* Enhanced Field Descriptions */
.field-description {
  color: #b0b0b0;
  font-size: 0.9rem;
  margin-bottom: 10px;
  line-height: 1.5;
  padding: 10px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  border-left: 3px solid rgba(138, 43, 226, 0.4);
}

.field-description strong {
  color: #d8bfd8;
  font-weight: 600;
}

/* Improved Button Styles */
.manager-nav button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.05) !important;
  color: rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

/* Enhanced Textarea for Templates */
.json-textarea {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #e0e0e0;
  padding: 15px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  resize: vertical;
  transition: all 0.3s ease;
  min-height: 200px;
}

.json-textarea:focus {
  outline: none;
  border-color: rgba(138, 43, 226, 0.5);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 10px rgba(138, 43, 226, 0.2);
}

.json-textarea.error {
  border-color: rgba(220, 53, 69, 0.5);
  background: rgba(220, 53, 69, 0.05);
}

.json-textarea::placeholder {
  color: rgba(255, 255, 255, 0.4);
  font-style: italic;
}

/* Spread Info Preview Styles */
.spread-info-preview {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 15px;
}

.info-item {
  margin-bottom: 8px;
  color: #b0b0b0;
  font-size: 0.9rem;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item strong {
  color: #d8bfd8;
  margin-right: 8px;
}

/* 새로운 카드 소개 템플릿 섹션 스타일 */
.label-with-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.mini-ai-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mini-ai-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* Rich Textarea 스타일 */
.rich-textarea {
  width: 100%;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #e0e0e0;
  padding: 15px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 0.95rem;
  line-height: 1.6;
  resize: vertical;
  transition: all 0.3s ease;
  min-height: 100px;
}

.rich-textarea:focus {
  outline: none;
  border-color: rgba(138, 43, 226, 0.5);
  background: rgba(255, 255, 255, 0.12);
  box-shadow: 0 0 10px rgba(138, 43, 226, 0.2);
}

.rich-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

/* 할인 기간 설정 스타일 */
.discount-period {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 12px;
  padding: 20px;
  margin-top: 15px;
}

.discount-period h4 {
  margin: 0 0 10px 0;
  color: #ffc107;
  font-size: 1rem;
}

.date-range {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.date-field {
  display: flex;
  flex-direction: column;
}

.date-field label {
  margin-bottom: 5px;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
}

.date-field input[type="datetime-local"] {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  color: #e0e0e0;
  padding: 8px 12px;
  font-size: 0.9rem;
}

.date-field input[type="datetime-local"]:focus {
  outline: none;
  border-color: rgba(255, 193, 7, 0.5);
  box-shadow: 0 0 5px rgba(255, 193, 7, 0.3);
}

/* 새로운 프로 레이아웃 편집기 스타일 */
.layout-editor-pro {
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.layout-editor-pro .editor-header {
  background: rgba(0, 0, 0, 0.3);
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.editor-mode-tabs {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.editor-mode-tabs button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background: rgba(138, 43, 226, 0.2);
  color: #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(138, 43, 226, 0.3);
}

.editor-mode-tabs button.active {
  background: linear-gradient(145deg, #8a2be2, #4b0082);
  box-shadow: 0 3px 10px rgba(138, 43, 226, 0.4);
}

.layout-toolbar {
  background: rgba(0, 0, 0, 0.2);
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 30px;
  align-items: center;
  flex-wrap: wrap;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toolbar-section label {
  color: #b0b0b0;
  font-size: 0.9rem;
}

.toolbar-section input[type="checkbox"] {
  transform: scale(1.2);
}

.grid-size-slider {
  width: 100px;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  outline: none;
}

.undo-btn, .redo-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 5px;
  background: rgba(34, 139, 34, 0.3);
  color: #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(34, 139, 34, 0.5);
}

.undo-btn:hover:not(:disabled), .redo-btn:hover:not(:disabled) {
  background: rgba(34, 139, 34, 0.5);
}

.undo-btn:disabled, .redo-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.layout-workspace-pro {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
  overflow: hidden;
}

.left-panel {
  width: 250px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow-y: auto;
}

.left-panel h3 {
  color: #d8bfd8;
  font-size: 1.1rem;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 8px;
}

.quick-arrangements {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 25px;
}

.arrangement-btn {
  padding: 10px 15px;
  border: none;
  border-radius: 6px;
  background: rgba(138, 43, 226, 0.2);
  color: #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(138, 43, 226, 0.3);
  text-align: left;
}

.arrangement-btn:hover {
  background: rgba(138, 43, 226, 0.4);
  transform: translateX(5px);
}

.ai-tools {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 25px;
}

.ai-btn {
  padding: 12px 15px;
  border: none;
  border-radius: 6px;
  background: linear-gradient(145deg, #17a2b8, #138496);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(23, 162, 184, 0.5);
  text-align: left;
}

.ai-btn:hover {
  background: linear-gradient(145deg, #138496, #117a8b);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(23, 162, 184, 0.3);
}

.preset-manager {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.preset-name-input {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
}

.save-preset-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 5px;
  background: rgba(218, 165, 32, 0.3);
  color: #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(218, 165, 32, 0.5);
}

.save-preset-btn:hover {
  background: rgba(218, 165, 32, 0.5);
}

.preset-list {
  max-height: 150px;
  overflow-y: auto;
}

.preset-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  margin-bottom: 5px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.preset-item span {
  cursor: pointer;
  flex: 1;
  color: #e0e0e0;
}

.preset-item span:hover {
  color: #d8bfd8;
}

.delete-preset-btn {
  background: rgba(220, 20, 60, 0.3);
  border: none;
  border-radius: 3px;
  color: #ff6b6b;
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-preset-btn:hover {
  background: rgba(220, 20, 60, 0.5);
}

.center-canvas {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.canvas-header {
  background: rgba(0, 0, 0, 0.3);
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.canvas-info {
  display: flex;
  gap: 20px;
  color: #b0b0b0;
  font-size: 0.9rem;
}

.canvas-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.canvas-controls input[type="range"] {
  width: 100px;
}

.size-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.05);
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.size-input-group label {
  color: #b0b0b0;
  font-size: 0.9rem;
  font-weight: 500;
  min-width: 35px;
}

.size-input {
  width: 70px;
  padding: 4px 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.08);
  color: #e0e0e0;
  font-size: 0.9rem;
  text-align: center;
}

.size-input:focus {
  outline: none;
  border-color: rgba(138, 43, 226, 0.5);
  background: rgba(255, 255, 255, 0.12);
  box-shadow: 0 0 5px rgba(138, 43, 226, 0.3);
}

.size-input-group span {
  color: #b0b0b0;
  font-size: 0.85rem;
}

.canvas-container {
  flex: 1;
  position: relative;
  margin: 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(20, 5, 40, 0.4);
  overflow: hidden;
}

.canvas-container.show-grid {
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: var(--grid-size, 20px) var(--grid-size, 20px);
}

.canvas-container.show-rulers::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 10;
}

.ruler-horizontal, .ruler-vertical {
  position: absolute;
  background: rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.ruler-horizontal {
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.ruler-vertical {
  top: 0;
  bottom: 0;
  left: 0;
  width: 20px;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.position-marker-pro {
  position: absolute;
  width: 40px;
  height: 60px;
  background: linear-gradient(145deg, #daa520, #b8860b);
  border-radius: 6px;
  cursor: move;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  user-select: none;
}

.position-marker-pro:hover {
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 0 6px 15px rgba(218, 165, 32, 0.5);
  border-color: rgba(255, 255, 255, 0.5);
}

.position-marker-pro.selected {
  border-color: #ff69b4;
  background: linear-gradient(145deg, #ff69b4, #e60077);
  box-shadow: 0 0 20px rgba(255, 105, 180, 0.8);
}

.position-marker-pro.multi-selected {
  border-color: #00ffff;
  background: linear-gradient(145deg, #00ffff, #0080ff);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.6);
}

.position-number {
  color: white;
  font-weight: bold;
  font-size: 0.8rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.position-controls-mini {
  display: flex;
  gap: 2px;
  margin-top: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.position-marker-pro:hover .position-controls-mini {
  opacity: 1;
}

.position-controls-mini button {
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 2px;
  color: white;
  width: 16px;
  height: 16px;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.position-controls-mini button:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.right-panel {
  width: 280px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow-y: auto;
}

.right-panel h3 {
  color: #d8bfd8;
  font-size: 1.1rem;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 8px;
}

.property-editor {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.property-editor h4 {
  color: #fff;
  margin-bottom: 15px;
  font-size: 1rem;
}

.property-group {
  margin-bottom: 15px;
}

.property-group label {
  display: block;
  color: #b0b0b0;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.property-group input,
.property-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
  font-size: 0.9rem;
}

.property-group input:focus,
.property-group textarea:focus {
  outline: none;
  border-color: rgba(138, 43, 226, 0.5);
  background: rgba(255, 255, 255, 0.08);
}

.transform-input {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.8rem;
  resize: vertical;
}

.multi-selection-controls {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid rgba(0, 255, 255, 0.3);
}

.multi-selection-controls h4 {
  color: #00ffff;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.multi-selection-controls button {
  width: 100%;
  padding: 8px 12px;
  margin-bottom: 8px;
  border: none;
  border-radius: 5px;
  background: rgba(0, 255, 255, 0.2);
  color: #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 255, 255, 0.3);
}

.multi-selection-controls button:hover {
  background: rgba(0, 255, 255, 0.4);
}

.all-positions-pro {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.all-positions-pro h4 {
  color: #d8bfd8;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.position-list {
  max-height: 200px;
  overflow-y: auto;
}

.position-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  margin-bottom: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.position-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.position-item.selected {
  background: rgba(138, 43, 226, 0.3);
  border-color: rgba(138, 43, 226, 0.5);
}

.position-label {
  color: #e0e0e0;
  font-weight: 500;
}

.position-coords {
  color: #b0b0b0;
  font-size: 0.8rem;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.layout-actions {
  background: rgba(0, 0, 0, 0.3);
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 15px;
  justify-content: center;
}

.save-btn-large {
  padding: 12px 30px;
  border: none;
  border-radius: 8px;
  background: linear-gradient(145deg, #daa520, #b8860b);
  color: white;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(218, 165, 32, 0.3);
}

.save-btn-large:hover {
  background: linear-gradient(145deg, #c7921d, #a0740a);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(218, 165, 32, 0.4);
}

.close-btn-large {
  padding: 12px 30px;
  border: none;
  border-radius: 8px;
  background: rgba(108, 117, 125, 0.3);
  color: #e0e0e0;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(108, 117, 125, 0.5);
}

.close-btn-large:hover {
  background: rgba(108, 117, 125, 0.5);
  transform: translateY(-2px);
}

/* 반응형 디자인 */
@media (max-width: 1200px) {
  .layout-workspace-pro {
    flex-direction: column;
    gap: 15px;
  }
  
  .left-panel,
  .right-panel {
    width: 100%;
    height: auto;
  }
  
  .center-canvas {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .layout-toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .toolbar-section {
    width: 100%;
    justify-content: space-between;
  }
  
  .layout-actions {
    flex-direction: column;
  }
  
  .save-btn-large,
  .close-btn-large {
    width: 100%;
  }
}

/* 드래그 상태일 때 */
.canvas-container.dragging {
  cursor: grabbing !important;
}

.canvas-container.dragging .position-marker-pro {
  pointer-events: none;
}

.canvas-container.dragging .position-marker-pro.selected {
  cursor: grabbing !important;
  pointer-events: auto;
}

/* 전체 페이지에서 드래그 중일 때 텍스트 선택 방지 */
body.no-select {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* 고급 프롬프트 편집기 스타일 */
.prompt-editor-pro {
  background: #1a1a1a;
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  min-height: 80vh;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.editor-header {
  background: linear-gradient(135deg, #8A2BE2 0%, #9932CC 100%);
  color: white;
  padding: 20px;
  border-radius: 12px 12px 0 0;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.editor-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.editor-mode-tabs {
  display: flex;
  gap: 10px;
}

.editor-mode-tabs button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.editor-mode-tabs button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.editor-mode-tabs button.active {
  background: rgba(255, 255, 255, 0.9);
  color: #8A2BE2;
  font-weight: 600;
}

.editor-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.editor-actions button {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  color: #333;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.editor-actions button:hover {
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.editor-actions button.active {
  background: #4CAF50;
  color: white;
}

.editor-actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 3패널 워크스페이스 */
.prompt-workspace {
  display: grid;
  grid-template-columns: 300px 1fr 280px;
  gap: 20px;
  padding: 20px;
  min-height: 70vh;
}

/* 왼쪽 패널 */
.left-prompt-panel {
  background: #2d2d2d;
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  overflow-y: auto;
  max-height: 70vh;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 중앙 패널 */
.center-prompt-panel {
  background: #2d2d2d;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  overflow-y: auto;
  max-height: 70vh;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 오른쪽 패널 */
.right-prompt-panel {
  background: #2d2d2d;
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  overflow-y: auto;
  max-height: 70vh;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 패널 섹션 공통 스타일 */
.panel-section {
  padding: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-section:last-child {
  border-bottom: none;
}

.panel-section h3 {
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  color: #e0e0e0;
  font-weight: 600;
}

.panel-section h4 {
  margin: 0 0 10px 0;
  font-size: 1rem;
  color: #e0e0e0;
  font-weight: 600;
}

/* AI 설정 그리드 */
.ai-settings-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.setting-group label {
  font-size: 0.85rem;
  color: #b0b0b0;
  font-weight: 500;
}

.setting-group select {
  padding: 6px 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  font-size: 0.85rem;
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
}

.setting-group select option {
  background: #2d2d2d;
  color: #e0e0e0;
  padding: 8px;
}

.setting-group input[type="checkbox"] {
  margin-right: 8px;
}

/* 통계 그리드 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-label {
  font-size: 0.85rem;
  color: #b0b0b0;
}

.stat-value {
  color: #ffffff;
  font-weight: 700;
  font-size: 1.1rem;
  background: linear-gradient(145deg, #c5b6d3, #4b474d);
  padding: 4px 12px;
  border-radius: 6px;
  text-align: center;
  min-width: 50px;
  border: 1px solid rgba(237, 234, 240, 0.5);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 검증 상태 */
.validation-status {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 10px;
}

.validation-status.valid {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid #4CAF50;
}

.validation-status.invalid {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid #f44336;
}

.validation-icon {
  font-size: 1.2rem;
}

.validation-text {
  font-size: 0.9rem;
  font-weight: 500;
  color: #e0e0e0;
}

.validation-issues, .validation-suggestions {
  margin-top: 10px;
}

.validation-issues h4, .validation-suggestions h4 {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  color: #b0b0b0;
}

.issue-item, .suggestion-item {
  font-size: 0.85rem;
  padding: 4px 0;
  line-height: 1.4;
}

.issue-item {
  color: #d32f2f;
}

.suggestion-item {
  color: #1976d2;
}

/* 히스토리 리스트 */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-item {
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.history-item:hover {
  background: rgba(138, 43, 226, 0.1);
  border-color: #8A2BE2;
}

.history-preview {
  font-size: 0.85rem;
  color: #e0e0e0;
  margin-bottom: 4px;
}

.history-meta {
  font-size: 0.75rem;
  color: #b0b0b0;
}

/* 템플릿 라이브러리 */
.template-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 15px;
}

.template-name-input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  font-size: 0.85rem;
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
}

.save-template-btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
}

.template-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.template-item {
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.template-info {
  margin-bottom: 8px;
}

.template-name {
  font-weight: 600;
  font-size: 0.9rem;
  color: #e0e0e0;
  margin-bottom: 4px;
}

.template-preview {
  font-size: 0.8rem;
  color: #b0b0b0;
  line-height: 1.3;
}

.template-actions-mini {
  display: flex;
  gap: 6px;
}

.load-template-btn, .delete-template-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.load-template-btn {
  background: #2196f3;
  color: white;
}

.delete-template-btn {
  background: #f44336;
  color: white;
}

.empty-templates {
  text-align: center;
  color: #b0b0b0;
  font-size: 0.85rem;
  padding: 20px;
}

/* 중앙 패널 스타일 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #e0e0e0;
}

.character-count {
  font-size: 0.85rem;
  color: #b0b0b0;
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

.prompt-editor-container {
  position: relative;
}

.prompt-textarea-enhanced {
  width: 100%;
  padding: 15px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
}

.prompt-textarea-enhanced:focus {
  outline: none;
  border-color: #8A2BE2;
  box-shadow: 0 0 0 3px rgba(138, 43, 226, 0.1);
}

.instruction-textarea-enhanced {
  width: 100%;
  padding: 12px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  font-family: inherit;
  font-size: 0.9rem;
  line-height: 1.4;
  resize: vertical;
  transition: border-color 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
}

.instruction-textarea-enhanced:focus {
  outline: none;
  border-color: #8A2BE2;
  box-shadow: 0 0 0 3px rgba(138, 43, 226, 0.1);
}

/* 변수 도우미 오버레이 */
.variable-helper-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #2d2d2d;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  z-index: 10;
  max-width: 300px;
}

.variable-helper-overlay h4 {
  margin: 0 0 10px 0;
  font-size: 0.9rem;
  color: #e0e0e0;
}

.variable-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.variable-btn {
  background: rgba(138, 43, 226, 0.2);
  border: 1px solid #8A2BE2;
  color: #d8bfd8;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  font-family: monospace;
  transition: all 0.2s ease;
}

.variable-btn:hover {
  background: #8A2BE2;
  color: white;
}

/* 미리보기 섹션 */
.prompt-preview-section {
  margin-top: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-controls {
  display: flex;
  gap: 8px;
}

.edit-test-data-btn, .refresh-preview-btn {
  background: #8A2BE2;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
}

.preview-content {
  margin-top: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.85rem;
  line-height: 1.5;
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
  color: #e0e0e0;
}

/* 테스트 결과 섹션 */
.test-result-section {
  margin-top: 20px;
  padding: 15px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 8px;
  border: 1px solid #4CAF50;
}

.test-result-content {
  margin: 15px 0;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 0.9rem;
  line-height: 1.5;
  white-space: pre-wrap;
  max-height: 400px;
  overflow-y: auto;
  color: #e0e0e0;
}

.test-result-actions {
  display: flex;
  gap: 8px;
}

.clear-test-btn, .retest-btn {
  background: #666;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
}

.retest-btn {
  background: #4CAF50;
}

/* 오른쪽 패널 스타일 */
.section-header-small {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.mini-generate-btn {
  background: #ff9800;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.json-textarea-mini {
  width: 100%;
  padding: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.8rem;
  line-height: 1.4;
  resize: vertical;
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
}

.json-textarea-mini.error {
  border-color: #f44336;
  background: rgba(244, 67, 54, 0.05);
}

.field-hint {
  font-size: 0.75rem;
  color: #b0b0b0;
  margin-top: 5px;
  line-height: 1.3;
}

/* 스프레드 정보 컴팩트 */
.spread-info-compact {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
}

.info-label {
  font-size: 0.8rem;
  color: #b0b0b0;
  font-weight: 500;
  min-width: 50px;
}

.info-value {
  font-size: 0.8rem;
  color: #e0e0e0;
  text-align: right;
  word-break: break-word;
}

/* 테스트 데이터 폼 */
.test-data-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.test-input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.test-input-group label {
  font-size: 0.8rem;
  color: #b0b0b0;
  font-weight: 500;
}

.test-input, .test-textarea {
  padding: 6px 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  font-size: 0.8rem;
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
}

.test-textarea {
  resize: vertical;
  font-family: inherit;
}

/* 테스트 카드 표시 */
.test-cards-display {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 8px;
  margin: 5px 0;
  max-height: 120px;
  overflow-y: auto;
}

.test-card-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.test-card-item:last-child {
  border-bottom: none;
}

.card-number {
  color: #8A2BE2;
  font-weight: 600;
  font-size: 0.8rem;
  min-width: 20px;
  margin-right: 8px;
}

.card-name {
  color: #e0e0e0;
  font-size: 0.85rem;
  flex: 1;
}

.test-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
  font-style: italic;
}

/* 재배치 버튼 스타일 */
.reshuffle-btn {
  background: linear-gradient(145deg, #8A2BE2, #9932CC);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  margin-top: 5px;
  transition: all 0.3s ease;
  border: 1px solid rgba(138, 43, 226, 0.3);
}

.reshuffle-btn:hover {
  background: linear-gradient(145deg, #9932CC, #8A2BE2);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(138, 43, 226, 0.3);
}

/* 오류 메시지 개선 */
.error-messages {
  margin-bottom: 15px;
  padding: 15px;
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid #f44336;
  border-radius: 8px;
}

.error-message {
  color: #d32f2f;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.error-message:last-child {
  margin-bottom: 0;
}

/* 반응형 디자인 */
@media (max-width: 1400px) {
  .prompt-workspace {
    grid-template-columns: 280px 1fr 260px;
  }
}

@media (max-width: 1200px) {
  .prompt-workspace {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }
  
  .left-prompt-panel, .right-prompt-panel {
    max-height: none;
  }
  
  .center-prompt-panel {
    order: -1;
  }
}

@media (max-width: 768px) {
  .editor-actions {
    flex-direction: column;
  }
  
  .editor-mode-tabs {
    flex-direction: column;
  }
  
  .prompt-workspace {
    padding: 10px;
    gap: 10px;
  }
}