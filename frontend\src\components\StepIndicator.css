/* StepIndicator.css - 현대적이고 우아한 네비게이터 디자인 */

.step-indicator-nav {
  position: fixed;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1001;
  background: linear-gradient(145deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.9));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 30px;
  padding: 25px 12px;
  box-shadow: 
    0 15px 35px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  min-width: 55px;
  overflow: hidden;
}

.step-indicator-nav::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg, rgba(79, 195, 247, 0.05), rgba(240, 147, 251, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.step-indicator-nav:hover {
  background: linear-gradient(145deg, rgba(26, 26, 46, 0.98), rgba(22, 33, 62, 0.95));
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow: 
    0 20px 45px rgba(0, 0, 0, 0.5),
    0 5px 15px rgba(79, 195, 247, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.step-indicator-nav:hover::before {
  opacity: 1;
}

.step-indicator {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 25px;
  align-items: center;
  position: relative;
}

/* 연결선 - 미니멀하고 우아한 점선 스타일 */
.step-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-item:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 55px;
  left: 50%;
  transform: translateX(-50%);
  width: 1px;
  height: 25px;
  background: rgba(255, 255, 255, 0.15);
  transition: all 0.4s ease;
  opacity: 0.8;
}

/* 점선 스타일의 연결선 */
.step-item:not(:last-child)::before {
  content: '';
  position: absolute;
  top: 58px;
  left: 50%;
  transform: translateX(-50%);
  width: 3px;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  box-shadow: 
    0 6px 0 rgba(255, 255, 255, 0.3),
    0 12px 0 rgba(255, 255, 255, 0.3),
    0 18px 0 rgba(255, 255, 255, 0.3);
  transition: all 0.4s ease;
  opacity: 0.6;
  z-index: 1;
}

.step-item.active::after,
.step-item.completed::after {
  background: rgba(79, 195, 247, 0.4);
  opacity: 1;
  box-shadow: 0 0 8px rgba(79, 195, 247, 0.3);
}

.step-item.active::before,
.step-item.completed::before {
  background: #4fc3f7;
  box-shadow: 
    0 6px 0 #4fc3f7,
    0 12px 0 #4fc3f7,
    0 18px 0 #4fc3f7;
  opacity: 1;
  animation: dotPulse 2s infinite ease-in-out;
}

@keyframes dotPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* 전체 프로그레스 배경 제거 (너무 복잡함) */

.step-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 2px solid rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  cursor: pointer;
  padding: 0;
  width: 50px;
  height: 50px;
  font-family: inherit;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  overflow: hidden;
  z-index: 1;
  backdrop-filter: blur(10px);
}

/* 버튼 내부 글로우 효과 */
.step-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent 70%);
  border-radius: 50%;
  transition: all 0.4s ease;
  transform: translate(-50%, -50%);
  z-index: -1;
}

.step-button:hover::before {
  width: 100px;
  height: 100px;
}

.step-button:hover {
  transform: scale(1.15);
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.step-number {
  font-size: 1.1rem;
  font-weight: 700;
  color: inherit;
  z-index: 2;
  position: relative;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 툴팁 스타일 개선 */
.step-title {
  position: absolute;
  right: 70px;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(145deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.9));
  backdrop-filter: blur(15px);
  color: #f8f9fa;
  padding: 12px 18px;
  border-radius: 15px;
  font-size: 0.95rem;
  font-weight: 500;
  white-space: nowrap;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 1002;
  min-width: 120px;
  text-align: center;
}

.step-title::after {
  content: '';
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid rgba(26, 26, 46, 0.95);
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  filter: drop-shadow(2px 0 4px rgba(0, 0, 0, 0.2));
}

.step-button:hover .step-title {
  opacity: 1;
  visibility: visible;
  transform: translateY(-50%) translateX(-15px);
  background: linear-gradient(145deg, rgba(26, 26, 46, 0.98), rgba(22, 33, 62, 0.95));
}

/* 활성 상태 스타일 */
.step-item.active .step-button {
  background: linear-gradient(145deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  border-color: rgba(255, 255, 255, 0.5);
  color: #ffffff;
  box-shadow: 
    0 10px 30px rgba(102, 126, 234, 0.6),
    0 0 25px rgba(240, 147, 251, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: scale(1.2);
}

.step-item.active .step-button::before {
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2), transparent 70%);
}

/* 완료 상태 스타일 - 미니멀하고 우아한 디자인 */
.step-item.completed .step-button {
  background: linear-gradient(145deg, rgba(79, 195, 247, 0.3), rgba(79, 195, 247, 0.1));
  border: 3px solid #4fc3f7;
  color: #4fc3f7;
  box-shadow: 
    0 8px 25px rgba(79, 195, 247, 0.3),
    inset 0 0 20px rgba(79, 195, 247, 0.1);
  transform: scale(1.05);
  position: relative;
}

/* 완료 상태 - 숫자 유지하되 스타일 변경 */
.step-item.completed .step-number {
  position: relative;
  opacity: 1;
  font-weight: 800;
  color: #4fc3f7;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
}

/* 완료 상태 - 테두리 글로우 효과 */
.step-item.completed .step-button::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  border: 1px solid rgba(79, 195, 247, 0.5);
  animation: completedGlow 3s infinite;
  z-index: -1;
}

@keyframes completedGlow {
  0%, 100% { 
    opacity: 0.6;
    transform: scale(1);
  }
  50% { 
    opacity: 1;
    transform: scale(1.05);
  }
}

/* 활성 스텝 펄스 애니메이션 개선 */
.step-item.active .step-button::after {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 50%;
  border: 2px solid rgba(240, 147, 251, 0.6);
  animation: enhancedStepPulse 2.5s infinite;
  z-index: -1;
}

@keyframes enhancedStepPulse {
  0% {
    transform: scale(1);
    opacity: 1;
    border-color: rgba(240, 147, 251, 0.6);
  }
  50% {
    transform: scale(1.15);
    opacity: 0.8;
    border-color: rgba(79, 195, 247, 0.4);
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
    border-color: rgba(255, 138, 128, 0.2);
  }
}

/* 호버 시 연결선 하이라이트 */
.step-item:hover::after {
  background: rgba(79, 195, 247, 0.6);
  box-shadow: 0 0 8px rgba(79, 195, 247, 0.4);
  width: 2px;
}

.step-item:hover::before {
  background: rgba(79, 195, 247, 0.8);
  box-shadow: 
    0 6px 0 rgba(79, 195, 247, 0.8),
    0 12px 0 rgba(79, 195, 247, 0.8),
    0 18px 0 rgba(79, 195, 247, 0.8);
  opacity: 1;
}

/* 미니 프로그레스 인디케이터 */
.step-indicator-nav::after {
  content: '';
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

/* 반응형 디자인 개선 */
@media (max-width: 1200px) {
  .step-indicator-nav {
    right: 20px;
    padding: 20px 10px;
  }
  
  .step-title {
    right: 65px;
    font-size: 0.9rem;
    padding: 10px 15px;
    min-width: 110px;
  }
}

@media (max-width: 768px) {
  .step-indicator-nav {
    right: 15px;
    padding: 18px 8px;
    min-width: 50px;
  }
  
  .step-button {
    width: 45px;
    height: 45px;
  }
  
  .step-number {
    font-size: 1rem;
  }
  
  .step-title {
    right: 60px;
    font-size: 0.85rem;
    padding: 8px 12px;
    min-width: 100px;
  }
  
  .step-indicator {
    gap: 20px;
  }
  
  .step-item:not(:last-child)::after {
    height: 20px;
    top: 45px;
  }
}

@media (max-width: 480px) {
  .step-indicator-nav {
    right: 10px;
    padding: 15px 6px;
    border-radius: 25px;
  }
  
  .step-button {
    width: 40px;
    height: 40px;
  }
  
  .step-number {
    font-size: 0.9rem;
  }
  
  .step-title {
    display: none; /* 매우 작은 화면에서는 툴팁 숨김 */
  }
  
  .step-indicator {
    gap: 15px;
  }
  
  .step-item:not(:last-child)::after {
    height: 15px;
    top: 40px;
  }
}

/* 다크 모드 지원 */
@media (prefers-color-scheme: dark) {
  .step-indicator-nav {
    background: linear-gradient(145deg, rgba(15, 15, 25, 0.98), rgba(18, 25, 40, 0.95));
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .step-title {
    background: linear-gradient(145deg, rgba(15, 15, 25, 0.98), rgba(18, 25, 40, 0.95));
  }
}

/* 접근성 개선 */
@media (prefers-reduced-motion: reduce) {
  .step-button,
  .step-title,
  .step-indicator-nav,
  .step-item:not(:last-child)::after {
    transition: all 0.2s ease;
    animation: none;
  }
  
  .step-item.active .step-button::after {
    animation: none;
  }
  
  .step-button:hover {
    transform: scale(1.05);
  }
  
  .step-item.active .step-button {
    transform: scale(1.1);
  }
}

/* 키보드 포커스 개선 */
.step-button:focus {
  outline: 3px solid #4fc3f7;
  outline-offset: 3px;
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 0 0 3px rgba(79, 195, 247, 0.3);
}

.step-button:focus:not(:focus-visible) {
  outline: none;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* 고대비 모드 지원 */
@media (prefers-contrast: high) {
  .step-indicator-nav {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #ffffff;
  }
  
  .step-button {
    border: 2px solid #ffffff;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .step-item.active .step-button {
    background: #0066cc;
    border-color: #ffffff;
  }
  
  .step-item.completed .step-button {
    background: #008800;
    border-color: #ffffff;
  }
} 