import React from 'react';
import './StepIndicator.css';

interface StepIndicatorProps {
  steps: Array<{ id: string; title: string }>;
  currentStepId: string | null; // 현재 활성화된 섹션의 ID
  onStepClick: (stepId: string) => void;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({ steps, currentStepId, onStepClick }) => {
  return (
    <nav className="step-indicator-nav">
      <ol className="step-indicator">
        {steps.map((step, index) => (
          <li 
            key={step.id} 
            className={`step-item ${currentStepId === step.id ? 'active' : ''} ${currentStepId && steps.findIndex(s => s.id === currentStepId) > index ? 'completed' : ''}`}
          >
            <button className="step-button" onClick={() => onStepClick(step.id)}>
              <span className="step-number">{index + 1}</span>
              <span className="step-title">{step.title}</span>
            </button>
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default StepIndicator; 