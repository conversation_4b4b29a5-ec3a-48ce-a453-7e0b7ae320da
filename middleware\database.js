const { PrismaClient } = require('@prisma/client');

// PrismaClient 인스턴스 생성
const prisma = new PrismaClient();

// Graceful shutdown을 위한 프로세스 이벤트 리스너
process.on('SIGINT', async () => {
  console.log('Received SIGINT, closing Prisma connection...');
  await prisma.$disconnect();
  process.exit();
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM, closing Prisma connection...');
  await prisma.$disconnect();
  process.exit();
});

module.exports = { prisma }; 