const express = require('express');
const router = express.Router();
const { prisma } = require('../middleware/database');
const { GoogleGenAI } = require('@google/genai');

// Gemini API 설정 (전역변수로 쉽게 변경 가능)
const GEMINI_MODEL = "gemini-2.0-flash";
const GEMINI_API_TIMEOUT = 60000;

// Initialize Google Gemini AI
const ai = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY });

// Gemini API 호출 함수
async function callGeminiAPI(prompt) {
  try {
    const response = await ai.models.generateContent({
      model: GEMINI_MODEL,
      contents: prompt
    });

    return response.text;
  } catch (error) {
    throw new Error(`Gemini API Error: ${error.message}`);
  }
}

// 모든 스프레드 가져오기
router.get('/spreads', async (req, res) => {
  try {
    const spreads = await prisma.tarotSpread.findMany({
      orderBy: [
        { order: 'asc' },
        { createdAt: 'asc' }
      ]
    });
    
    res.json({
      success: true,
      spreads: spreads.map(spread => ({
        ...spread,
        positions: JSON.parse(spread.positions || '[]'),
      }))
    });
  } catch (error) {
    console.error('스프레드 조회 오류:', error);
    res.status(500).json({
      success: false,
      message: '스프레드 데이터를 가져오는 중 오류가 발생했습니다.'
    });
  }
});

// 할인 상태 계산 함수
function calculateDiscountStatus(spread) {
  const now = new Date();
  const { discount, discountStartDate, discountEndDate } = spread;
  
  // 할인이 설정되지 않은 경우
  if (!discount || discount <= 0) {
    return {
      isDiscountActive: false,
      discountTimeRemaining: null,
      finalCost: spread.cost
    };
  }
  
  // 시작일이 있고 아직 시작되지 않은 경우
  if (discountStartDate && new Date(discountStartDate) > now) {
    return {
      isDiscountActive: false,
      discountTimeRemaining: '할인 예정',
      finalCost: spread.cost
    };
  }
  
  // 종료일이 있고 이미 종료된 경우
  if (discountEndDate && new Date(discountEndDate) <= now) {
    return {
      isDiscountActive: false,
      discountTimeRemaining: '할인 종료',
      finalCost: spread.cost
    };
  }
  
  // 할인이 진행 중인 경우
  let timeRemaining = '할인이 진행중입니다!';
  
  if (discountEndDate) {
    const end = new Date(discountEndDate);
    const timeDiff = end.getTime() - now.getTime();
    
    if (timeDiff > 0) {
      const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
      
      if (days > 0) {
        timeRemaining = `할인이 ${days}일 ${hours}시간 남았습니다!`;
      } else if (hours > 0) {
        timeRemaining = `할인이 ${hours}시간 ${minutes}분 남았습니다!`;
      } else if (minutes > 0) {
        timeRemaining = `할인이 ${minutes}분 남았습니다!`;
      } else {
        timeRemaining = '할인이 곧 종료 됩니다!';
      }
    }
  }
  
  return {
    isDiscountActive: true,
    discountTimeRemaining: timeRemaining,
    finalCost: Math.max(1, Math.floor(spread.cost * (1 - discount / 100)))
  };
}

// 활성화된 스프레드만 가져오기 (클라이언트용)
router.get('/spreads/active', async (req, res) => {
  try {
    const spreads = await prisma.tarotSpread.findMany({
      where: {
        isActive: true
      },
      orderBy: [
        { order: 'asc' },
        { createdAt: 'asc' }
      ]
    });
    
    res.json({
      success: true,
      spreads: spreads.map(spread => {
        const discountStatus = calculateDiscountStatus(spread);
        return {
          ...spread,
          positions: JSON.parse(spread.positions || '[]'),
          // 서버에서 계산된 할인 정보
          ...discountStatus,
          // 할인 날짜 정보 포함 (클라이언트에서 표시용)
          discountStartDate: spread.discountStartDate ? spread.discountStartDate.toISOString() : null,
          discountEndDate: spread.discountEndDate ? spread.discountEndDate.toISOString() : null
        };
      })
    });
  } catch (error) {
    console.error('활성 스프레드 조회 오류:', error);
    res.status(500).json({
      success: false,
      message: '스프레드 데이터를 가져오는 중 오류가 발생했습니다.'
    });
  }
});

// 특정 스프레드 가져오기
router.get('/spreads/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const spread = await prisma.tarotSpread.findUnique({
      where: { id }
    });
    
    if (!spread) {
      return res.status(404).json({
        success: false,
        message: '스프레드를 찾을 수 없습니다.'
      });
    }
    
    const discountStatus = calculateDiscountStatus(spread);
    
    res.json({
      success: true,
      spread: {
        ...spread,
        positions: JSON.parse(spread.positions || '[]'),
        ...discountStatus
      }
    });
  } catch (error) {
    console.error('스프레드 조회 오류:', error);
    res.status(500).json({
      success: false,
      message: '스프레드 데이터를 가져오는 중 오류가 발생했습니다.'
    });
  }
});

// 스프레드 할인 유효성 검증 API
router.post('/spreads/:id/validate-discount', async (req, res) => {
  try {
    const { id } = req.params;
    const { expectedCost } = req.body;
    
    const spread = await prisma.tarotSpread.findUnique({
      where: { id }
    });
    
    if (!spread) {
      return res.status(404).json({
        success: false,
        message: '스프레드를 찾을 수 없습니다.'
      });
    }
    
    const discountStatus = calculateDiscountStatus(spread);
    
    // 할인이 만료되었거나 가격이 변경된 경우
    if (expectedCost !== discountStatus.finalCost) {
      return res.json({
        success: false,
        priceChanged: true,
        currentCost: discountStatus.finalCost,
        isDiscountActive: discountStatus.isDiscountActive,
        discountTimeRemaining: discountStatus.discountTimeRemaining,
        message: '할인 상태가 변경되었습니다. 최신 정보를 확인해주세요.'
      });
    }
    
    res.json({
      success: true,
      currentCost: discountStatus.finalCost,
      isDiscountActive: discountStatus.isDiscountActive,
      discountTimeRemaining: discountStatus.discountTimeRemaining
    });
  } catch (error) {
    console.error('할인 유효성 검증 오류:', error);
    res.status(500).json({
      success: false,
      message: '할인 유효성 검증 중 오류가 발생했습니다.'
    });
  }
});

// 새 스프레드 생성
router.post('/spreads', async (req, res) => {
  try {
    const {
      name,
      description,
      cardCount,
      cost,
      iconLayout,
      spreadType,
      layoutDescription,
      className,
      positions,
      discount = 0,
      isActive = true
    } = req.body;

    // 입력 검증
    if (!name || !description || !cardCount || !cost || !spreadType) {
      return res.status(400).json({
        success: false,
        message: '필수 필드가 누락되었습니다.'
      });
    }

    // 스프레드 타입 중복 검사
    const existingSpread = await prisma.tarotSpread.findFirst({
      where: { spreadType }
    });

    if (existingSpread) {
      return res.status(400).json({
        success: false,
        message: '이미 존재하는 스프레드 타입입니다.'
      });
    }

    const newSpread = await prisma.tarotSpread.create({
      data: {
        name,
        description,
        cardCount: parseInt(cardCount),
        cost: parseInt(cost),
        iconLayout: iconLayout || '',
        spreadType,
        layoutDescription: layoutDescription || '',
        className: className || '',
        positions: JSON.stringify(positions || []),
        discount: parseInt(discount),
        isActive: Boolean(isActive)
      }
    });

    res.status(201).json({
      success: true,
      spread: {
        ...newSpread,
        positions: JSON.parse(newSpread.positions)
      }
    });
  } catch (error) {
    console.error('스프레드 생성 오류:', error);
    res.status(500).json({
      success: false,
      message: '스프레드 생성 중 오류가 발생했습니다.'
    });
  }
});

// 스프레드 업데이트
router.put('/spreads/:id', async (req, res) => {
  try {
    const { id } = req.params;
        const {      name,      description,      cardCount,      cost,      iconLayout,      spreadType,      layoutDescription,      className,      positions,      discount,      discountStartDate,      discountEndDate,      isActive,      promptTemplate,      systemInstruction,      cardPositionLabels,      customVariables,      cardIntroTemplates    } = req.body;

    // 스프레드 존재 확인
    const existingSpread = await prisma.tarotSpread.findUnique({
      where: { id }
    });

    if (!existingSpread) {
      return res.status(404).json({
        success: false,
        message: '스프레드를 찾을 수 없습니다.'
      });
    }

    // 스프레드 타입 중복 검사 (자신 제외)
    if (spreadType && spreadType !== existingSpread.spreadType) {
      const duplicateSpread = await prisma.tarotSpread.findFirst({
        where: { 
          spreadType,
          id: { not: id }
        }
      });

      if (duplicateSpread) {
        return res.status(400).json({
          success: false,
          message: '이미 존재하는 스프레드 타입입니다.'
        });
      }
    }

    const updatedSpread = await prisma.tarotSpread.update({
      where: { id },
      data: {
        ...(name !== undefined && { name }),
        ...(description !== undefined && { description }),
        ...(cardCount !== undefined && { cardCount: parseInt(cardCount) }),
        ...(cost !== undefined && { cost: parseInt(cost) }),
        ...(iconLayout !== undefined && { iconLayout }),
        ...(spreadType !== undefined && { spreadType }),
        ...(layoutDescription !== undefined && { layoutDescription }),
        ...(className !== undefined && { className }),
                ...(positions !== undefined && { positions: JSON.stringify(positions) }),        ...(discount !== undefined && { discount: parseInt(discount) }),        ...(isActive !== undefined && { isActive: Boolean(isActive) }),        ...(promptTemplate !== undefined && { promptTemplate }),        ...(systemInstruction !== undefined && { systemInstruction }),        ...(cardPositionLabels !== undefined && { cardPositionLabels }),        ...(customVariables !== undefined && { customVariables }),        ...(cardIntroTemplates !== undefined && { cardIntroTemplates }),        ...(discountStartDate !== undefined && { discountStartDate: discountStartDate ? new Date(discountStartDate) : null }),        ...(discountEndDate !== undefined && { discountEndDate: discountEndDate ? new Date(discountEndDate) : null })
      }
    });

    res.json({
      success: true,
      spread: {
        ...updatedSpread,
        positions: JSON.parse(updatedSpread.positions)
      }
    });
  } catch (error) {
    console.error('스프레드 업데이트 오류:', error);
    res.status(500).json({
      success: false,
      message: '스프레드 업데이트 중 오류가 발생했습니다.'
    });
  }
});

// 스프레드 삭제
router.delete('/spreads/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 스프레드 존재 확인
    const existingSpread = await prisma.tarotSpread.findUnique({
      where: { id }
    });

    if (!existingSpread) {
      return res.status(404).json({
        success: false,
        message: '스프레드를 찾을 수 없습니다.'
      });
    }

    await prisma.tarotSpread.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: '스프레드가 성공적으로 삭제되었습니다.'
    });
  } catch (error) {
    console.error('스프레드 삭제 오류:', error);
    res.status(500).json({
      success: false,
      message: '스프레드 삭제 중 오류가 발생했습니다.'
    });
  }
});

// 스프레드 활성화/비활성화 토글
router.patch('/spreads/:id/toggle', async (req, res) => {
  try {
    const { id } = req.params;

    // 스프레드 존재 확인
    const existingSpread = await prisma.tarotSpread.findUnique({
      where: { id }
    });

    if (!existingSpread) {
      return res.status(404).json({
        success: false,
        message: '스프레드를 찾을 수 없습니다.'
      });
    }

    const updatedSpread = await prisma.tarotSpread.update({
      where: { id },
      data: {
        isActive: !existingSpread.isActive
      }
    });

    res.json({
      success: true,
      spread: {
        ...updatedSpread,
        positions: JSON.parse(updatedSpread.positions)
      }
    });
  } catch (error) {
    console.error('스프레드 토글 오류:', error);
    res.status(500).json({
      success: false,
      message: '스프레드 상태 변경 중 오류가 발생했습니다.'
    });
  }
});

// 스프레드 순서 업데이트
router.patch('/spreads/reorder', async (req, res) => {
  try {
    const { spreads } = req.body; // [{ id, order }, ...]
    
    if (!Array.isArray(spreads)) {
      return res.status(400).json({
        success: false,
        message: '스프레드 순서 데이터가 올바르지 않습니다.'
      });
    }

    // 트랜잭션으로 순서 업데이트
    await prisma.$transaction(
      spreads.map(({ id, order }) =>
        prisma.tarotSpread.update({
          where: { id },
          data: { order: parseInt(order) }
        })
      )
    );

    res.json({
      success: true,
      message: '스프레드 순서가 업데이트되었습니다.'
    });
  } catch (error) {
    console.error('스프레드 순서 업데이트 오류:', error);
    res.status(500).json({
      success: false,
      message: '스프레드 순서 업데이트 중 오류가 발생했습니다.'
    });
  }
});

// 기본 스프레드 데이터 초기화 (개발용)
router.post('/spreads/init', async (req, res) => {
  try {
    // 기존 데이터 확인
    const existingCount = await prisma.tarotSpread.count();
    
    if (existingCount > 0) {
      return res.status(400).json({
        success: false,
        message: '이미 스프레드 데이터가 존재합니다.'
      });
    }

    // 기본 스프레드 데이터 (기존 tarotSpreadsData.ts 기반)
    const defaultSpreads = [
      {
        id: 'three_card',
        name: '3카드',
        description: '과거, 현재, 미래를 나타내는 기본적인 3장 스프레드입니다.',
        cardCount: 3,
        cost: 10,
        iconLayout: '',
        spreadType: 'customThreeCard',
        layoutDescription: '첫 번째 카드는 과거, 두 번째 카드는 현재, 세 번째 카드는 미래를 나타냅니다.',
        className: 'spread-three-card',
        positions: JSON.stringify([
          { id: '1', left: '25%', top: '50%' },
          { id: '2', left: '50%', top: '50%' },
          { id: '3', left: '75%', top: '50%' }
        ]),
        discount: 0,
        isActive: true
      },
      {
        id: 'celtic_cross',
        name: '켈틱 크로스',
        description: '복잡한 상황을 심층적으로 분석하는 가장 유명한 10장 스프레드입니다.',
        cardCount: 10,
        cost: 40,
        iconLayout: '',
        spreadType: 'customCelticCross',
        layoutDescription: '중앙 2장은 현재 상황과 도전, 주변 4장은 주요 영향, 우측 4장은 추가 통찰을 제공합니다.',
        className: 'spread-celtic-cross',
        positions: JSON.stringify([
          { id: '1', left: '40%', top: '40%' },
          { id: '2', left: '40%', top: '40%', transform: 'translate(-50%, -50%) rotate(90deg)' },
          { id: '3', left: '40%', top: '20%' },
          { id: '4', left: '40%', top: '60%' },
          { id: '5', left: '20%', top: '40%' },
          { id: '6', left: '60%', top: '40%' },
          { id: '7', left: '80%', top: '80%' },
          { id: '8', left: '80%', top: '60%' },
          { id: '9', left: '80%', top: '40%' },
          { id: '10', left: '80%', top: '20%' }
        ]),
        discount: 0,
        isActive: true
      }
      // 추가 스프레드들...
    ];

    // 일괄 생성
    await prisma.tarotSpread.createMany({
      data: defaultSpreads
    });

    res.json({
      success: true,
      message: `${defaultSpreads.length}개의 기본 스프레드가 생성되었습니다.`
    });
  } catch (error) {
    console.error('기본 스프레드 초기화 오류:', error);
    res.status(500).json({
      success: false,
      message: '기본 스프레드 초기화 중 오류가 발생했습니다.'
    });
  }
});

// 프롬프트 개선 엔드포인트
router.post('/improve-prompt', async (req, res) => {
  try {
    const { currentPrompt, spreadName, cardCount, description, customVariables } = req.body;
    
    if (!currentPrompt) {
      return res.status(400).json({
        success: false,
        message: '개선할 프롬프트가 필요합니다.'
      });
    }
    
    // 커스텀 변수 정보 추가
    let customVariablesInfo = '';
    if (customVariables && typeof customVariables === 'object') {
      customVariablesInfo = `\n\n활용 가능한 커스텀 변수:
${Object.entries(customVariables).map(([key, value]) => `- {${key}}: "${value}"`).join('\n')}

위 커스텀 변수들을 적절히 활용하여 프롬프트를 더욱 맞춤화하고 개선하세요.`;
    }
    
    const improvementPrompt = `다음은 타로 카드 해석을 위한 AI 프롬프트입니다. 이 프롬프트를 개선해주세요.

현재 프롬프트:
${currentPrompt}

스프레드 정보:
- 이름: ${spreadName || '미지정'}
- 카드 수: ${cardCount || '미지정'}
- 설명: ${description || '미지정'}${customVariablesInfo}

개선 요구사항:
1. 더 명확하고 구체적인 지시문 작성
2. 사용자 경험을 향상시키는 요소 추가
3. 일관된 결과를 얻을 수 있도록 구조화
4. 한국어 특성을 고려한 자연스러운 표현
5. 바넘 효과를 적절히 활용한 개인화된 느낌
6. 타로 카드의 전통적 의미를 현대적으로 해석
7. 커스텀 변수가 있다면 해당 변수들을 효과적으로 활용

개선된 프롬프트만 답변해주세요. 설명이나 추가 설명은 불필요합니다.`;
    
    const improvedPrompt = await callGeminiAPI(improvementPrompt);
    
    res.json({
      success: true,
      improvedPrompt: improvedPrompt.trim()
    });
  } catch (error) {
    console.error('프롬프트 개선 오류:', error);
    res.status(500).json({
      success: false,
      message: '프롬프트 개선 중 오류가 발생했습니다.'
    });
  }
});

// 카드 소개 템플릿 생성 엔드포인트 (새로 추가)
router.post('/generate-card-intro', async (req, res) => {
  try {
    const { spreadName, cardCount, description, layoutDescription, tarotStyle = 'modern', responseLength = 'medium' } = req.body;
    
    if (!spreadName || !cardCount) {
      return res.status(400).json({
        success: false,
        message: '스프레드 이름과 카드 수가 필요합니다.'
      });
    }
    

    
    // 타로 스타일별 설정
    const styleSettings = {
      traditional: {
        tone: '전통적이고 정중한',
        approach: '고전적인 타로의 상징성을 중시하는',
        language: '격식 있고 존중하는'
      },
      modern: {
        tone: '친근하고 현대적인',
        approach: '현실적이고 실용적인',
        language: '자연스럽고 공감하는'
      },
      intuitive: {
        tone: '직관적이고 영감을 주는',
        approach: '내면의 지혜를 깨우는',
        language: '시적이고 감성적인'
      }
    };

    // 응답 길이별 설정
    const lengthSettings = {
      short: {
        description: '간결하고 핵심적인',
        sentences: '1-2문장으로 간단히',
        structure: '핵심 메시지 중심'
      },
      medium: {
        description: '적당히 상세한',
        sentences: '2-3문장으로 균형 있게',
        structure: '의미 설명 + 간단한 조언'
      },
      detailed: {
        description: '상세하고 깊이 있는',
        sentences: '3-4문장으로 자세히',
        structure: '의미 설명 + 성찰 질문 + 구체적 조언'
      }
    };

    const selectedStyle = styleSettings[tarotStyle] || styleSettings.modern;
    const selectedLength = lengthSettings[responseLength] || lengthSettings.medium;

    const generationPrompt = `타로 스프레드 "${spreadName}"의 각 카드별 소개 텍스트 템플릿을 생성해주세요.

스프레드 상세 정보:
- 이름: ${spreadName}
- 카드 수: ${cardCount}장
- 설명: ${description || '설명 없음'}
- 레이아웃 설명: ${layoutDescription || '레이아웃 설명 없음'}

스타일 설정:
- 타로 스타일: ${tarotStyle} (${selectedStyle.tone} 톤, ${selectedStyle.approach} 접근법)
- 응답 길이: ${responseLength} (${selectedLength.description}, ${selectedLength.sentences})

생성 요구사항:
1. 위 스프레드의 이름, 설명, 레이아웃 설명을 참고하여 각 카드 위치의 의미를 정확히 반영
2. 카드 번호 순서대로 ${cardCount}개의 템플릿 생성
3. 각 템플릿은 다음 구조로 작성:
   "{cardIndex}번째 카드, **{cardName}**입니다.\\n\\n[카드 위치의 의미 설명]\\n\\n[성찰 질문이나 조언]"

4. 반드시 포함해야 할 변수들:
   - {userName}: 사용자 이름
   - {cardName}: 선택된 카드 이름  
   - {cardIndex}: 카드 번호 (1, 2, 3...)
   - {totalCards}: 전체 카드 수

5. 스타일 가이드:
   - ${selectedStyle.tone} 톤으로 작성
   - ${selectedStyle.language} 어조 사용
   - 한국어로 작성
   - **카드이름** 형태로 카드명 강조
   - \\n\\n을 사용하여 문단 구분 (줄바꿈을 자주 사용하세요)
   - 마침표 뒤에는 가능한 한 \\n\\n으로 문단을 나누세요
   - 각 카드의 고유한 역할과 의미를 명확히 표현
   - ${selectedLength.structure} 구조 활용
   - ${selectedLength.sentences} 작성

6. 스프레드별 특별 고려사항:
   - 3카드: 과거/현재/미래 또는 상황/도전/조언 구조
   - 5카드: 현재/장애물/숨겨진영향/조언/결과 구조  
   - 7카드: 상황/도전/과거/내려놓을것/자원/행동/결과 구조
   - 10카드: 켈틱크로스 구조 (중심/교차/과거/미래/상단/하단/자아/환경/희망두려움/결과)

응답 형식: 순수 JSON 배열만 반환 (설명 불필요)
예시: ["첫 번째 템플릿", "두 번째 템플릿", ...]

정확히 ${cardCount}개의 템플릿을 생성해주세요.`;
    
    const response = await callGeminiAPI(generationPrompt);
    
    try {
      // AI 응답에서 JSON 부분만 추출 (마크다운 코드 블록 제거)
      let cleanResponse = response.trim();
      
      // ```json 으로 시작하고 ``` 으로 끝나는 마크다운 제거
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      // JSON 형태로 파싱 시도
      const templates = JSON.parse(cleanResponse);
      
      if (!Array.isArray(templates) || templates.length !== parseInt(cardCount)) {
        throw new Error('Generated templates count mismatch');
      }
      
      res.json({
        success: true,
        templates: templates
      });
    } catch (parseError) {
      console.error('템플릿 파싱 오류:', parseError);
      // JSON 파싱 실패 시 기본 템플릿 생성
      const fallbackTemplates = [];
      for (let i = 0; i < parseInt(cardCount); i++) {
        const cardIndex = i + 1;
        let template = `${cardIndex}번째 카드, **{cardName}**입니다.\\n\\n이 카드는 {userName}님의 `;
        
        if (cardCount == 3) {
          if (i === 0) template += "현재 상황을 보여줍니다.";
          else if (i === 1) template += "직면한 도전이나 과제를 나타냅니다.";
          else template += "미래의 가능성과 조언을 제시합니다.";
        } else if (cardCount == 5) {
          if (i === 0) template += "현재 상황의 핵심을 보여줍니다.";
          else if (i === 1) template += "장애물이나 도전을 나타냅니다.";
          else if (i === 2) template += "숨겨진 영향력을 드러냅니다.";
          else if (i === 3) template += "조언과 행동 방향을 제시합니다.";
          else template += "최종 결과와 미래를 암시합니다.";
        } else {
          template += `${cardIndex}번째 위치에서의 특별한 의미를 담고 있습니다.`;
        }
        
        fallbackTemplates.push(template);
      }
      
      res.json({
        success: true,
        templates: fallbackTemplates
      });
    }
  } catch (error) {
    console.error('카드 소개 템플릿 생성 오류:', error);
    res.status(500).json({
      success: false,
      message: '카드 소개 템플릿 생성 중 오류가 발생했습니다.'
    });
  }
});

// 글로벌 설정 관련 API 엔드포인트들 (새로 추가)

// 모든 글로벌 설정 가져오기
router.get('/global-settings', async (req, res) => {
  try {
    const settings = await prisma.tarotGlobalSettings.findMany({
      where: {
        isActive: true
      },
      orderBy: {
        settingKey: 'asc'
      }
    });
    
    res.json({
      success: true,
      settings: settings
    });
  } catch (error) {
    console.error('글로벌 설정 조회 오류:', error);
    res.status(500).json({
      success: false,
      message: '글로벌 설정 데이터를 가져오는 중 오류가 발생했습니다.'
    });
  }
});

// 특정 글로벌 설정 가져오기
router.get('/global-settings/:key', async (req, res) => {
  try {
    const { key } = req.params;
    
    const setting = await prisma.tarotGlobalSettings.findUnique({
      where: { 
        settingKey: key,
        isActive: true
      }
    });
    
    if (!setting) {
      return res.status(404).json({
        success: false,
        message: '설정을 찾을 수 없습니다.'
      });
    }
    
    res.json({
      success: true,
      setting: setting
    });
  } catch (error) {
    console.error('글로벌 설정 조회 오류:', error);
    res.status(500).json({
      success: false,
      message: '글로벌 설정 데이터를 가져오는 중 오류가 발생했습니다.'
    });
  }
});

// 글로벌 설정 업데이트
router.put('/global-settings', async (req, res) => {
  try {
    const { settings } = req.body;
    
    if (!settings || typeof settings !== 'object') {
      return res.status(400).json({
        success: false,
        message: '유효한 설정 데이터가 필요합니다.'
      });
    }
    
    // 트랜잭션으로 모든 설정 업데이트
    const updatePromises = Object.entries(settings).map(async ([key, value]) => {
      return prisma.tarotGlobalSettings.upsert({
        where: { settingKey: key },
        update: { 
          settingValue: String(value),
          isActive: true
        },
        create: {
          settingKey: key,
          settingValue: String(value),
          description: getSettingDescription(key),
          isActive: true
        }
      });
    });
    
    await Promise.all(updatePromises);
    
    res.json({
      success: true,
      message: '글로벌 설정이 성공적으로 업데이트되었습니다.'
    });
  } catch (error) {
    console.error('글로벌 설정 업데이트 오류:', error);
    res.status(500).json({
      success: false,
      message: '글로벌 설정 업데이트 중 오류가 발생했습니다.'
    });
  }
});

// 설정 키에 따른 설명 반환 함수
function getSettingDescription(key) {
  const descriptions = {
    'global_system_instruction': '모든 타로 해석에 공통으로 적용되는 시스템 명령어',
    'default_tarot_style': '기본 타로 해석 스타일 (traditional, modern, intuitive)',
    'default_response_length': '기본 응답 길이 설정 (short, medium, detailed)',
    'card_reveal_animation_speed': '카드 공개 애니메이션 속도 설정',
    'enable_sound_effects': '효과음 활성화 여부',
    'default_language': '기본 언어 설정'
  };
  
  return descriptions[key] || '사용자 정의 설정';
}

// 프롬프트 생성 엔드포인트
router.post('/generate-prompt', async (req, res) => {
  try {
    const { spreadName, cardCount, description, layoutDescription, customVariables } = req.body;
    
    if (!spreadName || !cardCount) {
      return res.status(400).json({
        success: false,
        message: '스프레드 이름과 카드 수가 필요합니다.'
      });
    }
    
    // 커스텀 변수 정보 추가
    let customVariablesInfo = '';
    if (customVariables && typeof customVariables === 'object') {
      customVariablesInfo = `\n\n참고할 커스텀 변수:
${Object.entries(customVariables).map(([key, value]) => `- {${key}}: "${value}"`).join('\n')}

위 커스텀 변수들을 프롬프트에 적절히 활용하여 더욱 맞춤화된 해석을 제공하세요.`;
    }
    
    const generationPrompt = `타로 스프레드 "${spreadName}" (${cardCount}장)의 AI 해석용 프롬프트를 생성해주세요.

스프레드 정보:
- 이름: ${spreadName}
- 카드 수: ${cardCount}장
- 설명: ${description || '설명 없음'}
- 레이아웃 설명: ${layoutDescription || '레이아웃 설명 없음'}${customVariablesInfo}

생성 요구사항:
1. 한국어로 작성
2. {userName}, {cardCount}, {cards}, {userConcern} 등의 기본 변수 활용
3. 커스텀 변수가 있다면 해당 변수들도 적절히 활용
4. 구체적이고 명확한 해석 지시문
5. 사용자 맞춤형 조언 제공
6. 따뜻하고 신비로운 톤

프롬프트만 반환해주세요.`;
    
    const generatedPrompt = await callGeminiAPI(generationPrompt);
    
    res.json({
      success: true,
      generatedPrompt: generatedPrompt.trim()
    });
  } catch (error) {
    console.error('프롬프트 생성 오류:', error);
    res.status(500).json({
      success: false,
      message: '프롬프트 생성 중 오류가 발생했습니다.'
    });
  }
});

// 카드 위치 라벨 생성 엔드포인트
router.post('/generate-position-labels', async (req, res) => {
  try {
    const { spreadName, cardCount, description, layoutDescription } = req.body;
    
    if (!spreadName || !cardCount) {
      return res.status(400).json({
        success: false,
        message: '스프레드 이름과 카드 수가 필요합니다.'
      });
    }
    
    const generationPrompt = `타로 스프레드 "${spreadName}" (${cardCount}장)의 각 카드 위치 라벨을 생성해주세요.

스프레드 정보:
- 이름: ${spreadName}
- 카드 수: ${cardCount}장
- 설명: ${description || '설명 없음'}
- 레이아웃 설명: ${layoutDescription || '레이아웃 설명 없음'}

요구사항:
1. ${cardCount}개의 간단한 라벨 생성
2. 각 위치의 의미를 표현하는 짧은 단어나 구문
3. 한국어로 작성
4. JSON 배열 형태로 반환

예시: ["과거", "현재", "미래"] 또는 ["상황", "도전", "조언", "결과"]

JSON 배열만 반환해주세요.`;
    
    const response = await callGeminiAPI(generationPrompt);
    
    try {
      let cleanResponse = response.trim();
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      const labels = JSON.parse(cleanResponse);
      
      if (!Array.isArray(labels) || labels.length !== parseInt(cardCount)) {
        throw new Error('Generated labels count mismatch');
      }
      
      res.json({
        success: true,
        labels: labels
      });
    } catch (parseError) {
      // 파싱 실패 시 기본 라벨 생성
      const fallbackLabels = [];
      for (let i = 0; i < parseInt(cardCount); i++) {
        fallbackLabels.push(`${i + 1}번째 위치`);
      }
      
      res.json({
        success: true,
        labels: fallbackLabels
      });
    }
  } catch (error) {
    console.error('위치 라벨 생성 오류:', error);
    res.status(500).json({
      success: false,
      message: '위치 라벨 생성 중 오류가 발생했습니다.'
    });
  }
});

// 커스텀 변수 생성 엔드포인트
router.post('/generate-custom-variables', async (req, res) => {
  try {
    const { spreadName, cardCount, description, layoutDescription } = req.body;
    
    if (!spreadName || !cardCount) {
      return res.status(400).json({
        success: false,
        message: '스프레드 이름과 카드 수가 필요합니다.'
      });
    }
    
    const generationPrompt = `타로 스프레드 "${spreadName}" (${cardCount}장)의 커스텀 변수를 생성해주세요.

스프레드 정보:
- 이름: ${spreadName}
- 카드 수: ${cardCount}장
- 설명: ${description || '설명 없음'}
- 레이아웃 설명: ${layoutDescription || '레이아웃 설명 없음'}

요구사항:
1. 프롬프트 템플릿에서 사용할 유용한 변수들
2. 스프레드의 특성에 맞는 분위기나 스타일 변수
3. JSON 객체 형태로 반환
4. 3-5개의 변수 생성

예시: {"mood": "신비로운", "style": "공감적인", "focus": "미래지향적"}

JSON 객체만 반환해주세요.`;
    
    const response = await callGeminiAPI(generationPrompt);
    
    try {
      let cleanResponse = response.trim();
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      const variables = JSON.parse(cleanResponse);
      
      if (typeof variables !== 'object' || Array.isArray(variables)) {
        throw new Error('Generated variables format invalid');
      }
      
      res.json({
        success: true,
        variables: variables
      });
    } catch (parseError) {
      // 파싱 실패 시 기본 변수 생성
      const fallbackVariables = {
        mood: "신비로운",
        style: "공감적인",
        tone: "따뜻한",
        approach: "직관적인"
      };
      
      res.json({
        success: true,
        variables: fallbackVariables
      });
    }
  } catch (error) {
    console.error('커스텀 변수 생성 오류:', error);
    res.status(500).json({
      success: false,
      message: '커스텀 변수 생성 중 오류가 발생했습니다.'
    });
  }
});

// 배열 위치 개선 엔드포인트 (새로 추가)
router.post('/improve-layout', async (req, res) => {
  try {
    const { spreadName, cardCount, description, layoutDescription, currentPositions } = req.body;
    
    if (!spreadName || !cardCount || !currentPositions) {
      return res.status(400).json({
        success: false,
        message: '스프레드 정보와 현재 위치 데이터가 필요합니다.'
      });
    }
    
    const improvementPrompt = `타로 스프레드 "${spreadName}" (${cardCount}장)의 현재 카드 배치를 개선해주세요.

스프레드 정보:
- 이름: ${spreadName}
- 카드 수: ${cardCount}장
- 설명: ${description || '설명 없음'}
- 레이아웃 설명: ${layoutDescription || '레이아웃 설명 없음'}

현재 위치 정보:
${JSON.stringify(currentPositions, null, 2)}

개선 요구사항:
1. 현재 위치들의 전체적인 모양과 구조를 유지
2. 카드들의 순서나 상대적 위치 관계는 변경하지 않음
3. 약간의 조정만으로 더 보기 좋고 균형 잡힌 배치로 개선
4. 카드들이 겹치지 않도록 적절한 간격 유지
5. left, top은 백분율(%) 단위로 0-100 범위
6. 기존 transform 속성이 있다면 유지

개선 방식:
- 위치를 약간씩 조정하여 시각적 균형 개선
- 카드 간격을 더 일정하게 조정
- 전체적인 형태는 유지하면서 정렬 개선
- 너무 극단적인 변화는 피하고 미세 조정에 집중

JSON 배열 형태로 반환해주세요.
형식: [{"id": "1", "left": "25%", "top": "50%", "transform": "..."}, ...]`;
    
    const response = await callGeminiAPI(improvementPrompt);
    
    try {
      let cleanResponse = response.trim();
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      const improvedPositions = JSON.parse(cleanResponse);
      
      if (!Array.isArray(improvedPositions) || improvedPositions.length !== parseInt(cardCount)) {
        throw new Error('Improved positions count mismatch');
      }
      
      res.json({
        success: true,
        positions: improvedPositions
      });
    } catch (parseError) {
      console.error('위치 개선 파싱 오류:', parseError);
      // 파싱 실패 시 원본 위치 반환 (약간의 수동 개선)
      const fallbackPositions = currentPositions.map((pos, index) => {
        // 간단한 정렬 개선: 약간의 위치 조정
        let adjustedLeft = pos.left;
        let adjustedTop = pos.top;
        
        // 퍼센트 값에서 숫자 추출
        const leftNum = parseFloat(pos.left.replace('%', ''));
        const topNum = parseFloat(pos.top.replace('%', ''));
        
        // 미세 조정 (5% 내외로 정렬)
        const roundedLeft = Math.round(leftNum / 5) * 5;
        const roundedTop = Math.round(topNum / 5) * 5;
        
        return {
          ...pos,
          left: `${Math.max(10, Math.min(90, roundedLeft))}%`,
          top: `${Math.max(10, Math.min(90, roundedTop))}%`
        };
      });
      
      res.json({
        success: true,
        positions: fallbackPositions
      });
    }
  } catch (error) {
    console.error('배열 위치 개선 오류:', error);
    res.status(500).json({
      success: false,
      message: '배열 위치 개선 중 오류가 발생했습니다.'
    });
  }
});

// AI 레이아웃 생성 엔드포인트
router.post('/generate-layout', async (req, res) => {
  try {
    const { spreadName, cardCount, description, layoutDescription } = req.body;
    
    if (!spreadName || !cardCount) {
      return res.status(400).json({
        success: false,
        message: '스프레드 이름과 카드 수가 필요합니다.'
      });
    }
    
    const generationPrompt = `타로 스프레드 "${spreadName}" (${cardCount}장)의 카드 배치 레이아웃을 생성해주세요.

스프레드 정보:
- 이름: ${spreadName}
- 카드 수: ${cardCount}장
- 설명: ${description || '설명 없음'}
- 레이아웃 설명: ${layoutDescription || '레이아웃 설명 없음'}

요구사항:
1. ${cardCount}개 카드의 위치 좌표 생성
2. left, top은 백분율(%) 단위로 0-100 범위
3. 의미 있는 배치 (원형, 십자형, 일렬 등)
4. 각 카드는 겹치지 않게 배치
5. JSON 배열 형태로 반환

배열 형태: [{"id": "1", "left": "25%", "top": "50%"}, {"id": "2", "left": "75%", "top": "50%"}]

JSON 배열만 반환해주세요.`;
    
    const response = await callGeminiAPI(generationPrompt);
    
    try {
      let cleanResponse = response.trim();
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      const positions = JSON.parse(cleanResponse);
      
      if (!Array.isArray(positions) || positions.length !== parseInt(cardCount)) {
        throw new Error('Generated positions count mismatch');
      }
      
      res.json({
        success: true,
        positions: positions
      });
    } catch (parseError) {
      // 파싱 실패 시 기본 레이아웃 생성 (일렬 배치)
      const fallbackPositions = [];
      for (let i = 0; i < parseInt(cardCount); i++) {
        fallbackPositions.push({
          id: (i + 1).toString(),
          left: `${25 + (i * 50 / (parseInt(cardCount) - 1 || 1))}%`,
          top: '50%'
        });
      }
      
      res.json({
        success: true,
        positions: fallbackPositions
      });
    }
  } catch (error) {
    console.error('레이아웃 생성 오류:', error);
    res.status(500).json({
      success: false,
      message: '레이아웃 생성 중 오류가 발생했습니다.'
    });
  }
});

module.exports = router; 