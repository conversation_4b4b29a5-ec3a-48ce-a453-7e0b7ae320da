import React, { useState, useEffect, useRef } from 'react';
import './TarotReadingPage.css';
import '../styles/tarotCommon.css';
import { useAuth } from '../contexts/AuthContext';
import { tarotCardsData, type TarotCard } from '../data/tarotCardsData';
import { tarotSpreads, type TarotSpread, SPREAD_CLASS_NAMES } from '../data/tarotSpreadsData';
import TypingText from '../components/TypingText';
import TarotSoundControls from '../components/TarotSoundControls';
import TarotTransition from '../components/TarotTransition';
import { useTarotSound, SOUND_PATHS, getRandomCardSlideSound } from '../hooks/useTarotSound';
import { useTarotState, type FortuneType, type View, type ChatMessage, type ManagerSpreadPosition } from '../hooks/useTarotState';
import { useTarotAnimation } from '../hooks/useTarotAnimation';
import {
  YEAR_FORTUNE_COST,
  NUM_CARDS_FIVE_SPREAD,
  NUM_CARDS_THREE_SPREAD,
  CUSTOM_TAROT_COST_3_CARDS,
  CUSTOM_TAROT_COST_5_CARDS,
  CUSTOM_TAROT_COST_7_CARDS,
  CUSTOM_TAROT_COST_10_CARDS,
  getRandomCards,
  getCustomTarotCost,
  getCardCountByFortuneType,
  getApiFortuneType,
  generateCardIntroductionText,
  getFullScreenViews,
  getFortuneViews,
  getGenericNames,
  generateMeditationTexts,
  formatRemainingTime,
  generateErrorMessage,
  sanitizeCardName,
  generateShareText
} from '../utils/tarotUtils';

const TarotReadingPage: React.FC = () => {
  // 공통 훅들 사용
  const tarotState = useTarotState();
  const tarotSound = useTarotSound();
  const tarotAnimation = useTarotAnimation();
  const { isLoggedIn, user, updateUserCredits, token } = useAuth();

  // 공통 상태들을 구조분해할당으로 가져오기
  const {
    selectedFortuneType, setSelectedFortuneType,
    userName, setUserName,
    currentView, setCurrentView,
    userConcern, setUserConcern,
    selectedCardCountForCustom, setSelectedCardCountForCustom,
    currentNumCardsToSelect, setCurrentNumCardsToSelect,
    currentFortuneCost, setCurrentFortuneCost,
    cardsForSelection, setCardsForSelection,
    selectedCards, setSelectedCards,
    revealedCardsInfo, setRevealedCardsInfo,
    currentCardSelectionStep, setCurrentCardSelectionStep,
    currentCardRevealStep, setCurrentCardRevealStep,
    finalInterpretationText, setFinalInterpretationText,
    chatMessages, setChatMessages,
    fortuneInterpretation, setFortuneInterpretation,
    isLoadingFortune, setIsLoadingFortune,
    apiError, setApiError,
    cooldownMessageDetail, setCooldownMessageDetail,
    isEnteringSpace, setIsEnteringSpace,
    sceneTransition, setSceneTransition,
    autoRevealActive, setAutoRevealActive,
    currentRevealingCard, setCurrentRevealingCard,
    cardFlipStates, setCardFlipStates,
    showTransitionOverlay, setShowTransitionOverlay,
    contentOpacity, setContentOpacity,
    showMeditationButton, setShowMeditationButton,
    meditationTextIndex, setMeditationTextIndex,
    meditationTexts, setMeditationTexts,
    showCardSelectionButton, setShowCardSelectionButton,
    isIntroducingCard, setIsIntroducingCard,
    currentCardIntroText, setCurrentCardIntroText,
    fanHorizontalOffset, setFanHorizontalOffset,
    isDragging, setIsDragging,
    dragStartX, setDragStartX,
    showCardDetailModal, setShowCardDetailModal,
    modalCardDetails, setModalCardDetails,
    userCredits, setUserCredits,
    cardZIndices, setCardZIndices,
    highlightedCardIndex, setHighlightedCardIndex,
    cardsInitializing, setCardsInitializing,
    fanAnimationComplete, setFanAnimationComplete,
    showFinalInterpretationLoading, setShowFinalInterpretationLoading,
    selectedSpreadId, setSelectedSpreadId,
    apiSpreads, setApiSpreads,
    isLoadingSpreads, setIsLoadingSpreads,
    cardsInStartPosition, setCardsInStartPosition,
    cardsMovingToPosition, setCardsMovingToPosition,
    currentMovingCardIndex, setCurrentMovingCardIndex,
    celticCrossSecondCardRotating, setCelticCrossSecondCardRotating,
    managerSpreadPositions, setManagerSpreadPositions,
    resetTarotStates, addChatMessage
  } = tarotState;

  // 사운드 관련 상태들
  const {
    isSoundMuted, setIsSoundMuted,
    soundVolume, setSoundVolume,
    isFortuneMusic,
    audioRef, effectAudioRef,
    playSound, playBackgroundMusic, playEffect, stopSound
  } = tarotSound;

  // 애니메이션 관련 함수들
  const {
    spreadContainerRef,
    getSpreadLayoutStyles,
    transitionToView: animationTransitionToView,
    calculateFanPosition
  } = tarotAnimation;

  // 추가 로컬 상태들
  const [revealTimer, setRevealTimer] = useState<ReturnType<typeof setTimeout> | null>(null);
  const 宇宙背景Ref = useRef<HTMLDivElement>(null);

  // 상수들
  const fullScreenViews = getFullScreenViews();
  const fortuneViews = getFortuneViews();
  const genericNames = getGenericNames();
  const baseZIndex = 1000;
  const activeSpreads = apiSpreads;

  // 기본 초기화 useEffect
  useEffect(() => {
    // 사용자 크레딧 정보 가져오기
    if (isLoggedIn && user) {
      setUserCredits(user.credits || 0);
    }

    // 초기 뷰 설정
    if (currentView === 'concernInput') {
      setCurrentView('typeSelection');
    }
  }, [isLoggedIn, user, setUserCredits, currentView, setCurrentView]);

  // 뷰 변경에 따른 초기화 로직
  useEffect(() => {
    if (currentView === 'meditationIntro') {
      // 명상 텍스트 생성
      const texts = generateMeditationTexts(userName, userConcern, selectedFortuneType || '');
      setMeditationTexts(texts);
      setMeditationTextIndex(0);
      setShowMeditationButton(false);
    } else if (currentView === 'cardCountSelection') {
      setSelectedCards([]);
      setShowCardSelectionButton(false);
    }
  }, [currentView, userName, userConcern, selectedFortuneType, setMeditationTexts, setMeditationTextIndex, setShowMeditationButton, setSelectedCards, setShowCardSelectionButton]);

  // 자동 카드 공개 활성화
  useEffect(() => {
    if (currentView === 'cardRevealAndInterpretation') {
      const timer: number = setTimeout(() => {
        setAutoRevealActive(true);
      }, 1200);
      return () => clearTimeout(timer);
    } else {
      setAutoRevealActive(false);
    }
  }, [currentView, setAutoRevealActive]);

  // 기본 함수들 (기존 로직 유지)
  const handleFortuneTypeSelect = (type: FortuneType) => {
    setSelectedFortuneType(type);

    if (type === 'today') {
      setCurrentNumCardsToSelect(NUM_CARDS_THREE_SPREAD);
      setCurrentFortuneCost(0);
      setCurrentView('nameInput');
    } else if (type === 'year') {
      setCurrentNumCardsToSelect(NUM_CARDS_FIVE_SPREAD);
      setCurrentFortuneCost(YEAR_FORTUNE_COST);
      setCurrentView('nameInput');
    } else {
      setCurrentView('nameInput');
    }
  };

  const handleNameSubmit = () => {
    if (userName.trim()) {
      if (selectedFortuneType === 'today' || selectedFortuneType === 'year') {
        setIsEnteringSpace(true);
        setTimeout(() => {
          setIsEnteringSpace(false);
          setCurrentView('meditationIntro');
        }, 2500);
      } else {
        setCurrentView('concernInput');
      }
    }
  };

  const handleConcernSubmit = () => {
    if (userConcern.trim()) {
      setIsEnteringSpace(true);
      setTimeout(() => {
        setIsEnteringSpace(false);
        setCurrentView('meditationIntro');
      }, 2500);
    }
  };

  // 명상 단계에서 카드 선택으로 진행
  const startCardSelectionPhase = () => {
    const isCustomFortune = selectedFortuneType && !['today', 'year'].includes(selectedFortuneType);

    let systemMessage = '';
    let nextView: View = 'cardDrawing';

    if (selectedFortuneType === 'today') {
      systemMessage = `좋습니다. 이제 당신의 오늘을 들여다볼 시간입니다. 당신의 직감을 믿고, 마음이 이끄는 카드 ${NUM_CARDS_THREE_SPREAD}장을 차례대로 선택해주세요.`;
      nextView = 'cardDrawing';
    } else if (selectedFortuneType === 'year') {
      systemMessage = `한 해의 지혜를 구하는 시간입니다. 우주의 에너지를 느끼며 ${NUM_CARDS_FIVE_SPREAD}장의 카드를 선택해주세요.`;
      nextView = 'cardDrawing';
    } else if (isCustomFortune) {
      systemMessage = `당신의 고민에 대한 답을 찾기 위해, 우선 마음이 이끄는 카드를 선택할 준비를 합니다. 곧이어 실제 카드 장수를 선택하게 됩니다.`;
      nextView = 'cardCountSelection';
    }

    addChatMessage(systemMessage, 'system');
    setCardsForSelection(getRandomCards(tarotCardsData, tarotCardsData.length));
    setSelectedCards([]);
    setShowCardSelectionButton(false);
    transitionToView(nextView);
  };

  // 트랜지션 함수
  const transitionToView = (nextView: View) => {
    animationTransitionToView(
      nextView,
      currentView,
      fullScreenViews,
      isFortuneMusic,
      playSound,
      stopSound,
      setCurrentView,
      setContentOpacity,
      setShowTransitionOverlay,
      SOUND_PATHS
    );
  };

  // 운세 타입 선택 렌더링
  const renderFortuneTypeSelection = () => (
    <div className="celestial-selection-container">
      <div className="celestial-selection-header">
        <h1 className="celestial-title">운명의 선택</h1>
        <p className="celestial-prompt">어떤 미래를 들여다보고 싶으신가요?</p>
      </div>

      <div className="destiny-choices-container">
        <div
          className="destiny-orb today-orb"
          onClick={() => handleFortuneTypeSelect('today')}
        >
          <div className="orb-icon-container">
            <svg className="orb-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </div>
          <h2 className="orb-title">오늘의 운세</h2>
          <p className="orb-description">오늘 하루의 에너지와 조언을 받아보세요</p>
          <p className="orb-subtext">무료</p>
        </div>

        <div
          className="destiny-orb year-orb"
          onClick={() => handleFortuneTypeSelect('year')}
        >
          <div className="orb-icon-container">
            <svg className="orb-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </div>
          <h2 className="orb-title">올해의 운세</h2>
          <p className="orb-description">한 해의 전체적인 흐름과 중요한 시기를 알아보세요</p>
          <p className="orb-subtext">{YEAR_FORTUNE_COST} 크레딧</p>
        </div>
      </div>
    </div>
  );

  // 이름 입력 렌더링
  const renderNameInput = () => (
    <div className="name-input-container">
      <h2>당신의 이름을 알려주세요</h2>
      <input
        type="text"
        value={userName}
        onChange={(e) => setUserName(e.target.value)}
        placeholder="이름을 입력해주세요"
        className="name-input"
        onKeyPress={(e) => e.key === 'Enter' && handleNameSubmit()}
      />
      <button
        onClick={handleNameSubmit}
        disabled={!userName.trim()}
        className="mystical-button"
      >
        다음
      </button>
    </div>
  );

  // 고민 입력 렌더링
  const renderConcernInput = () => (
    <div className="concern-input-container">
      <h2>어떤 고민이 있으신가요?</h2>
      <textarea
        value={userConcern}
        onChange={(e) => setUserConcern(e.target.value)}
        placeholder="고민이나 궁금한 점을 자유롭게 적어주세요"
        className="concern-textarea"
        rows={4}
      />
      <button
        onClick={handleConcernSubmit}
        disabled={!userConcern.trim()}
        className="mystical-button"
      >
        타로 보기
      </button>
    </div>
  );

  // 명상 소개 렌더링
  const renderMeditationIntro = () => {
    if (meditationTexts.length === 0) return null;

    const [text1, text2, text3] = meditationTexts;

    return (
      <div className="meditation-intro-container">
        <p style={{ fontSize: '1.6em', marginBottom: '30px', minHeight: '2em' }}>
          <TypingText text={text1} speed={80} />
        </p>
        <p style={{ fontSize: '1.4em', marginBottom: '40px', minHeight: '3em' }}>
          <TypingText text={text2} speed={60} startDelay={500 + text1.length * 80 + 300} />
        </p>
        <p style={{ fontSize: '1.1em', marginBottom: '50px', minHeight: '2em', opacity: showMeditationButton ? 1 : 0, transition: 'opacity 0.5s ease-in' }}>
           <TypingText
             text={text3}
             speed={50}
             startDelay={500 + (text1.length * 80) + 300 + (text2.length * 60) + 300}
             onComplete={() => setShowMeditationButton(true)}
           />
        </p>
        {showMeditationButton && (
          <button
            onClick={startCardSelectionPhase}
            className="mystical-button"
            style={{ animation: 'fadeIn 0.5s ease-in' }}
          >
            카드 선택하기
          </button>
        )}
      </div>
    );
  };

  return (
    <>
      {/* 사운드 컨트롤 */}
      <TarotSoundControls
        isSoundMuted={isSoundMuted}
        setIsSoundMuted={setIsSoundMuted}
        soundVolume={soundVolume}
        setSoundVolume={setSoundVolume}
        audioRef={audioRef}
        effectAudioRef={effectAudioRef}
      />

      {/* 트랜지션 오버레이 */}
      <TarotTransition
        showTransitionOverlay={showTransitionOverlay}
        isEnteringSpace={isEnteringSpace}
      />

      {/* 배경 오버레이 - 전체화면 뷰에서만 표시 */}
      {fullScreenViews.includes(currentView) && (
        <div className="background-overlay" />
      )}

      {/* 메인 컨텐츠 */}
      <div className="fortune-content" style={{
        opacity: contentOpacity,
        transition: 'opacity 0.8s ease-in-out',
        position: 'relative',
        zIndex: fullScreenViews.includes(currentView) ? 9995 : 'auto'
      }}>
        {currentView === 'typeSelection' && renderFortuneTypeSelection()}
        {currentView === 'nameInput' && renderNameInput()}
        {currentView === 'concernInput' && renderConcernInput()}
        {currentView === 'meditationIntro' && renderMeditationIntro()}

        {/* 다른 뷰들은 계속 추가 예정 */}
        {!['typeSelection', 'nameInput', 'concernInput', 'meditationIntro'].includes(currentView) && (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100vh',
            color: '#fff',
            fontSize: '1.5rem'
          }}>
            <p>다른 뷰들 구현 중...</p>
            <p>현재 뷰: {currentView}</p>
            <button
              onClick={() => setCurrentView('typeSelection')}
              className="mystical-button"
              style={{ marginTop: '20px' }}
            >
              처음으로 돌아가기
            </button>
          </div>
        )}
      </div>
    </>
  );
};

export default TarotReadingPage;
