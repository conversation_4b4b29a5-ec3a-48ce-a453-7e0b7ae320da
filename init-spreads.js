const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function initSpreads() {
  try {
    // 기존 데이터 확인
    const existingCount = await prisma.tarotSpread.count();
    console.log(`기존 스프레드 수: ${existingCount}`);
    
    if (existingCount > 0) {
      console.log('기존 데이터가 있습니다. 삭제 후 재생성합니다.');
      await prisma.tarotSpread.deleteMany();
    }

    // 기본 스프레드 데이터 생성
    const defaultSpreads = [
      {
        name: '3카드 스프레드',
        description: '과거, 현재, 미래를 나타내는 기본적인 3장 스프레드입니다.',
        cardCount: 3,
        cost: 10,
        iconLayout: '',
        spreadType: 'customThreeCard',
        layoutDescription: '첫 번째 카드는 과거, 두 번째 카드는 현재, 세 번째 카드는 미래를 나타냅니다.',
        className: 'spread-three-card',
        positions: JSON.stringify([
          { id: '1', left: '25%', top: '50%' },
          { id: '2', left: '50%', top: '50%' },
          { id: '3', left: '75%', top: '50%' }
        ]),
        discount: 0,
        isActive: true
      },
      {
        name: '5카드 스프레드',
        description: '더 자세한 상황 분석을 위한 5장 스프레드입니다.',
        cardCount: 5,
        cost: 20,
        iconLayout: '',
        spreadType: 'customFiveCard',
        layoutDescription: '5장의 카드로 상황을 종합적으로 분석합니다.',
        className: 'spread-five-card',
        positions: JSON.stringify([
          { id: '1', left: '10%', top: '50%' },
          { id: '2', left: '30%', top: '50%' },
          { id: '3', left: '50%', top: '50%' },
          { id: '4', left: '70%', top: '50%' },
          { id: '5', left: '90%', top: '50%' }
        ]),
        discount: 10,
        isActive: true
      },
      {
        name: '켈틱 크로스',
        description: '복잡한 상황을 심층적으로 분석하는 가장 유명한 10장 스프레드입니다.',
        cardCount: 10,
        cost: 40,
        iconLayout: '',
        spreadType: 'customCelticCross',
        layoutDescription: '중앙 2장은 현재 상황과 도전, 주변 4장은 주요 영향, 우측 4장은 추가 통찰을 제공합니다.',
        className: 'spread-celtic-cross',
        positions: JSON.stringify([
          { id: '1', left: '40%', top: '40%' },
          { id: '2', left: '40%', top: '40%', transform: 'translate(-50%, -50%) rotate(90deg)' },
          { id: '3', left: '40%', top: '20%' },
          { id: '4', left: '40%', top: '60%' },
          { id: '5', left: '20%', top: '40%' },
          { id: '6', left: '60%', top: '40%' },
          { id: '7', left: '80%', top: '80%' },
          { id: '8', left: '80%', top: '60%' },
          { id: '9', left: '80%', top: '40%' },
          { id: '10', left: '80%', top: '20%' }
        ]),
        discount: 20,
        isActive: true
      }
    ];

    // 일괄 생성
    const createdSpreads = [];
    for (const spreadData of defaultSpreads) {
      const spread = await prisma.tarotSpread.create({
        data: spreadData
      });
      createdSpreads.push(spread);
      console.log(`생성됨: ${spread.name} (${spread.id})`);
    }

    console.log(`\n총 ${createdSpreads.length}개의 스프레드가 생성되었습니다.`);
    
    // 생성된 데이터 확인
    const allSpreads = await prisma.tarotSpread.findMany();
    console.log('\n생성된 스프레드 목록:');
    allSpreads.forEach(spread => {
      console.log(`- ${spread.name}: ${spread.cardCount}장, ${spread.cost}크레딧 (할인: ${spread.discount}%)`);
    });

  } catch (error) {
    console.error('스프레드 초기화 오류:', error);
  } finally {
    await prisma.$disconnect();
  }
}

initSpreads(); 