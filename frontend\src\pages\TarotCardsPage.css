/* TarotCardsPage.css - Modern Dark Theme to match HomePage */

.tarot-cards-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 30%, #2d4a5a 70%, #3e5b7a 100%);
  color: #f8f9fa;
  padding: 150px 5% 60px;
  position: relative;
  overflow-x: hidden;
}

/* Floating particles background */
.tarot-cards-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255, 138, 128, 0.4), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(79, 195, 247, 0.3), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(240, 147, 251, 0.3), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(255, 255, 255, 0.2), transparent);
  background-size: 200px 100px;
  animation: sparkleFloat 25s linear infinite;
}

@keyframes sparkleFloat {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  25% { transform: translateY(-10px) translateX(5px); }
  50% { transform: translateY(-20px) translateX(0px); }
  75% { transform: translateY(-10px) translateX(-5px); }
}

.page-title {
  text-align: center;
  color: #f8f9fa;
  margin-bottom: 50px;
  font-size: 3.2rem;
  font-weight: 700;
  background: linear-gradient(45deg, #ff8a80, #f093fb, #4fc3f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
  position: relative;
  z-index: 1;
}

.tarot-suit-section {
  margin-bottom: 60px;
  position: relative;
  z-index: 1;
}

.tarot-card-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  max-width: 1400px;
  margin: 0 auto;
}

.tarot-card-item {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 25px;
  padding: 30px;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.tarot-card-item::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.05), transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tarot-card-item:hover::before {
  opacity: 1;
}

.tarot-card-item:hover {
  transform: translateY(-15px) scale(1.02);
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
}

.tarot-card-image {
  width: 150px;
  height: auto;
  margin-bottom: 20px;
  border-radius: 15px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
}

.tarot-card-item:hover .tarot-card-image {
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.4);
  filter: drop-shadow(0 12px 24px rgba(0, 0, 0, 0.4));
}

.tarot-card-name {
  font-size: 1.3rem;
  color: #ffffff;
  margin-bottom: 15px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.tarot-card-description {
  font-size: 0.95rem;
  color: #d1d9e0;
  line-height: 1.6;
  opacity: 0.9;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: calc(1.6em * 4);
  font-weight: 300;
}

/* Tab Styles */
.tarot-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 50px;
  flex-wrap: wrap;
  gap: 10px;
  position: relative;
  z-index: 1;
}

.tab-button {
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  margin: 5px;
}

.tab-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.tab-button:hover::before {
  left: 100%;
}

.tab-button:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 10px 25px rgba(255, 255, 255, 0.1);
}

.tab-button.active {
  color: #ffffff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
  transform: translateY(-2px);
}

.tab-button.active:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .tarot-cards-page {
    padding: 120px 3% 40px;
  }

  .page-title {
    font-size: 2.5rem;
    margin-bottom: 30px;
  }

  .tarot-card-list {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .tarot-card-item {
    padding: 25px;
  }

  .tarot-card-image {
    width: 120px;
  }

  .tarot-tabs {
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }

  .tab-button {
    padding: 12px 25px;
    font-size: 1rem;
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 2rem;
  }

  .tarot-card-item {
    padding: 20px;
  }

  .tarot-card-image {
    width: 100px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .tarot-card-item,
  .tab-button,
  .tarot-cards-page::before {
    animation: none;
    transition: none;
  }
  
  .tarot-card-item:hover {
    transform: scale(1.02);
  }
  
  .tab-button:hover {
    transform: scale(1.02);
  }
}

/* Focus states for keyboard navigation */
.tarot-card-item:focus,
.tab-button:focus {
  outline: 2px solid #4fc3f7;
  outline-offset: 2px;
}

/* 활성 섹션만 보여주기 위한 스타일 (필요 시) */
/* 현재는 모든 섹션을 그리고 activeSuit에 따라 내용만 변경하므로 특별히 필요 없을 수 있음 */
/* 만약 섹션 자체를 숨기거나 보이게 한다면 아래와 같이 사용 가능
.tarot-suit-section {
  display: none; 
}
.tarot-suit-section.active-section {
  display: block;
}
*/ 