const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function addTestReadings() {
  try {
    console.log('테스트 리딩 데이터 추가 중...');
    
    // 여러 개의 테스트 리딩 추가
    const testReadings = [
      {
        readingType: 'fortune_today',
        cardCount: 3,
        promptTokens: 150,
        completionTokens: 300,
        totalTokens: 450,
        ipAddress: '127.0.0.1'
      },
      {
        readingType: 'fortune_year',
        cardCount: 5,
        promptTokens: 200,
        completionTokens: 400,
        totalTokens: 600,
        ipAddress: '127.0.0.1'
      },
      {
        readingType: 'tarot_spread',
        spreadType: 'celtic_cross',
        cardCount: 10,
        promptTokens: 300,
        completionTokens: 600,
        totalTokens: 900,
        ipAddress: '127.0.0.1'
      },
      {
        readingType: 'tarot_spread',
        spreadType: 'three_card',
        cardCount: 3,
        promptTokens: 120,
        completionTokens: 250,
        totalTokens: 370,
        ipAddress: '127.0.0.1'
      },
      {
        readingType: 'fortune_today',
        cardCount: 1,
        promptTokens: 80,
        completionTokens: 180,
        totalTokens: 260,
        ipAddress: '***********'
      }
    ];
    
    for (const reading of testReadings) {
      await prisma.tarotReading.create({
        data: reading
      });
    }
    
    console.log(`${testReadings.length}개의 테스트 리딩이 추가되었습니다.`);
    
    // 현재 통계 확인
    const totalReadings = await prisma.tarotReading.count();
    const activeSpreadCount = await prisma.tarotSpread.count({
      where: { isActive: true }
    });
    
    console.log(`총 리딩 수: ${totalReadings}`);
    console.log(`활성 스프레드 수: ${activeSpreadCount}`);
    
  } catch (error) {
    console.error('테스트 데이터 추가 실패:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addTestReadings(); 