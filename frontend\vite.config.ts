import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      '/api': {
        target: 'http://125.189.172.142:3000', // 백엔드 서버 주소
        changeOrigin: true,
        // secure: false, // HTTPS를 사용하는 경우 필요할 수 있음
        // rewrite: (path) => path.replace(/^\/api/, '') // 경우에 따라 경로 재작성이 필요할 수 있음
      }
    }
  }
})
