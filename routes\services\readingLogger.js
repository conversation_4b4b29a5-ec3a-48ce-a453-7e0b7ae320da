const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * 타로 리딩 기록을 데이터베이스에 저장
 * @param {Object} readingData 리딩 데이터
 * @param {string|null} readingData.userId 사용자 ID
 * @param {string} readingData.readingType 리딩 타입
 * @param {string|null} readingData.spreadType 스프레드 타입
 * @param {number} readingData.cardCount 카드 수
 * @param {number} readingData.promptTokens 프롬프트 토큰 수
 * @param {number} readingData.completionTokens 완성 토큰 수
 * @param {number} readingData.totalTokens 총 토큰 수
 * @param {string|null} readingData.ipAddress IP 주소
 * @param {string|null} readingData.userAgent 사용자 에이전트
 * @returns {Promise<Object>} 생성된 리딩 기록
 */
async function logTarotReading({
  userId = null,
  readingType,
  spreadType = null,
  cardCount = 0,
  promptTokens = 0,
  completionTokens = 0,
  totalTokens = 0,
  ipAddress = null,
  userAgent = null
}) {
  try {
    const readingRecord = await prisma.tarotReading.create({
      data: {
        userId: userId ? String(userId) : null,
        readingType,
        spreadType,
        cardCount,
        promptTokens,
        completionTokens,
        totalTokens,
        ipAddress,
        userAgent,
        createdAt: new Date()
      }
    });

    console.log(`[Reading Logger] 타로 리딩 기록됨: ID=${readingRecord.id}, Type=${readingType}, Cards=${cardCount}, Tokens=${totalTokens}`);
    
    return readingRecord;
  } catch (error) {
    console.error('[Reading Logger] 리딩 기록 실패:', error);
    // 로깅 실패가 메인 기능을 방해하지 않도록 에러를 throw하지 않음
    return null;
  }
}

/**
 * Gemini API 사용량 추출 함수
 * @param {Object} geminiResponse Gemini API 응답
 * @returns {Object} 토큰 사용량 정보
 */
function extractTokenUsage(geminiResponse) {
  const defaultUsage = {
    promptTokens: 0,
    completionTokens: 0,
    totalTokens: 0
  };

  try {
    // Gemini API 응답에서 사용량 정보 추출
    if (geminiResponse && geminiResponse.usageMetadata) {
      const usage = geminiResponse.usageMetadata;
      return {
        promptTokens: usage.promptTokenCount || 0,
        completionTokens: usage.candidatesTokenCount || 0,
        totalTokens: usage.totalTokenCount || 0
      };
    }
    
    return defaultUsage;
  } catch (error) {
    console.error('[Reading Logger] 토큰 사용량 추출 실패:', error);
    return defaultUsage;
  }
}

/**
 * 리딩 타입별 상수
 */
const READING_TYPES = {
  TAROT_SPREAD: 'tarot_spread',
  FORTUNE_TODAY: 'fortune_today', 
  FORTUNE_YEAR: 'fortune_year',
  FORTUNE_GENERAL: 'fortune_general',
  CUSTOM_TAROT: 'custom_tarot'
};

module.exports = {
  logTarotReading,
  extractTokenUsage,
  READING_TYPES
}; 