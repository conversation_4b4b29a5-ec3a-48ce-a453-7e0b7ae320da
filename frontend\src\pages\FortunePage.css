/* frontend/src/pages/TarotReadingPage.css */
.tarot-reading-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start; /* 콘텐츠가 적을 때 위쪽으로 정렬 */
  min-height: calc(100vh - 80px); /* 헤더 높이 제외 */
  padding: 40px 20px; /* 상하 패딩 증가 */
  padding-top: 120px; /* 헤더 높이(80px) + 추가 여유 */
  background: linear-gradient(135deg, #283D8B 0%, #6A5ACD 40%, #936FFF 65%); /* 좀 더 깊고 신비로운 보라색 계열 */
  color: #E0E0E0; /* 기본 텍스트 색상 약간 어둡게 */
  font-family: 'Nanum Gothic', 'Malgun Gothic', sans-serif; /* 가독성 좋은 기본 고딕체로 변경 */
  text-align: center;
  overflow-y: auto; /* 콘텐츠가 길어지면 스크롤 */
}

.fortune-container {
  background-color: rgba(0, 0, 0, 0.25); /* 배경 어둡게, 투명도 조절 */
  padding: 0px;
  border-radius: 15px; /* 좀 더 부드러운 곡선 */
  box-shadow: 0 10px 35px rgba(0, 0, 0, 0.3), 0 0 15px rgba(220, 180, 255, 0.1) inset; /* 그림자 및 내부 그림자 효과 */
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  width: 100%;
  transition: all 0.3s ease-out;
}

.page-title {
  font-size: 2.3em; /* 약간 작게 */
  margin-bottom: 10px;
  font-family: 'Gaegu', 'Cute Font', cursive; /* 제목용 폰트는 유지하되, Gaegu 추가 */
  color: #E6E6FA; /* 라벤더 색상 */
  text-shadow: 1px 1px 3px rgba(0,0,0,0.4);
}

.page-subtitle {
  font-size: 1.1em;
  margin-bottom: 30px;
  color: #C0C0C0; /* 부제목 색상 */
  opacity: 1;
}

.fortune-type-selector {
  display: flex;
  justify-content: space-around;
  gap: 25px;
  flex-wrap: wrap;
}

.fortune-type-btn {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #DCDCDC;
  padding: 25px;
  border-radius: 18px;
  cursor: pointer;
  font-family: 'Nanum Gothic', 'Malgun Gothic', sans-serif;
  font-size: 1.2em;
  font-weight: 600; /* 약간 굵게 */
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 220px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.2), 0 2px 5px rgba(0,0,0,0.15) inset;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.fortune-type-btn:hover {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  transform: translateY(-7px) scale(1.03);
  box-shadow: 0 12px 25px rgba(0,0,0,0.25), 0 2px 5px rgba(0,0,0,0.1) inset;
  color: #FFF;
}

.fortune-type-btn .btn-icon {
  font-size: 2.3em;
  margin-bottom: 12px;
  filter: drop-shadow(0 0 5px rgba(220, 180, 255, 0.5)); /* 아이콘에 부드러운 그림자 효과 */
}

.fortune-type-btn .btn-description {
  font-size: 0.85em;
  font-weight: normal;
  color: #B0B0B0;
  margin-top: 10px;
}

/* 이름 입력 섹션 */
.name-input-section {
  margin-top: 25px;
  padding: 20px;
}

.name-input-section .section-title {
  font-size: 1.6em;
  margin-bottom: 15px;
  color: #D8BFD8; /* 연보라색 계열 */
  font-family: 'Gaegu', 'Cute Font', cursive;
}
.name-input-section .section-subtitle {
  font-size: 1.0em;
  margin-bottom: 25px;
  color: #B0B0B0;
}


.name-input {
  width: calc(100% - 34px); /* padding 고려 */
  padding: 15px 15px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background-color: rgba(0, 0, 0, 0.2);
  color: #E0E0E0;
  font-size: 1.05em;
  margin-bottom: 25px;
  font-family: 'Nanum Gothic', 'Malgun Gothic', sans-serif;
  transition: all 0.3s ease;
}

.name-input::placeholder {
  color: rgba(224, 224, 224, 0.5);
}

.name-input:focus {
  outline: none;
  border-color: rgba(173, 216, 230, 0.7); /* Lightblue for focus */
  background-color: rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 10px rgba(173, 216, 230, 0.3);
}

.submit-name-btn,
.back-btn {
  padding: 14px 28px;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  font-size: 1.05em;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 8px;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.submit-name-btn {
  background: linear-gradient(145deg, #DAA520, #B8860B); /* 금색 계열 그라데이션 */
  color: #FFFFFF;
  box-shadow: 0 4px 10px rgba(218, 165, 32, 0.3), 0 1px 3px rgba(0,0,0,0.2) inset;
}

.submit-name-btn:hover {
  background: linear-gradient(145deg, #C7921D, #A0740A);
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(218, 165, 32, 0.4), 0 1px 3px rgba(0,0,0,0.1) inset;
}

.back-btn {
  background-color: rgba(255, 255, 255, 0.1);
  color: #D0D0D0;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.25);
  color: #FFF;
  transform: translateY(-2px);
}

/* 카드 선택 섹션 */
.card-selection-section .section-title,
.result-display-section .section-title {
  font-size: 1.7em; /* 타이틀 크기 일관성 */
  margin-bottom: 20px;
  color: #D8BFD8; /* 연보라색 계열 */
  font-family: 'Gaegu', 'Cute Font', cursive;
}

.card-selection-section .section-subtitle {
  font-size: 1.0em;
  margin-bottom: 25px;
  color: #B0B0B0;
}

.cards-to-select-container {
  display: flex;
  justify-content: center;
  align-items: stretch; /* 카드 높이가 다를 경우 맞춰줌 */
  gap: 25px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.selectable-card-item {
  background-color: rgba(255, 255, 255, 0.08); /* 배경 더 어둡게 */
  border-radius: 12px; /* 테두리 둥글기 조정 */
  padding: 12px;
  cursor: pointer;
  transition: all 0.35s cubic-bezier(0.25, 0.8, 0.25, 1); /* 부드러운 전환 효과 */
  border: 1px solid rgba(255, 255, 255, 0.15);
  width: 140px; /* 카드 너비 */
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.selectable-card-item:hover {
  transform: translateY(-12px) scale(1.08); /* 호버 효과 강화 */
  box-shadow: 0 15px 25px rgba(0, 0, 0, 0.3), 0 0 10px rgba(220, 180, 255, 0.2);
  background-color: rgba(255, 255, 255, 0.12);
  border-color: rgba(220, 180, 255, 0.3);
}

.selectable-card-image {
  width: 90px; /* 이미지 너비 */
  height: auto;
  border-radius: 8px;
  margin-bottom: 8px;
  box-shadow: 0 3px 6px rgba(0,0,0,0.15);
  transition: transform 0.01s ease;
}
.selectable-card-item:hover .selectable-card-image {
  transform: scale(2.05); /* 이미지도 약간 커지게 */
}


/* 결과 표시 섹션 */
.result-display-section {
  text-align: center;
  padding: 10px;
}

.fortune-result-card {
  background-color: rgba(0, 0, 0, 0.3); /* 배경 더 어둡게 */
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 25px rgba(0,0,0,0.25), 0 0 10px rgba(220, 180, 255, 0.05) inset;
}

.result-card-image {
  width: 150px; /* 결과 카드 이미지 크기 조정 */
  height: auto;
  border-radius: 10px;
  margin-bottom: 15px;
  box-shadow: 0 6px 12px rgba(0,0,0,0.25);
  border: 2px solid rgba(255,255,255,0.15);
}

.result-card-name {
  font-size: 1.8em; /* 크기 증가 */
  margin-bottom: 18px;
  color: #FFD700; /* 금색 유지 */
  font-family: 'Gaegu', 'Cute Font', cursive;
  text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}

.result-card-interpretation {
  font-size: 1.05em;
  line-height: 1.75;
  white-space: pre-wrap;
  color: #E0E0E0; /* Slightly brighter text */
  background-color: rgba(0, 0, 0, 0.2); /* Slightly darker background for the text box */
  padding: 25px; /* Increased padding */
  border-radius: 12px;
  text-align: left;
  text-shadow: 0px 1px 2px rgba(0,0,0,0.3); /* Subtle shadow for readability */
}

/* 메시지 섹션 (안내, 경고 등) */
.message-section {
  padding: 25px;
  background-color: rgba(0,0,0,0.25);
  border-radius: 18px;
  text-align: center;
  margin-top: 20px;
}

.message-section .section-title {
  font-size: 1.6em;
  margin-bottom: 12px;
  color: #FFC107; 
  font-family: 'Gaegu', 'Cute Font', cursive;
}

.message-section p {
  font-size: 1.05em;
  line-height: 1.65;
  margin-bottom: 20px;
  color: #C8C8C8;
}

/* 로딩 UI 스타일 */
.loading-interpretation {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 35px;
  min-height: 180px; 
}

.spinner {
  border: 5px solid rgba(255, 255, 255, 0.2);
  border-left-color: #DDA0DD; /* 스피너 색상 변경 (연보라) */
  border-radius: 50%;
  width: 45px;
  height: 45px;
  animation: spin 1.2s linear infinite;
  margin-bottom: 22px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1.25em;
  color: #D8BFD8; /* 연보라색 계열 */
  font-family: 'Gaegu', 'Cute Font', cursive;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

/* 에러 메시지 UI 스타일 */
.error-display {
  padding: 25px;
  margin: 20px 0;
  background-color: rgba(139, 0, 0, 0.2); /* 어두운 빨강 배경 */
  border: 1px solid rgba(139, 0, 0, 0.4);
  border-radius: 12px;
  text-align: center;
}

.error-message-title {
  font-size: 1.5em;
  color: #FFA0A0; /* 밝은 빨강 계열 */
  font-family: 'Gaegu', 'Cute Font', cursive;
  margin-bottom: 12px;
}

.error-message-content {
  font-size: 1.0em;
  color: #FFC0C0; /* 밝은 빨강 계열 */
  line-height: 1.6;
  word-break: keep-all; 
}

/* (선택) 다시 시도 버튼 스타일 */
/*
.retry-btn {
  background-color: #FFC107;
  color: #4A0E69;
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-size: 1em;
  font-weight: bold;
  margin-top: 15px;
  transition: background-color 0.3s ease;
}

.retry-btn:hover {
  background-color: #FFD54F;
}
*/

.primary-action-btn {
  padding: 14px 28px;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  font-size: 1.05em;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 8px; 
  text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
  background: linear-gradient(145deg, #8A2BE2, #4B0082); /* BlueViolet to Indigo gradient */
  color: #FFFFFF;
  box-shadow: 0 4px 10px rgba(138, 43, 226, 0.3), 0 1px 3px rgba(0,0,0,0.2) inset;
}

.primary-action-btn:hover {
  background: linear-gradient(145deg, #7B24CB, #3A006B); /* Darker shades on hover */
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(138, 43, 226, 0.4), 0 1px 3px rgba(0,0,0,0.1) inset;
}

/* Styles for the new immersive experience */

/* Fullscreen view base */
.fortune-fullscreen-view {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px;
  z-index: 1000;
  overflow-y: auto;
  color: white; /* Default text color for dark backgrounds */
}

/* 뒤로 가기 버튼 스타일 */
.fortune-back-button {
  opacity: 0.7;
  transition: all 0.3s ease;
}

.fortune-back-button:hover {
  opacity: 1;
  transform: scale(1.1);
  background: rgba(60, 20, 120, 0.6) !important;
  box-shadow: 0 0 15px rgba(138, 43, 226, 0.5) !important;
}

/* Specific background/styles for different fullscreen views can be added */
.view-meditationIntro {
  background: radial-gradient(ellipse at bottom, #1b2735 0%, #090a0f 100%); /* Dark space gradient */
  /* Add subtle star animation if desired */
}

/* --- Space Entry Transition Animation --- */

@keyframes spaceEntry {
  0% {
    opacity: 0;
    transform: scale(1);
    background-color: rgba(0, 0, 0, 0); /* Start transparent */
  }
  50% {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.8); /* Fade to dark */
  }
  100% {
    opacity: 1;
    transform: scale(1.1); /* Slight zoom effect */
    background-color: black; /* End fully black */
  }
}

.space-entry-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1500; /* Above normal fullscreen view, below sound controls */
  background-color: black; /* Fallback background */
  animation: spaceEntry 2.5s ease-out forwards; /* 2.5 seconds duration */
  /* Add pseudo-elements or child elements for star effects later if desired */
}

/* --- Meditation Intro View --- */
.view-meditationIntro {
  /* You can add specific background images or gradients here */
  /* background: url('/images/cosmic-background.jpg') center/cover no-repeat; */
  background: radial-gradient(ellipse at bottom, #1b2735 0%, #090a0f 100%); /* Dark space gradient */
  /* Add subtle star animation if desired */
}

/* Typing Text Effect */
.typing-text::after {
  content: '|'; /* Simple cursor */
  animation: blinkCursor 0.7s infinite;
  margin-left: 2px;
  opacity: 1;
}

/* Apply the class to the TypingText component via its className prop if needed */
/* Example: <TypingText className="typing-text" ... /> */

@keyframes blinkCursor {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

/* Meditation Button Styles */
.meditation-button {
  background: linear-gradient(145deg, #8A2BE2, #4B0082) !important; /* Ensure gradient override */
  color: #E0E0E0 !important;
  padding: 15px 35px !important;
  font-size: 1.2em !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 5px 15px rgba(138, 43, 226, 0.3), 0 1px 3px rgba(0,0,0,0.2) inset !important;
}

.meditation-button:hover {
  background: linear-gradient(145deg, #9932CC, #51008E) !important;
  box-shadow: 0 8px 20px rgba(138, 43, 226, 0.4), 0 1px 4px rgba(0,0,0,0.1) inset !important;
  transform: translateY(-4px) !important;
}

@keyframes fadeInButton {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* --- Placeholders for new UI elements --- */

.tarot-card-button-placeholder {
  /* Style the placeholder buttons for card selection */
  padding: 20px 10px;
  border: 1px solid #ccc;
  background-color: #555;
  color: white;
  cursor: pointer;
  min-width: 80px;
}

.tarot-card-placeholder-small {
   /* Style for the small preview of selected cards */
  padding: 5px;
  border: 1px solid #888;
  background-color: #333;
  color: white;
  font-size: 0.8em;
}

.revealed-card-info {
  border: 1px solid #444;
  padding: 15px;
  margin-bottom: 10px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
}

.message-section {
    /* Style the error/message sections if needed */
    padding: 20px;
    text-align: center;
}

.message-section .section-title {
    margin-bottom: 15px;
}

/* Add other styles for chat, cards etc. as needed */

.typing-cursor {
  display: inline-block;
  margin-left: 2px;
  animation: blink 1s step-end infinite;
  color: #ffeb3b;
  font-weight: bold;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.card-intro-text-container {
  line-height: 1.6;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

/* 기존 highlighted-text에 애니메이션 및 시각적 효과 추가 */
.highlighted-text {
  background-color: rgba(255, 235, 59, 0.15); /* 테마 색상 기반의 연한 배경 */
  padding: 0.1em 0.3em;
  border-radius: 4px;
  font-weight: 600; /* 살짝 굵게 */
  color: #fff59d; /* 테마 색상 텍스트 */
  text-shadow: 0 0 8px rgba(255, 235, 59, 0.5); /* 글로우 효과 강화 */
  box-shadow: 0 0 10px rgba(255, 235, 59, 0.1); /* 미묘한 외부 글로우 */
  animation: highlightPulse 2s ease-in-out infinite; /* 부드러운 펄스 애니메이션 */
  position: relative;
  display: inline-block;
}

@keyframes highlightPulse {
  0%, 100% { text-shadow: 0 0 8px rgba(255, 235, 59, 0.5); }
  50% { text-shadow: 0 0 15px rgba(255, 235, 59, 0.7); }
}

/* Add styles for the credits required message and other feedback messages */
.mystical-feedback-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  width: 100%;
  animation: fadeIn 0.5s ease-out;
  height: 100%;
  max-width: 100vw;
}

.fortune-fullscreen-view .mystical-feedback-container {
  min-height: 100vh;
  padding-top: 0;
  align-items: center;
}

.mystical-feedback-portal {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 15px rgba(220, 180, 255, 0.1) inset;
  padding: 30px;
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.feedback-icon-area {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.feedback-icon-area svg {
  width: 60px;
  height: 60px;
  fill: #D8BFD8;
}

.feedback-icon-area img {
  width: 60px;
  height: 60px;
}

.feedback-portal-title {
  font-size: 1.6em;
  margin-bottom: 15px;
  color: #D8BFD8;
  font-family: 'Gaegu', 'Cute Font', cursive;
}

.feedback-portal-message {
  font-size: 1.1em;
  line-height: 1.6;
  margin-bottom: 25px;
  color: #E0E0E0;
  white-space: pre-line;
}

.feedback-portal-button {
  padding: 12px 25px;
  border-radius: 12px;
  border: none;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #E0E0E0;
  cursor: pointer;
  font-size: 1.05em;
  transition: all 0.3s ease;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.feedback-portal-button:hover {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  color: #FFF;
}

/* Add background overlay styles for fullscreen views */
.background-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #1b2735 0%, #090a0f 100%);
  z-index: 9990;
  animation: fadeIn 0.8s ease-out;
}

/* Mystical Mist/Fog Effect */
.background-overlay .mist-layer {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* Clicks go through */
  opacity: 0; /* Start hidden, fade in via animation */
  z-index: 1; /* Ensure mist is below stars but above plain background */
}

/* Define multiple mist layers with different appearances and animations */
.background-overlay .mist-layer-1 {
  background: radial-gradient(ellipse at 50% 50%, rgba(120, 80, 200, 0.3) 0%, transparent 70%); /* Softer purple */
  animation: flowMist 25s infinite linear alternate, fadeInMist 8s 0.5s forwards ease-out;
  transform: scale(1.5);
}

.background-overlay .mist-layer-2 {
  background: radial-gradient(ellipse at 30% 70%, rgba(180, 100, 220, 0.25) 0%, transparent 65%); /* Lighter, more pinkish purple */
  animation: flowMist 35s infinite linear alternate-reverse, fadeInMist 8s 1s forwards ease-out;
  transform: scale(1.8) rotate(45deg);
}

.background-overlay .mist-layer-3 {
  background: radial-gradient(ellipse at 70% 30%, rgba(90, 60, 150, 0.35) 0%, transparent 60%); /* Deeper purple */
  animation: flowMist 30s infinite linear alternate, fadeInMist 8s 1.5s forwards ease-out;
  transform: scale(1.6) rotate(-30deg);
}

@keyframes flowMist {
  0% {
    transform: translateX(-12%) translateY(-7%) rotate(-5deg) scale(1.5);
  }
  100% {
    transform: translateX(12%) translateY(7%) rotate(25deg) scale(1.85);
  }
}

/* Separate fade-in for mist layers */
@keyframes fadeInMist {
  from { opacity: 0; }
  to { opacity: 0.45; } /* Adjusted final opacity for pulse base */
}

/* New animation for subtle opacity pulse after fade-in */
@keyframes pulseMistOpacity {
  0%, 100% {
    opacity: 0.45; /* Corresponds to fadeInMist's 'to' state */
  }
  50% {
    opacity: 0.65; /* Slightly more opaque */
  }
}

/* Starry sky effect for the background overlay */
.background-overlay::after { /* This is for the stars */
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-image:
    /* Smaller stars */
    radial-gradient(0.5px 0.5px at 10% 30%, white, transparent),
    radial-gradient(0.5px 0.5px at 85% 60%, white, transparent),
    radial-gradient(0.7px 0.7px at 30% 10%, white, transparent),
    radial-gradient(0.7px 0.7px at 70% 85%, white, transparent),

    /* Medium stars */
    radial-gradient(1px 1px at 20% 20%, white, transparent),
    radial-gradient(1px 1px at 80% 80%, white, transparent),
    radial-gradient(1px 1px at 5% 70%, white, transparent),
    radial-gradient(1px 1px at 95% 25%, white, transparent),
    radial-gradient(1px 1px at 40% 50%, white, transparent),
    radial-gradient(1px 1px at 60% 40%, white, transparent),
    radial-gradient(1px 1px at 15% 90%, white, transparent),
    radial-gradient(1px 1px at 75% 5%, white, transparent),

    /* Slightly larger stars */
    radial-gradient(1.5px 1.5px at 50% 60%, white, transparent),
    radial-gradient(1.5px 1.5px at 90% 10%, white, transparent),
    radial-gradient(1.5px 1.5px at 10% 50%, white, transparent),
    radial-gradient(1.5px 1.5px at 65% 75%, white, transparent),
    radial-gradient(1.5px 1.5px at 35% 30%, white, transparent),

    /* Even larger stars */
    radial-gradient(2px 2px at 70% 50%, white, transparent),
    radial-gradient(2px 2px at 25% 70%, white, transparent),
    radial-gradient(2px 2px at 55% 15%, white, transparent);
  background-repeat: repeat; /* Repeat the pattern */
  background-size: 500px 500px; /* Adjust size of the repeating tile to change star density */
  animation: twinkleStars 6s infinite alternate ease-in-out; /* Slower and smoother animation */
  z-index: 2; /* Ensure stars are above mist layers */
}

@keyframes twinkleStars {
  0% { opacity: 0.5; } /* Start a bit more transparent */
  50% { opacity: 0.9; } /* Max opacity */
  100% { opacity: 0.5; } /* Back to start */
}

/* Ensure the mystical-feedback-portal stands out against the background */
.fortune-fullscreen-view .mystical-feedback-portal {
  background-color: rgba(20, 10, 50, 0.6);
  backdrop-filter: blur(5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4), 0 0 25px rgba(150, 100, 255, 0.2) inset;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeIn 0.5s ease-out 0.3s both;
}

.feedback-portal-title {
  color: #f1c40f !important;
  text-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
}

/* Volume control styling - refined */
.mystical-volume-controls {
  transition: all 0.3s ease;
  transform-origin: right top;
}

.mystical-volume-controls:hover {
  opacity: 1;
  transform: scale(1.05);
}

.mystical-volume-controls:not(:hover) {
  opacity: 0.7;
}

.mystical-volume-tooltip {
  opacity: 0;
  transform: translateY(5px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.mystical-volume-controls:hover .mystical-volume-tooltip {
  opacity: 1;
  transform: translateY(0);
}

/* Volume slider thumb for different browsers - refined */
input[type=range]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #ffeb3b;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(255, 235, 59, 0.7);
  transition: transform 0.1s;
}

input[type=range]:hover::-webkit-slider-thumb {
  transform: scale(1.1);
}

input[type=range]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #ffeb3b;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(255, 235, 59, 0.7);
  border: none;
  transition: transform 0.1s;
}

input[type=range]:hover::-moz-range-thumb {
  transform: scale(1.1);
}

input[type=range]::-ms-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #ffeb3b;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(255, 235, 59, 0.7);
}

/* Volume slider track for different browsers - refined */
input[type=range]::-webkit-slider-runnable-track {
  width: 100%;
  height: 6px;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

input[type=range]::-moz-range-track {
  width: 100%;
  height: 6px;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

input[type=range]::-ms-track {
  width: 100%;
  height: 6px;
  cursor: pointer;
  background: transparent;
  border-color: transparent;
  color: transparent;
}

/* Sound controls animation - refined */
@keyframes soundPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.9; }
}

.sound-icon-active {
  animation: soundPulse 2s infinite ease-in-out;
} 

/* New styles for Concern Input Section */
.concern-input-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 20px;
  width: 100%;
  max-width: 700px; /* Limit width for better readability */
  margin: auto; /* Center it in the fullscreen view */
  animation: fadeIn 1s ease-out;
  color: #E0E0E0;
}

.concern-input-section .section-title {
  font-family: 'Cute Font', 'Poor Story', cursive;
  font-size: 2.8em; /* Make it prominent */
  color: #E6E6FA; /* Lavender color */
  text-shadow: 1px 1px 3px rgba(0,0,0,0.4), 0 0 10px rgba(230, 230, 250, 0.3);
  margin-bottom: 20px;
}

.concern-input-section .section-subtitle {
  font-family: 'Poor Story', cursive;
  font-size: 1.3em; /* Slightly larger subtitle */
  color: #C0C0C0; /* Light grey */
  margin-bottom: 35px;
  line-height: 1.6;
}

.concern-textarea {
  width: clamp(300px, 90%, 600px); /* Responsive width */
  min-height: 120px;
  padding: 18px 20px;
  border-radius: 15px; /* Softer corners */
  border: 1px solid rgba(255, 255, 255, 0.25);
  background-color: rgba(0, 0, 0, 0.3);
  color: #E0E0E0;
  font-family: 'Nanum Gothic', 'Malgun Gothic', sans-serif;
  font-size: 1.1em;
  line-height: 1.6;
  margin-bottom: 30px;
  resize: none; /* Disable manual resize by user */
  box-shadow: 0 5px 15px rgba(0,0,0,0.2), inset 0 2px 4px rgba(0,0,0,0.3);
  transition: all 0.3s ease;
}

.concern-textarea::placeholder {
  color: rgba(224, 224, 224, 0.5);
  font-style: italic;
}

.concern-textarea:focus {
  outline: none;
  border-color: rgba(170, 120, 255, 0.7); /* Mystical purple focus */
  background-color: rgba(0, 0, 0, 0.4);
  box-shadow: 0 0 15px rgba(170, 120, 255, 0.4), inset 0 2px 4px rgba(0,0,0,0.2);
}

.submit-concern-btn {
  padding: 16px 35px;
  border-radius: 30px; /* Pill shape */
  border: none;
  cursor: pointer;
  font-family: 'Cute Font', 'Poor Story', cursive;
  font-size: 1.3em;
  font-weight: normal; /* Cute Font is already boldish */
  transition: all 0.3s ease;
  margin: 8px;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
  background: linear-gradient(145deg, #8A2BE2, #4B0082); /* Mystical purple gradient */
  color: #FFFFFF;
  box-shadow: 0 5px 15px rgba(138, 43, 226, 0.4), 0 1px 3px rgba(0,0,0,0.2) inset;
}

.submit-concern-btn:hover {
  background: linear-gradient(145deg, #9932CC, #5D1049);
  transform: translateY(-3px) scale(1.03);
  box-shadow: 0 8px 20px rgba(138, 43, 226, 0.5), 0 1px 4px rgba(0,0,0,0.1) inset;
}

.submit-concern-btn:disabled {
  background: linear-gradient(145deg, #5c427d, #382154);
  color: #a0a0a0;
  cursor: not-allowed;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  transform: translateY(0);
}

.concern-input-section .back-btn {
  background-color: rgba(255, 255, 255, 0.05);
  color: #B0B0B0;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 12px 25px;
  font-size: 1em;
  font-family: 'Nanum Gothic', sans-serif;
}

.concern-input-section .back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: #FFF;
}

.card-count-button {
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  background: linear-gradient(145deg, rgba(90, 60, 150, 0.7), rgba(60, 30, 100, 0.9)) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(5px);
}

.card-count-button:hover {
  transform: translateY(-5px) scale(1.05) !important;
  background: linear-gradient(145deg, rgba(120, 80, 180, 0.8), rgba(80, 40, 140, 0.9)) !important;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3), 0 0 15px rgba(140, 100, 200, 0.5) !important;
  border: 1px solid rgba(255, 215, 0, 0.3) !important;
}

.spread-layout-visual {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  padding: 5px;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.card-count-button:hover .spread-layout-visual {
  background-color: rgba(20, 10, 50, 0.5);
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.2);
}

/* 타로 스프레드 아이콘 내 점 애니메이션 */
@keyframes pulseDot {
  0%, 100% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.3); opacity: 1; }
}

.card-count-button:hover .spread-dot {
  animation: pulseDot 1.5s infinite ease-in-out;
}

/* 각 점마다 애니메이션 지연시간 다르게 적용 */
.spread-dot:nth-child(1) { animation-delay: 0s; }
.spread-dot:nth-child(2) { animation-delay: 0.1s; }
.spread-dot:nth-child(3) { animation-delay: 0.2s; }
.spread-dot:nth-child(4) { animation-delay: 0.3s; }
.spread-dot:nth-child(5) { animation-delay: 0.4s; }
.spread-dot:nth-child(6) { animation-delay: 0.5s; }
.spread-dot:nth-child(7) { animation-delay: 0.6s; }
.spread-dot:nth-child(8) { animation-delay: 0.7s; }
.spread-dot:nth-child(9) { animation-delay: 0.8s; }
.spread-dot:nth-child(10) { animation-delay: 0.9s; }
.spread-dot:nth-child(11) { animation-delay: 1.0s; }
.spread-dot:nth-child(12) { animation-delay: 1.1s; }

/* 타로 스프레드 선택 섹션 스타일 - 새 디자인 */
.tarot-spread-selection {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  color: white;
  padding: 30px 30px 50px;
  overflow-y: auto;
  background: radial-gradient(ellipse at center, rgba(40, 10, 80, 0.5) 0%, rgba(20, 5, 40, 0.3) 70%, rgba(10, 2, 20, 0.1) 100%);
  overflow-x: hidden;
}

.spread-selection-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 0 20px;
}

.spread-selection-title {
  font-size: 2.8rem;
  margin-bottom: 15px;
  font-weight: bold;
  color: #d0b0ff;
  text-shadow: 0 0 15px rgba(168, 100, 255, 0.8);
  letter-spacing: 2px;
}

.spread-selection-subtitle {
  font-size: 1.4rem;
  opacity: 0.9;
  max-width: 800px;
  margin: 0 auto;
  color: #e0d0ff;
}

.spread-grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 25px;
  justify-content: center;
  padding: 0 20px;
  margin-bottom: 30px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.spread-grid-item {
  perspective: 1000px;
  cursor: pointer;
  height: 340px; /* 높이 증가 */
}

.spread-card {
  height: 100%;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-style: preserve-3d;
  position: relative;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, rgba(70, 30, 140, 0.7) 0%, rgba(40, 15, 80, 0.8) 100%);
  border: 1px solid rgba(150, 120, 200, 0.3);
}

.spread-card:hover {
  transform: translateY(-10px) scale(1.03);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5), 0 0 30px rgba(138, 43, 226, 0.4);
  border-color: rgba(200, 180, 255, 0.6);
  background: linear-gradient(135deg, rgba(90, 40, 170, 0.8) 0%, rgba(60, 25, 120, 0.9) 100%);
}

.spread-card:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 60%);
  z-index: 1;
}

.spread-card-inner {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 15px 12px 18px 12px; /* 하단 패딩 증가로 아이콘 잘림 방지 */
  position: relative;
  z-index: 2;
  box-sizing: border-box; /* 박스 사이징 명시 */
}

.spread-visual-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(20, 5, 40, 0.6);
  border-radius: 10px;
  margin-bottom: 12px; /* 여백 감소 */
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(120, 80, 180, 0.3);
  min-height: 180px; /* 최소 높이 설정 */
}

.spread-visual-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(100, 50, 200, 0.1) 0%, rgba(30, 10, 60, 0.3) 100%);
  z-index: 1;
}

.spread-visual {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 2;
}

/* 스프레드 레이아웃 점(dot) 스타일 */
.spread-dot {
  width: 10px;
  height: 16px;
  background-color: rgba(220, 180, 255, 0.9);
  border-radius: 2px;
  display: block;
  position: absolute;
  box-shadow: 0 0 8px rgba(180, 120, 255, 0.6);
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.spread-card:hover .spread-dot {
  background-color: rgba(255, 215, 30, 0.9);
  box-shadow: 0 0 12px rgba(255, 215, 30, 0.8), 0 0 20px rgba(255, 215, 30, 0.4);
  animation: spreadDotPulse 1.5s infinite ease-in-out;
}

@keyframes spreadDotPulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.9; }
  50% { transform: translate(-50%, -50%) scale(1.4); opacity: 1; }
}

.spread-title {
  font-size: 1.3rem; /* 폰트 크기 약간 감소 */
  font-weight: bold;
  text-align: center;
  margin-bottom: 6px; /* 여백 감소 */
  color: #fff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 1px;
  line-height: 1.3; /* 줄 높이 조정 */
}

.spread-meta {
  font-size: 0.85rem; /* 폰트 크기 약간 감소 */
  text-align: center;
  color: rgba(220, 200, 255, 0.9);
  padding: 5px 8px; /* 패딩 감소 */
  background: rgba(60, 20, 120, 0.4);
  border-radius: 15px; /* 둥근 모서리 감소 */
  margin: 0 auto;
  display: inline-block;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  line-height: 1.2; /* 줄 높이 조정 */
  max-width: 90%; /* 최대 너비 제한 */
  position: relative;
}

/* 할인된 가격 스타일 - 별빛색 */
.discounted-price {
  color: #FFD700;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(255, 215, 0, 0.8), 0 0 12px rgba(255, 215, 0, 0.6), 0 0 16px rgba(255, 215, 0, 0.4);
  animation: starGlow 2s ease-in-out infinite alternate;
}

@keyframes starGlow {
  0% {
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.8), 0 0 12px rgba(255, 215, 0, 0.6), 0 0 16px rgba(255, 215, 0, 0.4);
  }
  100% {
    text-shadow: 0 0 12px rgba(255, 215, 0, 1), 0 0 16px rgba(255, 215, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.6);
  }
}

/* 할인 시간 표시 - 빨간 글씨 */
.discount-timer {
  font-size: 0.7rem;
  color: #ff4757;
  margin-top: 2px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  animation: timerPulse 1.5s ease-in-out infinite;
}

@keyframes timerPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.spread-selection-footer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.spread-back-btn {
  background: rgba(60, 20, 120, 0.4);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(150, 100, 200, 0.3);
  padding: 12px 30px;
  border-radius: 30px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.spread-back-btn:hover {
  background: rgba(80, 40, 150, 0.6);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3), 0 0 15px rgba(138, 43, 226, 0.4);
  transform: translateY(-3px);
  border-color: rgba(180, 140, 255, 0.5);
}

/* 각 스프레드별 dot 위치 지정 - 전통적인 타로 레이아웃에 맞게 수정 */

/* 3카드 - 과거/현재/미래 (일렬 배치) */
.spread-three-card .spread-dot:nth-child(1) { left: 25%; top: 50%; }
.spread-three-card .spread-dot:nth-child(2) { left: 50%; top: 50%; }
.spread-three-card .spread-dot:nth-child(3) { left: 75%; top: 50%; }

/* 4카드 - 십자형 (위/아래/좌/우) */
.spread-four-card .spread-dot:nth-child(1) { left: 50%; top: 20%; }
.spread-four-card .spread-dot:nth-child(2) { left: 25%; top: 50%; }
.spread-four-card .spread-dot:nth-child(3) { left: 75%; top: 50%; }
.spread-four-card .spread-dot:nth-child(4) { left: 50%; top: 80%; }

/* 5카드 - 십자형 (중앙 카드 포함) */
.spread-five-card .spread-dot:nth-child(1) { left: 50%; top: 20%; }
.spread-five-card .spread-dot:nth-child(2) { left: 25%; top: 50%; }
.spread-five-card .spread-dot:nth-child(3) { left: 50%; top: 50%; }
.spread-five-card .spread-dot:nth-child(4) { left: 75%; top: 50%; }
.spread-five-card .spread-dot:nth-child(5) { left: 50%; top: 80%; }

/* 더블 라인 - 6카드 (2줄 x 3열) */
.spread-double-line .spread-dot:nth-child(1) { left: 25%; top: 35%; }
.spread-double-line .spread-dot:nth-child(2) { left: 50%; top: 35%; }
.spread-double-line .spread-dot:nth-child(3) { left: 75%; top: 35%; }
.spread-double-line .spread-dot:nth-child(4) { left: 25%; top: 65%; }
.spread-double-line .spread-dot:nth-child(5) { left: 50%; top: 65%; }
.spread-double-line .spread-dot:nth-child(6) { left: 75%; top: 65%; }

/* 켈틱 크로스 - 전통적인 10카드 레이아웃 */
.spread-celtic-cross .spread-dot:nth-child(1) { left: 40%; top: 40%; }
.spread-celtic-cross .spread-dot:nth-child(2) { left: 40%; top: 40%; transform: translate(-50%, -50%) rotate(90deg); }
.spread-celtic-cross .spread-dot:nth-child(3) { left: 40%; top: 20%; }
.spread-celtic-cross .spread-dot:nth-child(4) { left: 40%; top: 60%; }
.spread-celtic-cross .spread-dot:nth-child(5) { left: 20%; top: 40%; }
.spread-celtic-cross .spread-dot:nth-child(6) { left: 60%; top: 40%; }
.spread-celtic-cross .spread-dot:nth-child(7) { left: 80%; top: 80%; }
.spread-celtic-cross .spread-dot:nth-child(8) { left: 80%; top: 60%; }
.spread-celtic-cross .spread-dot:nth-child(9) { left: 80%; top: 40%; }
.spread-celtic-cross .spread-dot:nth-child(10) { left: 80%; top: 20%; }

/* 매직 세븐 - V자 모양 배치 */
.spread-magic-seven .spread-dot:nth-child(1) { left: 50%; top: 80%; }
.spread-magic-seven .spread-dot:nth-child(2) { left: 40%; top: 65%; }
.spread-magic-seven .spread-dot:nth-child(3) { left: 60%; top: 65%; }
.spread-magic-seven .spread-dot:nth-child(4) { left: 30%; top: 50%; }
.spread-magic-seven .spread-dot:nth-child(5) { left: 70%; top: 50%; }
.spread-magic-seven .spread-dot:nth-child(6) { left: 20%; top: 35%; }
.spread-magic-seven .spread-dot:nth-child(7) { left: 80%; top: 35%; }

/* 십자 - 5카드 십자형 */
.spread-cross .spread-dot:nth-child(1) { left: 50%; top: 50%; }
.spread-cross .spread-dot:nth-child(2) { left: 50%; top: 20%; }
.spread-cross .spread-dot:nth-child(3) { left: 20%; top: 50%; }
.spread-cross .spread-dot:nth-child(4) { left: 80%; top: 50%; }
.spread-cross .spread-dot:nth-child(5) { left: 50%; top: 80%; }

/* 피라미드 - 10카드 피라미드 (1-2-3-4 구조) */
.spread-pyramid .spread-dot:nth-child(1) { left: 50%; top: 10%; }
.spread-pyramid .spread-dot:nth-child(2) { left: 37.5%; top: 25%; }
.spread-pyramid .spread-dot:nth-child(3) { left: 62.5%; top: 25%; }
.spread-pyramid .spread-dot:nth-child(4) { left: 25%; top: 45%; }
.spread-pyramid .spread-dot:nth-child(5) { left: 41.7%; top: 45%; }
.spread-pyramid .spread-dot:nth-child(6) { left: 58.3%; top: 45%; }
.spread-pyramid .spread-dot:nth-child(7) { left: 75%; top: 45%; }
.spread-pyramid .spread-dot:nth-child(8) { left: 12.5%; top: 70%; }
.spread-pyramid .spread-dot:nth-child(9) { left: 31.25%; top: 70%; }
.spread-pyramid .spread-dot:nth-child(10) { left: 50%; top: 70%; }

/* 말발굽 - 7카드 호스슈 모양 */
.spread-horseshoe .spread-dot:nth-child(1) { left: 15%; top: 75%; }
.spread-horseshoe .spread-dot:nth-child(2) { left: 25%; top: 50%; }
.spread-horseshoe .spread-dot:nth-child(3) { left: 40%; top: 30%; }
.spread-horseshoe .spread-dot:nth-child(4) { left: 50%; top: 20%; }
.spread-horseshoe .spread-dot:nth-child(5) { left: 60%; top: 30%; }
.spread-horseshoe .spread-dot:nth-child(6) { left: 75%; top: 50%; }
.spread-horseshoe .spread-dot:nth-child(7) { left: 85%; top: 75%; }

/* 어웬 - 7카드 켈트 삼광선 */
.spread-awen .spread-dot:nth-child(1) { left: 20%; top: 20%; }
.spread-awen .spread-dot:nth-child(2) { left: 50%; top: 10%; }
.spread-awen .spread-dot:nth-child(3) { left: 80%; top: 20%; }
.spread-awen .spread-dot:nth-child(4) { left: 35%; top: 50%; }
.spread-awen .spread-dot:nth-child(5) { left: 50%; top: 45%; }
.spread-awen .spread-dot:nth-child(6) { left: 65%; top: 50%; }
.spread-awen .spread-dot:nth-child(7) { left: 50%; top: 80%; }

/* 드래곤 라자 - 10카드 용의 지혜 */
.spread-dragon-raziel .spread-dot:nth-child(1) { left: 50%; top: 10%; }
.spread-dragon-raziel .spread-dot:nth-child(2) { left: 30%; top: 25%; }
.spread-dragon-raziel .spread-dot:nth-child(3) { left: 50%; top: 25%; }
.spread-dragon-raziel .spread-dot:nth-child(4) { left: 70%; top: 25%; }
.spread-dragon-raziel .spread-dot:nth-child(5) { left: 15%; top: 50%; }
.spread-dragon-raziel .spread-dot:nth-child(6) { left: 35%; top: 50%; }
.spread-dragon-raziel .spread-dot:nth-child(7) { left: 65%; top: 50%; }
.spread-dragon-raziel .spread-dot:nth-child(8) { left: 85%; top: 50%; }
.spread-dragon-raziel .spread-dot:nth-child(9) { left: 40%; top: 75%; }
.spread-dragon-raziel .spread-dot:nth-child(10) { left: 60%; top: 75%; }

/* 양자택일 - 5카드 이진 선택 */
.spread-binary-choice .spread-dot:nth-child(1) { left: 50%; top: 20%; }
.spread-binary-choice .spread-dot:nth-child(2) { left: 25%; top: 50%; }
.spread-binary-choice .spread-dot:nth-child(3) { left: 75%; top: 50%; }
.spread-binary-choice .spread-dot:nth-child(4) { left: 35%; top: 80%; }
.spread-binary-choice .spread-dot:nth-child(5) { left: 65%; top: 80%; }

/* 릴레이션십 - 7카드 관계 분석 */
.spread-relationship .spread-dot:nth-child(1) { left: 25%; top: 25%; }
.spread-relationship .spread-dot:nth-child(2) { left: 75%; top: 25%; }
.spread-relationship .spread-dot:nth-child(3) { left: 20%; top: 50%; }
.spread-relationship .spread-dot:nth-child(4) { left: 50%; top: 45%; }
.spread-relationship .spread-dot:nth-child(5) { left: 80%; top: 50%; }
.spread-relationship .spread-dot:nth-child(6) { left: 35%; top: 75%; }
.spread-relationship .spread-dot:nth-child(7) { left: 65%; top: 75%; }

/* 컵오브릴레이션십 - 8카드 감정의 잔 */
.spread-cup-of-relationship .spread-dot:nth-child(1) { left: 35%; top: 20%; }
.spread-cup-of-relationship .spread-dot:nth-child(2) { left: 50%; top: 15%; }
.spread-cup-of-relationship .spread-dot:nth-child(3) { left: 65%; top: 20%; }
.spread-cup-of-relationship .spread-dot:nth-child(4) { left: 25%; top: 45%; }
.spread-cup-of-relationship .spread-dot:nth-child(5) { left: 75%; top: 45%; }
.spread-cup-of-relationship .spread-dot:nth-child(6) { left: 20%; top: 70%; }
.spread-cup-of-relationship .spread-dot:nth-child(7) { left: 80%; top: 70%; }
.spread-cup-of-relationship .spread-dot:nth-child(8) { left: 50%; top: 85%; }

/* 음양 - 6카드 균형 */
.spread-yin-yang .spread-dot:nth-child(1) { left: 30%; top: 35%; }
.spread-yin-yang .spread-dot:nth-child(2) { left: 50%; top: 25%; }
.spread-yin-yang .spread-dot:nth-child(3) { left: 70%; top: 35%; }
.spread-yin-yang .spread-dot:nth-child(4) { left: 30%; top: 65%; }
.spread-yin-yang .spread-dot:nth-child(5) { left: 50%; top: 75%; }
.spread-yin-yang .spread-dot:nth-child(6) { left: 70%; top: 65%; }

/* 리딩 마인드 - 7카드 심리 분석 */
.spread-reading-mind .spread-dot:nth-child(1) { left: 50%; top: 15%; }
.spread-reading-mind .spread-dot:nth-child(2) { left: 30%; top: 35%; }
.spread-reading-mind .spread-dot:nth-child(3) { left: 50%; top: 35%; }
.spread-reading-mind .spread-dot:nth-child(4) { left: 70%; top: 35%; }
.spread-reading-mind .spread-dot:nth-child(5) { left: 25%; top: 65%; }
.spread-reading-mind .spread-dot:nth-child(6) { left: 50%; top: 65%; }
.spread-reading-mind .spread-dot:nth-child(7) { left: 75%; top: 65%; }

/* 호로스코프 - 12카드 원형 배치 */
.spread-horoscope .spread-dot {
  width: 8px;
  height: 12px;
}
.spread-horoscope .spread-dot:nth-child(1) { left: 50%; top: 8%; }     /* 12시 */
.spread-horoscope .spread-dot:nth-child(2) { left: 75%; top: 15%; }    /* 1시 */
.spread-horoscope .spread-dot:nth-child(3) { left: 92%; top: 35%; }    /* 3시 */
.spread-horoscope .spread-dot:nth-child(4) { left: 92%; top: 65%; }    /* 4시 */
.spread-horoscope .spread-dot:nth-child(5) { left: 75%; top: 85%; }    /* 5시 */
.spread-horoscope .spread-dot:nth-child(6) { left: 50%; top: 92%; }    /* 6시 */
.spread-horoscope .spread-dot:nth-child(7) { left: 25%; top: 85%; }    /* 7시 */
.spread-horoscope .spread-dot:nth-child(8) { left: 8%; top: 65%; }     /* 8시 */
.spread-horoscope .spread-dot:nth-child(9) { left: 8%; top: 35%; }     /* 9시 */
.spread-horoscope .spread-dot:nth-child(10) { left: 25%; top: 15%; }   /* 10시 */
.spread-horoscope .spread-dot:nth-child(11) { left: 42%; top: 8%; }    /* 11시 */
.spread-horoscope .spread-dot:nth-child(12) { left: 58%; top: 8%; }    /* 12시 옆 */

/* 미디어 쿼리 - 반응형 디자인 */
/* 모바일 전용 스타일 */
@media (max-width: 576px) {
  .spread-grid-item {
    height: 320px; /* 모바일에서는 좀 더 컴팩트하게 */
  }
  
  .spread-card-inner {
    padding: 12px 10px 16px 10px; /* 모바일에서도 하단 패딩 증가 */
  }
  
  .spread-visual-container {
    min-height: 160px;
  }
  
  .spread-title {
    font-size: 1.2rem;
  }
  
  .spread-meta {
    font-size: 0.8rem;
    padding: 4px 6px;
  }
}

@media (min-width: 768px) {
  .spread-grid-container {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;
  }
  
  .spread-grid-item {
    height: 360px; /* 태블릿 이상에서는 더 넉넉하게 */
  }
}

@media (min-width: 1200px) {
  .spread-grid-container {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 35px;
  }
  
  .tarot-spread-selection {
    padding: 40px 50px 60px;
  }
  
  .spread-grid-item {
    height: 380px; /* 데스크톱에서는 가장 넉넉하게 */
  }
}

/* 스켈레톤 및 로딩 상태 스타일 */
.spread-card.skeleton {
  pointer-events: none;
  opacity: 0.6;
}

.spread-skeleton {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.skeleton-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  height: 40px;
}

.skeleton-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  animation: skeletonPulse 1.5s ease-in-out infinite;
}

.skeleton-dot:nth-child(1) { animation-delay: 0s; }
.skeleton-dot:nth-child(2) { animation-delay: 0.2s; }
.skeleton-dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes skeletonPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.7; }
}

.skeleton-text {
  height: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  margin: 8px 0;
  animation: skeletonShimmer 1.5s ease-in-out infinite;
}

@keyframes skeletonShimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.skeleton-text {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.05) 25%, 
    rgba(255, 255, 255, 0.15) 50%, 
    rgba(255, 255, 255, 0.05) 75%);
  background-size: 200px 100%;
  animation: skeletonShimmer 1.5s ease-in-out infinite;
}

/* 연결 오류 상태 스타일 */
.connection-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  text-align: center;
  color: #C0C0C0;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 200px;
  grid-column: 1 / -1; /* 전체 그리드 너비 차지 */
}

.error-icon {
  font-size: 3em;
  margin-bottom: 20px;
  animation: errorPulse 2s ease-in-out infinite;
}

@keyframes errorPulse {
  0%, 100% { 
    opacity: 0.6; 
    transform: scale(1);
  }
  50% { 
    opacity: 1; 
    transform: scale(1.1);
  }
}

.connection-error h3 {
  font-size: 1.4em;
  margin-bottom: 10px;
  color: #E0E0E0;
  font-family: 'Gaegu', 'Cute Font', cursive;
}

.connection-error p {
  font-size: 1em;
  margin-bottom: 25px;
  opacity: 0.8;
}

.retry-btn {
  background: linear-gradient(145deg, #4a9eff, #357abd);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(74, 158, 255, 0.3);
}

.retry-btn:hover {
  background: linear-gradient(145deg, #357abd, #2968a3);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(74, 158, 255, 0.4);
}

.retry-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 카드 애니메이션 관련 스타일 */
.card-deck-position {
  /* 초기 덱 위치에서의 카드 스타일 */
  transform-origin: bottom center;
  will-change: transform, left, bottom;
}

.card-moving {
  /* 이동 중인 카드의 스타일 */
  transform-origin: center center;
  will-change: transform, left, top, bottom;
  /* z-index removed, will be handled by inline style from cardZIndices state */
}

.card-reveal {
  /* 최종 위치에 도달한 카드의 스타일 */
  transform-origin: center center;
  will-change: transform;
}

/* 켈틱 크로스의 가로 카드 회전 애니메이션 */
.celtic-cross-crossing {
  transform: rotate(90deg) !important;
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 하이라이트된 카드 효과 - 시각적 효과만 */
.highlight-effects { /* Renamed from .highlight */
  box-shadow: 0 0 20px rgba(255, 235, 59, 0.8), 0 0 40px rgba(255, 235, 59, 0.4) !important;
  /* transform: scale(1.05) !important; REMOVED - Handled inline */
  /* z-index: 1200 !important; REMOVED - Handled inline or by context */
  transition: box-shadow 0.3s ease !important; /* Only transition box-shadow from class */
}

/* 부드러운 GPU 가속 애니메이션을 위한 설정 */
.card-deck-position,
.card-moving,
.card-reveal {
  transform: translateZ(0); /* GPU 가속 활성화 */
  backface-visibility: hidden; /* 뒷면 깜빡임 방지 */
} 