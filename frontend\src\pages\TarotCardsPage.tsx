import React, { useState, useEffect } from 'react';
import { tarotCardsData } from '../data/tarotCardsData';
import type { TarotCard } from '../data/tarotCardsData';
import './TarotCardsPage.css'; // 페이지 스타일을 위한 CSS 파일 임포트

// Floating particles component
const FloatingParticles: React.FC = () => {
  const [particles, setParticles] = useState<Array<{id: number, left: number, delay: number}>>([]);

  useEffect(() => {
    const particleArray = [];
    for (let i = 0; i < 15; i++) {
      particleArray.push({
        id: i,
        left: Math.random() * 100,
        delay: Math.random() * 15
      });
    }
    setParticles(particleArray);
  }, []);

  return (
    <div className="floating-particles" style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      pointerEvents: 'none',
      zIndex: 0
    }}>
      {particles.map(particle => (
        <div
          key={particle.id}
          style={{
            position: 'absolute',
            width: '2px',
            height: '2px',
            background: particle.id % 3 === 0 ? 'rgba(255, 138, 128, 0.7)' : 
                       particle.id % 3 === 1 ? 'rgba(79, 195, 247, 0.7)' : 'rgba(240, 147, 251, 0.7)',
            borderRadius: '50%',
            left: `${particle.left}%`,
            animation: `particleFloat 15s linear infinite`,
            animationDelay: `${particle.delay}s`,
            boxShadow: particle.id % 3 === 0 ? '0 0 8px rgba(255, 138, 128, 0.5)' : 
                      particle.id % 3 === 1 ? '0 0 8px rgba(79, 195, 247, 0.5)' : '0 0 8px rgba(240, 147, 251, 0.5)'
          }}
        />
      ))}
      <style>{`
        @keyframes particleFloat {
          0% {
            transform: translateY(100vh) scale(0);
            opacity: 0;
          }
          10% {
            opacity: 1;
          }
          90% {
            opacity: 1;
          }
          100% {
            transform: translateY(-100px) scale(1);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  );
};

type SuitKey = 'major' | 'wands' | 'cups' | 'swords' | 'pentacles';

const suitNames: Record<SuitKey, string> = {
  major: '메이저 아르카나',
  wands: '완드 (Wands)',
  cups: '컵 (Cups)',
  swords: '소드 (Swords)',
  pentacles: '펜타클 (Pentacles)',
};

const TarotCardsPage: React.FC = () => {
  const [activeSuit, setActiveSuit] = useState<SuitKey>('major');

  const cardsBySuit = (suit: SuitKey) => tarotCardsData.filter(card => card.suit === suit);

  const renderCard = (card: TarotCard) => (
    <div key={card.id} className="tarot-card-item">
      <img 
        src={`/images/tarot/${card.imageName}`} 
        alt={card.name} 
        className="tarot-card-image"
      />
      <h4 className="tarot-card-name">{card.name}</h4>
      <p className="tarot-card-description">{card.description}</p>
    </div>
  );

  return (
    <div className="tarot-cards-page">
      {/* Floating particles background */}
      <FloatingParticles />
      
      <h2 className="page-title">타로 카드 전체 보기</h2>

      <div className="tarot-tabs">
        {(Object.keys(suitNames) as SuitKey[]).map(suitKey => (
          <button 
            key={suitKey} 
            className={`tab-button ${activeSuit === suitKey ? 'active' : ''}`}
            onClick={() => setActiveSuit(suitKey)}
          >
            {suitNames[suitKey]}
          </button>
        ))}
      </div>

      <section className="tarot-suit-section active-section">
        {/* <h3 className="suit-title">{suitNames[activeSuit]}</h3> */} {/* 탭으로 대체되어 주석 처리 또는 제거 가능 */}
        <div className="tarot-card-list">
          {cardsBySuit(activeSuit).map(renderCard)}
        </div>
      </section>
    </div>
  );
};

export default TarotCardsPage; 