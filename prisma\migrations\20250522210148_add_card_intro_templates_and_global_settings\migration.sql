-- AlterTable
ALTER TABLE "TarotSpread" ADD COLUMN     "cardIntroTemplates" TEXT NOT NULL DEFAULT '[]';

-- CreateTable
CREATE TABLE "TarotGlobalSettings" (
    "id" TEXT NOT NULL,
    "settingKey" TEXT NOT NULL,
    "settingValue" TEXT NOT NULL,
    "description" TEXT NOT NULL DEFAULT '',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TarotGlobalSettings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "TarotGlobalSettings_settingKey_key" ON "TarotGlobalSettings"("settingKey");

-- CreateIndex
CREATE INDEX "TarotGlobalSettings_settingKey_idx" ON "TarotGlobalSettings"("settingKey");

-- CreateIndex
CREATE INDEX "TarotGlobalSettings_isActive_idx" ON "TarotGlobalSettings"("isActive");
