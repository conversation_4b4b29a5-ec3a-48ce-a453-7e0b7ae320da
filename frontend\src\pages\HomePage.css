/* HomePage.css - Modern Soft Dark Theme */

.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 30%, #2d4a5a 70%, #3e5b7a 100%);
  color: #f8f9fa;
  position: relative;
  overflow-x: hidden;
}

/* Constellation Background */
.constellation-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.floating-constellation {
  color: rgba(255, 255, 255, 0.4);
  animation: float 8s ease-in-out infinite;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.2));
}

.constellation-1 { animation-delay: 0s; }
.constellation-2 { animation-delay: 2s; }
.constellation-3 { animation-delay: 4s; }
.constellation-4 { animation-delay: 6s; }
.constellation-5 { animation-delay: 8s; }
.constellation-6 { animation-delay: 10s; }

@keyframes float {
  0%, 100% { 
    transform: translateY(0) rotate(0deg); 
    opacity: 0.4; 
  }
  25% { 
    transform: translateY(-15px) rotate(2deg); 
    opacity: 0.6; 
  }
  50% { 
    transform: translateY(-25px) rotate(5deg); 
    opacity: 0.8; 
  }
  75% { 
    transform: translateY(-15px) rotate(3deg); 
    opacity: 0.6; 
  }
}

/* Floating particles */
.floating-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: particleFloat 15s linear infinite;
  box-shadow: 0 0 6px rgba(255, 255, 255, 0.4);
}

.particle:nth-child(3n) {
  background: rgba(255, 138, 128, 0.7);
  box-shadow: 0 0 8px rgba(255, 138, 128, 0.5);
}

.particle:nth-child(3n+1) {
  background: rgba(79, 195, 247, 0.7);
  box-shadow: 0 0 8px rgba(79, 195, 247, 0.5);
}

.particle:nth-child(3n+2) {
  background: rgba(240, 147, 251, 0.7);
  box-shadow: 0 0 8px rgba(240, 147, 251, 0.5);
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) scale(1);
    opacity: 0;
  }
}

/* Hero Section */
.hero-modern {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 5%;
  position: relative;
  z-index: 1;
}

.hero-content {
  flex: 1;
  max-width: 650px;
  padding-right: 2rem;
}

.mystical-time {
  margin-bottom: 2rem;
  opacity: 0.8;
  font-size: 0.95rem;
  letter-spacing: 1.2px;
  color: #b8c5d1;
  text-transform: uppercase;
  font-weight: 300;
}

.hero-title {
  font-size: 4.8rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 2rem;
  text-shadow: 0 0 40px rgba(255, 255, 255, 0.1);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.title-line {
  display: block;
}

.gradient-text {
  background: linear-gradient(45deg, #ff8a80, #ff5722, #ffc107, #4fc3f7, #ab47bc);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 6s ease infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 25% 0%; }
  50% { background-position: 100% 50%; }
  75% { background-position: 75% 100%; }
}

.hero-subtitle {
  font-size: 1.3rem;
  opacity: 0.85;
  line-height: 1.7;
  margin-bottom: 3.5rem;
  color: #d1d9e0;
  font-weight: 300;
}

.hero-cta-buttons {
  display: flex;
  gap: 1.2rem;
  flex-wrap: wrap;
}

.cta-primary, .cta-secondary {
  padding: 1.2rem 2.5rem;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  display: flex;
  align-items: center;
  gap: 0.7rem;
  position: relative;
  overflow: hidden;
}

.cta-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  color: white;
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
}

.cta-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.cta-primary:hover::before {
  left: 100%;
}

.cta-primary:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.5);
}

.cta-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.cta-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 10px 25px rgba(255, 255, 255, 0.1);
}

/* Hero Visual - Mystical Astrology Orb */
.hero-visual {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  position: relative;
}

.mystical-astrology-orb {
  width: 350px;
  height: 350px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.astrology-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  opacity: 0.9;
}

/* Outer Ring - Tarot Major Arcana */
.outer-ring {
  width: 85%;
  height: 85%;
  animation: rotateClockwise 150s linear infinite;
  opacity: 0.5;
}

.tarot-ring {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.2));
}

/* Middle Ring - Alchemical Elements */
.middle-ring {
  width: 65%;
  height: 65%;
  animation: rotateCounterClockwise 120s linear infinite;
  opacity: 0.5;
}

.alchemy-ring {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.2));
}

/* Inner Ring - Mystical Mandala */
.inner-ring {
  width: 45%;
  height: 45%;
  animation: rotateClockwise 90s linear infinite;
  opacity: 0.7;
}

.mandala-geometry {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.2));
}

/* Central Core - All-Seeing Eye */
.orb-core {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle at center, 
    rgba(255, 255, 255, 0.3) 0%, 
    rgba(255, 255, 255, 0.2) 40%, 
    rgba(255, 255, 255, 0.1) 70%, 
    transparent 100%);
  border-radius: 50%;
  animation: corePulse 6s ease-in-out infinite;
}

.all-seeing-eye {
  width: 100%;
  height: 100%;
  animation: eyeBlink 8s ease-in-out infinite;
}

/* Enhanced Animation Keyframes */
@keyframes rotateClockwise {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes rotateCounterClockwise {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(-360deg); }
}

@keyframes corePulse {
  0%, 100% { 
    transform: translate(-50%, -50%) scale(1);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  }
  50% { 
    transform: translate(-50%, -50%) scale(1.05);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  }
}

@keyframes eyeBlink {
  0%, 90%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  95% {
    opacity: 0.9;
    transform: scale(1.02);
  }
}

/* Individual symbol animations */
.tarot-symbols {
  animation: tarotGlow 12s ease-in-out infinite;
}

.alchemy-symbols {
  animation: alchemyPulse 10s ease-in-out infinite 2s;
}

.mandala-pattern {
  animation: mandalaBreath 15s ease-in-out infinite;
}

.celtic-knot {
  animation: celticFlow 20s ease-in-out infinite;
}

@keyframes tarotGlow {
  0%, 100% {
    filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.3));
  }
  25% {
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.4));
  }
  50% {
    filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.3));
  }
  75% {
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.4));
  }
}

@keyframes alchemyPulse {
  0%, 100% {
    opacity: 0.5;
    filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.3));
  }
  33% {
    opacity: 0.6;
    filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.4));
  }
  66% {
    opacity: 0.5;
    filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.3));
  }
}

@keyframes mandalaBreath {
  0%, 100% {
    opacity: 0.5;
    stroke-width: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    stroke-width: 1.2;
    transform: scale(1.01);
  }
}

@keyframes celticFlow {
  0%, 100% {
    opacity: 0.5;
    stroke-width: 1;
  }
  25% {
    opacity: 0.6;
    stroke-width: 1.2;
  }
  50% {
    opacity: 0.5;
    stroke-width: 1.1;
  }
  75% {
    opacity: 0.6;
    stroke-width: 1.2;
  }
}

/* Mystical aura effect */
.mystical-astrology-orb::before {
  content: '';
  position: absolute;
  top: -25px;
  left: -25px;
  right: -25px;
  bottom: -25px;
  border-radius: 50%;
  background: radial-gradient(circle at center,
    transparent 35%,
    rgba(255, 255, 255, 0.05) 45%,
    rgba(255, 255, 255, 0.05) 55%,
    rgba(255, 255, 255, 0.05) 65%,
    rgba(255, 255, 255, 0.05) 75%,
    transparent 85%);
  animation: auraShift 12s ease-in-out infinite;
  z-index: -1;
}

@keyframes auraShift {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1) rotate(0deg);
  }
  25% {
    opacity: 0.4;
    transform: scale(1.02) rotate(90deg);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.05) rotate(180deg);
  }
  75% {
    opacity: 0.4;
    transform: scale(1.02) rotate(270deg);
  }
}

/* Services Section */
.services-modern {
  padding: 8rem 5%;
  position: relative;
  z-index: 1;
  background: linear-gradient(180deg, transparent, rgba(255, 255, 255, 0.02));
}

.section-header {
  text-align: center;
  margin-bottom: 5rem;
}

.section-title {
  font-size: 3.2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  background: linear-gradient(45deg, #ff8a80, #f093fb, #4fc3f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.3rem;
  opacity: 0.8;
  color: #b8c5d1;
  font-weight: 300;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
  gap: 3rem;
  max-width: 1400px;
  margin: 0 auto;
}

.service-card {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 30px;
  padding: 3rem 2.5rem;
  cursor: pointer;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  min-height: 420px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Enhanced cosmic glow effects */
.service-card::before {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 300%;
  height: 300%;
  background: radial-gradient(circle at center, 
    rgba(79, 195, 247, 0.08) 0%,
    rgba(240, 147, 251, 0.05) 30%,
    rgba(255, 138, 128, 0.03) 60%,
    transparent 80%);
  opacity: 0;
  transition: all 0.6s ease;
  animation: cosmicRotation 20s linear infinite;
}

.service-card::after {
  content: '';
  position: absolute;
  inset: 1px;
  border-radius: 29px;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.02) 0%,
    rgba(255, 255, 255, 0.01) 50%,
    rgba(255, 255, 255, 0.03) 100%);
  z-index: -1;
}

@keyframes cosmicRotation {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}

.service-card:hover::before {
  opacity: 1;
  transform: scale(1.2);
}

.service-card:hover {
  transform: translateY(-20px) scale(1.03);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 30px 60px rgba(0, 0, 0, 0.3),
    0 0 40px rgba(79, 195, 247, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Premium card special styling */
.service-card.premium {
  background: rgba(255, 138, 128, 0.06);
  border: 2px solid rgba(255, 138, 128, 0.2);
  position: relative;
}

.service-card.premium::before {
  background: radial-gradient(circle at center, 
    rgba(255, 138, 128, 0.15) 0%,
    rgba(240, 147, 251, 0.08) 40%,
    rgba(79, 195, 247, 0.05) 70%,
    transparent 90%);
}

.service-card.premium::after {
  background: linear-gradient(135deg, 
    rgba(255, 138, 128, 0.05) 0%,
    rgba(240, 147, 251, 0.03) 50%,
    rgba(255, 138, 128, 0.08) 100%);
}

.service-card.premium:hover {
  border-color: rgba(255, 138, 128, 0.5);
  box-shadow: 
    0 30px 60px rgba(255, 138, 128, 0.25),
    0 0 40px rgba(255, 138, 128, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Premium badge floating effect */
.service-card.premium .service-badge {
  animation: premiumFloat 3s ease-in-out infinite;
}

@keyframes premiumFloat {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-4px) scale(1.05); }
}

.card-header {
  margin-bottom: 2rem;
  text-align: center;
  position: relative;
  z-index: 2;
}

.service-icon {
  font-size: 4.5rem;
  margin-bottom: 1.8rem;
  display: block;
  text-align: center;
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
  transition: all 0.4s ease;
  animation: iconFloat 6s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-6px) rotate(2deg); }
  50% { transform: translateY(-10px) rotate(0deg); }
  75% { transform: translateY(-6px) rotate(-2deg); }
}

.service-card:hover .service-icon {
  transform: scale(1.15) translateY(-8px);
  filter: drop-shadow(0 12px 24px rgba(79, 195, 247, 0.4));
}

.service-card h3 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: white;
  font-weight: 700;
  text-align: center;
  background: linear-gradient(135deg, #ffffff 0%, #f0f3ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
  position: relative;
  z-index: 2;
}

.service-card p {
  opacity: 0.9;
  line-height: 1.8;
  margin-bottom: 2.5rem;
  color: #d8e2e8;
  font-weight: 300;
  text-align: center;
  font-size: 1.05rem;
  position: relative;
  z-index: 2;
  flex-grow: 1;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  position: relative;
  z-index: 2;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.service-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  color: white;
  padding: 0.6rem 1.4rem;
  border-radius: 30px;
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.service-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.service-card:hover .service-badge::before {
  left: 100%;
}

.service-price {
  font-weight: 700;
  color: #4fc3f7;
  font-size: 1.2rem;
  text-shadow: 0 2px 4px rgba(79, 195, 247, 0.3);
  position: relative;
}

.service-price::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #4fc3f7, #f093fb);
  transition: width 0.4s ease;
}

.service-card:hover .service-price::after {
  width: 100%;
}

/* Daily Reading Section - Enhanced 3D Flip Card */
.daily-reading-modern {
  padding: 8rem 5%;
  background: linear-gradient(180deg, transparent, rgba(255, 255, 255, 0.03));
  position: relative;
  z-index: 1;
}

.daily-container {
  max-width: 900px;
  margin: 0 auto;
  text-align: center;
}

.daily-title {
  font-size: 2.8rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(45deg, #ffc107, #ff8a80);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.daily-subtitle {
  opacity: 0.8;
  margin-bottom: 4rem;
  font-size: 1.2rem;
  color: #b8c5d1;
  font-weight: 300;
}

.daily-card-area {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

/* 3D Flip Card Container */
.card-container {
  perspective: 1000px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.flip-card {
  width: 280px;
  height: 420px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.flip-card:hover {
  transform: scale(1.05) rotateY(5deg) rotateX(5deg);
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 1s ease-in-out;
  transform-style: preserve-3d;
}

.flip-card.flipped .flip-card-inner {
  transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 
    0 20px 50px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(255, 235, 59, 0.2);
  border: 2px solid rgba(255, 235, 59, 0.3);
}

.flip-card-front {
  transform: rotateY(180deg);
  background: linear-gradient(135deg, #1a1a2e, #16213e);
}

.flip-card-back {
  background: linear-gradient(135deg, #1a1a2e, #16213e);
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 13px;
  transition: all 0.3s ease;
}

.flip-card:hover .card-image {
  filter: brightness(1.1) contrast(1.1);
}

.reveal-text {
  margin-top: 1rem;
  opacity: 0.85;
  font-style: italic;
  color: #b8c5d1;
  font-size: 1.1rem;
  animation: textPulse 2s ease-in-out infinite;
}

@keyframes textPulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* Revealed Card Styles */
.revealed-card {
  animation: cardRevealFade 1s ease-out;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.card-front-revealed {
  width: 280px;
  height: 420px;
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 
    0 25px 60px rgba(255, 193, 7, 0.3),
    0 0 40px rgba(255, 235, 59, 0.4),
    inset 0 1px 3px rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 235, 59, 0.4);
  background: linear-gradient(135deg, #1a1a2e, #16213e);
}

.card-front-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 13px;
  transition: all 0.3s ease;
}

.card-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(
    to top, 
    rgba(0, 0, 0, 0.9) 0%, 
    rgba(0, 0, 0, 0.7) 40%, 
    rgba(0, 0, 0, 0.3) 70%, 
    transparent 100%
  );
  padding: 20px;
  text-align: center;
}

.card-name {
  font-size: 1.8rem;
  color: #fff59d;
  margin: 0 0 10px 0;
  font-weight: 700;
  text-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.5),
    0 0 15px rgba(255, 235, 59, 0.4);
  letter-spacing: 0.5px;
}

.card-suit-badge {
  background: linear-gradient(45deg, rgba(255, 235, 59, 0.8), rgba(255, 193, 7, 0.8));
  color: #2e2e2e;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: inline-block;
  box-shadow: 0 2px 8px rgba(255, 235, 59, 0.3);
}

.card-message-container {
  max-width: 500px;
  text-align: center;
  position: relative;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(255, 235, 59, 0.2);
  backdrop-filter: blur(10px);
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.2),
    inset 0 1px 2px rgba(255, 255, 255, 0.1);
}

.mystical-decoration {
  color: #ffeb3b;
  font-size: 1.5rem;
  margin: 0.5rem 0;
  animation: decorationSpin 8s linear infinite;
  text-shadow: 0 0 10px rgba(255, 235, 59, 0.5);
}

@keyframes decorationSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.card-message {
  margin: 1.5rem 0;
  max-width: 450px;
  line-height: 1.7;
  opacity: 0.95;
  color: #e8e8e8;
  font-size: 1.1rem;
  font-weight: 300;
}

.new-card-btn {
  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 
    0 4px 15px rgba(102, 126, 234, 0.3),
    0 0 20px rgba(118, 75, 162, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.new-card-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.new-card-btn:hover::before {
  left: 100%;
}

.new-card-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 
    0 8px 25px rgba(102, 126, 234, 0.4),
    0 0 30px rgba(118, 75, 162, 0.3);
}

.new-card-btn:active {
  transform: translateY(-1px) scale(1.02);
}

@keyframes cardRevealFade {
  0% { 
    opacity: 0; 
    transform: scale(0.9) translateY(30px); 
  }
  50% {
    opacity: 0.6;
    transform: scale(1.02) translateY(-10px);
  }
  100% { 
    opacity: 1; 
    transform: scale(1) translateY(0); 
  }
}

/* 반응형 디자인 개선 */
@media (max-width: 768px) {
  .daily-title {
    font-size: 2.2rem;
  }

  .flip-card,
  .card-front-revealed {
    width: 220px;
    height: 330px;
  }

  .card-message-container {
    padding: 1.5rem;
    margin: 0 1rem;
  }

  .card-message {
    font-size: 1rem;
  }

  .new-card-btn {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .flip-card,
  .card-front-revealed {
    width: 180px;
    height: 270px;
  }

  .card-name {
    font-size: 1.4rem;
  }

  .card-suit-badge {
    font-size: 0.7rem;
    padding: 4px 8px;
  }
}

/* Stats Section */
.stats-section {
  padding: 8rem 5%;
  position: relative;
  z-index: 1;
  background: linear-gradient(180deg, transparent, rgba(255, 255, 255, 0.03));
}

.stats-container {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.stats-title {
  font-size: 2.8rem;
  margin-bottom: 4rem;
  color: white;
  font-weight: 700;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2.5rem;
}

.stat-item {
  padding: 3rem 2rem;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255, 138, 128, 0.1), transparent);
  transition: left 0.5s ease;
}

.stat-item:hover::before {
  left: 100%;
}

.stat-number {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(45deg, #ff8a80, #4fc3f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.8rem;
  transition: all 0.3s ease;
}

.stat-item:hover .stat-number {
  transform: scale(1.1);
  text-shadow: 0 0 20px rgba(255, 138, 128, 0.5);
}

.stat-label {
  opacity: 0.85;
  font-size: 1.2rem;
  color: #b8c5d1;
  font-weight: 300;
}

/* Final CTA */
.final-cta {
  padding: 8rem 5% 6rem;
  text-align: center;
  position: relative;
  z-index: 1;
}

.cta-container {
  max-width: 700px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2.8rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
  color: white;
}

.cta-description {
  font-size: 1.3rem;
  opacity: 0.85;
  margin-bottom: 3rem;
  color: #b8c5d1;
  font-weight: 300;
  line-height: 1.6;
}

.cta-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-start, .btn-signup {
  padding: 1.2rem 2.5rem;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.btn-start {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  color: white;
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
}

.btn-start:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.5);
}

.btn-signup {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.btn-signup:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-3px) scale(1.02);
}

.btn-start::before, .btn-signup::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-start:hover::before, .btn-signup:hover::before {
  left: 100%;
}

/* Enhanced responsive design */
@media (max-width: 1024px) {
  .hero-modern {
    flex-direction: column;
    text-align: center;
    padding: 4rem 5% 2rem;
  }

  .hero-content {
    padding-right: 0;
    margin-bottom: 3rem;
  }

  .hero-title {
    font-size: 3.8rem;
  }
  
  .mystical-astrology-orb {
    width: 280px;
    height: 280px;
  }

  .all-seeing-eye {
    width: 90%;
    height: 90%;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 3rem;
  }

  .constellation-background {
    opacity: 0.5;
  }
  
  .floating-particles {
    opacity: 0.7;
  }
  
  .particle {
    animation-duration: 20s;
  }

  .mystical-astrology-orb {
    width: 220px;
    height: 220px;
  }

  .all-seeing-eye {
    width: 85%;
    height: 85%;
  }

  /* Simplify animations on mobile for performance */
  .outer-ring {
    animation-duration: 180s;
  }
  
  .middle-ring {
    animation-duration: 120s;
  }
  
  .inner-ring {
    animation-duration: 90s;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 2.5rem;
    max-width: 400px;
    margin: 0 auto;
  }

  .service-card {
    padding: 2.5rem 2rem;
    min-height: 380px;
  }

  .service-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
  }

  .service-card h3 {
    font-size: 1.6rem;
  }

  .hero-cta-buttons, .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .daily-title {
    font-size: 2.2rem;
  }

  .stats-title {
    font-size: 2.2rem;
  }

  .cta-title {
    font-size: 2.2rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .mystical-astrology-orb {
    width: 180px;
    height: 180px;
  }

  .all-seeing-eye {
    width: 80%;
    height: 80%;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .services-grid {
    gap: 1.5rem;
  }

  .service-card {
    padding: 2rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .floating-constellation,
  .particle,
  .mystical-astrology-orb .outer-ring,
  .mystical-astrology-orb .middle-ring,
  .mystical-astrology-orb .inner-ring,
  .mystical-astrology-orb .all-seeing-eye,
  .service-card {
    animation: none;
    transition: none;
  }
  
  .mystery-card:hover {
    transform: scale(1.02);
  }
}

/* Focus states for keyboard navigation */
.cta-primary:focus,
.cta-secondary:focus,
.btn-start:focus,
.btn-signup:focus,
.service-card:focus,
.mystical-astrology-orb:focus {
  outline: 2px solid #4fc3f7;
  outline-offset: 2px;
} 