const express = require('express');
const { PrismaClient } = require('@prisma/client');

const router = express.Router();
const prisma = new PrismaClient();

// 전체 통계 조회
router.get('/overview', async (req, res) => {
  try {
    // 타로 리딩 횟수 조회
    const tarotReadingCount = await prisma.tarotReading.count();
    
    // 활성화된 스프레드 종류 개수 조회
    const activeSpreadCount = await prisma.tarotSpread.count({
      where: {
        isActive: true
      }
    });
    
    // 고정 값들
    const tarotCardCount = 78; // 고정값 (메이저 22 + 마이너 56)
    const aiSupportStatus = '24/7'; // 고정값
    
    // 응답 데이터 구성
    const stats = {
      tarotReadings: tarotReadingCount,
      tarotCards: tarotCardCount,
      spreadTypes: activeSpreadCount,
      aiSupport: aiSupportStatus
    };
    
    res.json({
      success: true,
      stats
    });
    
  } catch (error) {
    console.error('통계 조회 실패:', error);
    res.status(500).json({
      success: false,
      message: '통계 데이터를 가져오는데 실패했습니다.',
      error: error.message
    });
  }
});

// 리딩 횟수 증가 (제미니 API 호출 시 사용)
router.post('/reading/increment', async (req, res) => {
  try {
    const { 
      userId = null, 
      readingType, 
      promptTokens = 0, 
      completionTokens = 0, 
      totalTokens = 0,
      spreadType = null,
      cardCount = 0
    } = req.body;
    
    // 새로운 타로 리딩 기록 생성
    const newReading = await prisma.tarotReading.create({
      data: {
        userId: userId ? parseInt(userId) : null,
        readingType: readingType || 'unknown',
        promptTokens,
        completionTokens,
        totalTokens,
        spreadType,
        cardCount,
        createdAt: new Date()
      }
    });
    
    // 업데이트된 총 리딩 횟수 반환
    const totalReadings = await prisma.tarotReading.count();
    
    res.json({
      success: true,
      message: '리딩 횟수가 기록되었습니다.',
      readingId: newReading.id,
      totalReadings
    });
    
  } catch (error) {
    console.error('리딩 기록 실패:', error);
    res.status(500).json({
      success: false,
      message: '리딩 기록에 실패했습니다.',
      error: error.message
    });
  }
});

// 월별 리딩 통계 (선택사항)
router.get('/monthly', async (req, res) => {
  try {
    const { year = new Date().getFullYear(), month = new Date().getMonth() + 1 } = req.query;
    
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);
    
    const monthlyReadings = await prisma.tarotReading.count({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      }
    });
    
    // 타입별 분석
    const readingsByType = await prisma.tarotReading.groupBy({
      by: ['readingType'],
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      },
      _count: {
        id: true
      }
    });
    
    res.json({
      success: true,
      data: {
        year: parseInt(year),
        month: parseInt(month),
        totalReadings: monthlyReadings,
        readingsByType
      }
    });
    
  } catch (error) {
    console.error('월별 통계 조회 실패:', error);
    res.status(500).json({
      success: false,
      message: '월별 통계를 가져오는데 실패했습니다.',
      error: error.message
    });
  }
});

module.exports = router; 