-- CreateTable
CREATE TABLE "TarotSpread" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "cardCount" INTEGER NOT NULL,
    "cost" INTEGER NOT NULL,
    "iconLayout" TEXT NOT NULL DEFAULT '',
    "spreadType" TEXT NOT NULL,
    "layoutDescription" TEXT NOT NULL DEFAULT '',
    "className" TEXT NOT NULL DEFAULT '',
    "positions" TEXT NOT NULL DEFAULT '[]',
    "discount" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
        "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "TarotSpread_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "TarotSpread_spreadType_key" ON "TarotSpread"("spreadType");

-- CreateIndex 
CREATE INDEX "TarotSpread_isActive_idx" ON "TarotSpread"("isActive");

-- CreateIndex
CREATE INDEX "TarotSpread_cardCount_idx" ON "TarotSpread"("cardCount");

-- CreateIndex
CREATE INDEX "TarotSpread_cost_idx" ON "TarotSpread"("cost"); 