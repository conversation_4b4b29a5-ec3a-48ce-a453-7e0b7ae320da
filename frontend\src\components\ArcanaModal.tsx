import React from 'react';
import type { TarotCard } from '../data/tarotCardsData'; // TarotCard 타입을 가져옵니다.
import './ArcanaModal.css';

interface ArcanaModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  cards: TarotCard[];
}

const ArcanaModal: React.FC<ArcanaModalProps> = ({ isOpen, onClose, title, cards }) => {
  if (!isOpen) return null;

  return (
    <div className="arcana-modal-overlay" onClick={onClose}>
      <div className="arcana-modal-content" onClick={(e) => e.stopPropagation()}>
        <button className="arcana-modal-close-button" onClick={onClose}>×</button>
        <h3 className="arcana-modal-title">{title}</h3>
        <ul className="arcana-modal-list">
          {cards.map((card, index) => (
            <li 
              key={card.id} 
              className="arcana-modal-list-item" 
              style={{ animationDelay: `${index * 0.05}s` }} // 리스트 아이템 애니메이션 지연
            >
              <img 
                src={`/images/tarot/${card.imageName}`} 
                alt={card.name} 
                className="arcana-modal-card-image"
              />
              <div className="arcana-modal-card-details">
                <span className="arcana-modal-card-name">{card.name} {card.number ? `(${card.number})` : ''}</span>
                <p className="arcana-modal-card-description">{card.description}</p>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default ArcanaModal; 