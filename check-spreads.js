const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkSpreads() {
  try {
    const spreads = await prisma.tarotSpread.findMany({
      where: { isActive: true },
      orderBy: { order: 'asc' }
    });
    
    console.log('=== 활성화된 매니저 스프레드 ===');
    console.log(`총 ${spreads.length}개의 스프레드가 있습니다.`);
    
    spreads.forEach((spread, index) => {
      console.log(`\n[${index + 1}] ${spread.name}`);
      console.log(`- 스프레드 타입: ${spread.spreadType}`);
      console.log(`- 카드 수: ${spread.cardCount}장`);
      console.log(`- 비용: ${spread.cost}크레딧`);
      console.log(`- 할인: ${spread.discount || 0}%`);
    });
    
  } catch (error) {
    console.error('에러 발생:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSpreads(); 