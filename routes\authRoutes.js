const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { PrismaClient } = require('@prisma/client');
const authenticateToken = require('../middleware/authenticateToken');

const prisma = new PrismaClient();
const router = express.Router();

const JWT_SECRET = process.env.JWT_SECRET || 'your-very-secure-secret-key'; // .env 파일에 실제 시크릿 키 저장 권장
const ACCESS_TOKEN_EXPIRY = process.env.ACCESS_TOKEN_EXPIRY || '1d'; // Access token expiry
const REFRESH_TOKEN_SECRET = process.env.REFRESH_TOKEN_SECRET || 'your-even-more-secure-refresh-secret-key';
const REFRESH_TOKEN_EXPIRY = process.env.REFRESH_TOKEN_EXPIRY || '7d'; // Refresh token expiry
const INITIAL_CREDITS = 5; // 첫 가입 시 지급 크레딧

// POST /auth/register (이메일 회원가입)
router.post('/register', async (req, res) => {
    const { email, password, name } = req.body;

    // 1. 입력값 유효성 검사 (기본)
    if (!email || !password) {
        return res.status(400).json({ message: '이메일과 비밀번호는 필수입니다.' });
    }
    if (password.length < 8) {
        return res.status(400).json({ message: '비밀번호는 8자 이상이어야 합니다.' });
    }
    // (추가) 비밀번호 길이, 형식 등 검사 로직 추가 가능

    try {
        // 2. 이메일 중복 확인
        const existingUser = await prisma.user.findUnique({
            where: { email },
        });
        if (existingUser) {
            return res.status(409).json({ message: '이미 사용 중인 이메일입니다.' });
        }

        // 3. 비밀번호 암호화
        const hashedPassword = await bcrypt.hash(password, 10); // 10은 salt rounds

        // 4. 사용자 생성 (첫 가입 시 5 크레딧 지급)
        const newUser = await prisma.user.create({
            data: {
                email,
                hashedPassword: hashedPassword,
                name: name || null, // 이름은 선택 사항
                credits: INITIAL_CREDITS, // 초기 크레딧 설정
            },
        });

        // (선택) 사용자 생성 후 바로 JWT 토큰 발급하여 자동 로그인 처리 가능
        // const token = jwt.sign({ userId: newUser.id, email: newUser.email }, JWT_SECRET, { expiresIn: '1h' });

        res.status(201).json({
            message: '회원가입이 성공적으로 완료되었습니다! ✨',
            userId: newUser.id,
            email: newUser.email,
            credits: newUser.credits,
            // token: token (자동 로그인 시)
        });

    } catch (error) {
        console.error('[Register Error]:', error);
        res.status(500).json({ message: '서버 오류가 발생했습니다. 다시 시도해주세요.' });
    }
});

// POST /auth/login (이메일 로그인)
router.post('/login', async (req, res) => {
    console.log('[Login Attempt]: Request body:', req.body); // 요청 본문 로깅
    const { email, password } = req.body;

    if (!email || !password) {
        console.log('[Login Error]: Email or password missing');
        return res.status(400).json({ message: '이메일과 비밀번호를 입력해주세요.' });
    }

    try {
        console.log(`[Login Info]: Finding user with email: ${email}`);
        const user = await prisma.user.findUnique({
            where: { email },
        });

        if (!user) {
            console.log(`[Login Error]: User not found for email: ${email}`);
            return res.status(401).json({ message: '등록되지 않은 이메일이거나 비밀번호가 일치하지 않습니다.' });
        }
        console.log('[Login Info]: User found:', { id: user.id, email: user.email });

        // 소셜 로그인 사용자인데 password가 없는 경우 처리
        if (!user.hashedPassword) {
            console.log(`[Login Error]: User ${email} is a social login user without a password.`);
            return res.status(401).json({ message: '소셜 계정으로 로그인해주세요. 이메일/비밀번호 로그인을 사용하시려면 먼저 비밀번호를 설정해야 합니다.' });
        }

        console.log('[Login Info]: Comparing password for user:', user.email);
        const isPasswordValid = await bcrypt.compare(password, user.hashedPassword);
        
        if (!isPasswordValid) {
            console.log(`[Login Error]: Invalid password for user: ${email}`);
            return res.status(401).json({ message: '등록되지 않은 이메일이거나 비밀번호가 일치하지 않습니다.' });
        }
        console.log('[Login Info]: Password is valid for user:', user.email);

        // --- JWT Generation --- 
        console.log('[Login Info]: Generating tokens for user:', user.email);
        // Access Token
        const accessToken = jwt.sign(
            { userId: user.id, email: user.email }, // Payload
            JWT_SECRET,
            { expiresIn: ACCESS_TOKEN_EXPIRY } // Use configured expiry
        );
        // Refresh Token
        const refreshToken = jwt.sign(
            { userId: user.id }, // Keep payload minimal for refresh token
            REFRESH_TOKEN_SECRET,
            { expiresIn: REFRESH_TOKEN_EXPIRY } // Use configured expiry
        );

        // --- Set Refresh Token in HttpOnly Cookie --- 
        res.cookie('refreshToken', refreshToken, {
            httpOnly: true, // Accessible only by the web server
            secure: process.env.NODE_ENV === 'production', // Send only over HTTPS in production
            sameSite: 'strict', // Helps prevent CSRF attacks
            maxAge: 7 * 24 * 60 * 60 * 1000 // Cookie expiry in milliseconds (match REFRESH_TOKEN_EXPIRY, e.g., 7 days)
            // path: '/' // Default path is usually fine
        });
        console.log('[Login Info]: Refresh token set in HttpOnly cookie for user:', user.email);

        // --- Send Access Token and User Info in Response Body --- 
        res.status(200).json({
            message: '로그인 성공! 💖',
            token: accessToken, // Send the access token
            userId: user.id,
            email: user.email,
            name: user.name,
            credits: user.credits,
            user: {
                id: user.id,
                name: user.name,
                email: user.email,
                credits: user.credits
            }
        });

    } catch (error) {
        console.error('[Login Catch Error]: An error occurred during login process for email:', email, error);
        res.status(500).json({ message: '서버 오류가 발생했습니다. 다시 시도해주세요.' });
    }
});

// POST /auth/refresh - Refresh the access token using the refresh token cookie
router.post('/refresh', async (req, res) => {
    const refreshToken = req.cookies.refreshToken;
    console.log('[/auth/refresh] Received refresh token from cookie:', refreshToken ? 'Present' : 'Missing');

    if (!refreshToken) {
        return res.status(401).json({ message: 'Refresh token not found.' });
    }

    try {
        // Verify the refresh token
        const payload = jwt.verify(refreshToken, REFRESH_TOKEN_SECRET);
        const userId = payload.userId;
        console.log('[/auth/refresh] Refresh token verified for userId:', userId);

        // Optionally: Check if the user still exists or if the token is revoked in DB (if using DB storage)
        // const user = await prisma.user.findUnique({ where: { id: userId } });
        // if (!user) { return res.status(401).json({ message: 'User not found.' }); }

        // Generate a new access token
        const newAccessToken = jwt.sign(
            { userId: userId, email: payload.email }, // Assuming email was in original payload or fetch user data
            JWT_SECRET,
            { expiresIn: ACCESS_TOKEN_EXPIRY }
        );
        console.log('[/auth/refresh] New access token generated for userId:', userId);

        // Send the new access token
        res.status(200).json({ 
            message: 'Access token refreshed successfully.',
            token: newAccessToken 
        });

    } catch (error) {
        console.error('[/auth/refresh] Error:', error.message, error.name);
        if (error.name === 'TokenExpiredError') {
            return res.status(403).json({ message: 'Refresh token expired. Please log in again.', code: 'REFRESH_TOKEN_EXPIRED' });
        } else if (error.name === 'JsonWebTokenError') {
            return res.status(403).json({ message: 'Invalid refresh token.', code: 'INVALID_REFRESH_TOKEN' });
        } else {
            return res.status(500).json({ message: 'Could not refresh access token.' });
        }
    }
});

// TODO: 소셜 로그인 API (구글, 카카오)
// TODO: 로그아웃 API (/auth/logout)
// TODO: 사용자 정보 확인 API (/auth/me)

// 닉네임 변경 라우트 (기존에 있었다면 여기에 위치할 수 있음)
router.patch('/me/nickname', authenticateToken, async (req, res) => {
    const { name } = req.body;
    const userId = req.user.userId; // authenticateToken 미들웨어에서 설정

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
        return res.status(400).json({ message: '새로운 닉네임을 입력해주세요.' });
    }

    try {
        const updatedUser = await prisma.user.update({
            where: { id: userId },
            data: { name: name.trim() },
        });
        // 중요: 패스워드 필드는 절대 반환하지 않습니다.
        res.json({ 
            message: '닉네임이 성공적으로 변경되었습니다.', 
            user: {
                id: updatedUser.id,
                name: updatedUser.name,
                email: updatedUser.email,
                credits: updatedUser.credits
            }
        });
    } catch (error) {
        console.error('Error updating nickname:', error);
        res.status(500).json({ message: '닉네임 변경 중 서버 오류가 발생했습니다.' });
    }
});

// 비밀번호 변경 라우트 추가
router.patch('/me/password', authenticateToken, async (req, res) => {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.userId;

    if (!currentPassword || !newPassword) {
        return res.status(400).json({ message: '현재 비밀번호와 새 비밀번호를 모두 입력해주세요.' });
    }

    if (newPassword.length < 8) {
        return res.status(400).json({ message: '새 비밀번호는 8자 이상이어야 합니다.' });
    }

    try {
        const user = await prisma.user.findUnique({ where: { id: userId } });
        if (!user) {
            // 이 경우는 authenticateToken을 통과했으므로 발생하기 어렵지만, 방어적으로 코딩
            return res.status(404).json({ message: '사용자를 찾을 수 없습니다.' });
        }

        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.hashedPassword);
        if (!isCurrentPasswordValid) {
            return res.status(400).json({ message: '현재 비밀번호가 일치하지 않습니다.' });
        }

        const hashedNewPassword = await bcrypt.hash(newPassword, 10);

        await prisma.user.update({
            where: { id: userId },
            data: { hashedPassword: hashedNewPassword },
        });

        res.json({ message: '비밀번호가 성공적으로 변경되었습니다. 다시 로그인해주세요.' });

    } catch (error) {
        console.error('Error updating password:', error);
        res.status(500).json({ message: '비밀번호 변경 중 서버 오류가 발생했습니다.' });
    }
});

module.exports = router; 