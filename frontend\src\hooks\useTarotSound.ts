import { useState, useRef, useEffect } from 'react';

// 효과음 파일 경로 (public 폴더 기준)
export const SOUND_PATHS = {
  backgroundMusic: '/sounds/cosmic-background.mp3',
  cardShuffle: '/sounds/card-shuffle.mp3',
  cardSelect: '/sounds/card-select.mp3',
  cardReveal: '/sounds/card-reveal.mp3',
  chatMessage: '/sounds/chat-message.mp3',
  finalReveal: '/sounds/final-reveal.mp3',
  fortuneMusic: '/sounds/A_New_Town.mp3', // 운세 진행 시 재생될 음악
  cardPlace: '/sounds/effect/card-place.ogg', // 카드 선택 효과음
  cardPlaceCancel: '/sounds/effect/card-place-2.ogg', // 카드 선택 해제 효과음
  cardSlide1: '/sounds/effect/card-slide-1.ogg', // 카드 뒤집기 효과음 1
  cardSlide2: '/sounds/effect/card-slide-2.ogg',
  cardSlide3: '/sounds/effect/card-slide-3.ogg',
  cardSlide4: '/sounds/effect/card-slide-4.ogg',
  cardSlide5: '/sounds/effect/card-slide-5.ogg',
  cardSlide6: '/sounds/effect/card-slide-6.ogg',
  cardSlide7: '/sounds/effect/card-slide-7.ogg',
  cardSlide8: '/sounds/effect/card-slide-8.ogg',
};

// 랜덤 카드 슬라이드 사운드 선택 함수
export const getRandomCardSlideSound = () => {
  const cardSlideSounds = [
    SOUND_PATHS.cardSlide1,
    SOUND_PATHS.cardSlide2,
    SOUND_PATHS.cardSlide3,
    SOUND_PATHS.cardSlide4,
    SOUND_PATHS.cardSlide5,
    SOUND_PATHS.cardSlide6,
    SOUND_PATHS.cardSlide7,
    SOUND_PATHS.cardSlide8,
  ];
  return cardSlideSounds[Math.floor(Math.random() * cardSlideSounds.length)];
};

export const useTarotSound = () => {
  // Sound states
  const [isSoundMuted, setIsSoundMuted] = useState<boolean>(false);
  const [soundVolume, setSoundVolume] = useState<number>(0.5); // Default volume 50%
  const audioRef = useRef<HTMLAudioElement | null>(null); // For background music
  const effectAudioRef = useRef<HTMLAudioElement | null>(null); // For sound effects
  const [isFortuneMusic, setIsFortuneMusic] = useState<boolean>(false); // 운세 음악이 재생 중인지 여부

  // Function to play background music
  const playBackgroundMusic = (soundPath: string) => {
    if (!isSoundMuted && audioRef.current) {
      // 현재 운세 음악이 재생 중이고, 같은 음악을 재생하려는 경우 중복 재생 방지
      if (isFortuneMusic && soundPath === SOUND_PATHS.fortuneMusic && !audioRef.current.paused) {
        return;
      }
      
      // 재생 중이면 중지
      if (!audioRef.current.paused) {
        audioRef.current.pause();
      }
      audioRef.current.src = soundPath;
      audioRef.current.volume = soundVolume;
      
      // 운세 음악인 경우 반복 재생 설정
      if (soundPath === SOUND_PATHS.fortuneMusic) {
        audioRef.current.loop = true;
        setIsFortuneMusic(true);
      } else {
        audioRef.current.loop = false;
        setIsFortuneMusic(false);
      }
      
      audioRef.current.play().catch(e => console.warn("Audio play failed:", e));
    }
  };

  // Function to play sound effects
  const playEffect = (soundPath: string) => {
    if (!isSoundMuted && effectAudioRef.current) {
      effectAudioRef.current.src = soundPath;
      effectAudioRef.current.volume = soundVolume;
      effectAudioRef.current.play().catch(e => console.warn("Effect play failed:", e));
    }
  };

  // Function to play sound - 판단하여 알맞은 함수 호출
  const playSound = (soundPath: string) => {
    // 배경 음악인 경우
    if (soundPath === SOUND_PATHS.fortuneMusic) {
      playBackgroundMusic(soundPath);
    } else {
      // 효과음인 경우
      playEffect(soundPath);
    }
  };

  // Function to stop background music
  const stopSound = () => {
    if (audioRef.current && !audioRef.current.paused) {
      audioRef.current.pause();
      setIsFortuneMusic(false);
    }
  };

  // 볼륨 또는 음소거 설정이 변경될 때 실행할 useEffect
  useEffect(() => {
    // 배경 음악 볼륨 조정
    if (audioRef.current) {
      if (isSoundMuted) {
        audioRef.current.volume = 0;
      } else {
        audioRef.current.volume = soundVolume;
      }
    }
    
    // 효과음 볼륨 조정
    if (effectAudioRef.current) {
      if (isSoundMuted) {
        effectAudioRef.current.volume = 0;
      } else {
        effectAudioRef.current.volume = soundVolume;
      }
    }
  }, [isSoundMuted, soundVolume]);

  useEffect(() => {
    audioRef.current = new Audio();
    effectAudioRef.current = new Audio();
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.src = '';
        audioRef.current = null;
      }
      if (effectAudioRef.current) {
        effectAudioRef.current.pause();
        effectAudioRef.current.src = '';
        effectAudioRef.current = null;
      }
      setIsFortuneMusic(false);
    };
  }, []);

  return {
    // States
    isSoundMuted,
    setIsSoundMuted,
    soundVolume,
    setSoundVolume,
    isFortuneMusic,
    
    // Refs
    audioRef,
    effectAudioRef,
    
    // Functions
    playSound,
    playBackgroundMusic,
    playEffect,
    stopSound,
  };
};
