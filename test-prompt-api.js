const axios = require('axios');

async function testPromptAPI() {
  try {
    const testData = {
      promptTemplate: "안녕하세요 {userName}님! 당신의 {userConcern}에 대한 타로 해석을 시작하겠습니다.\n\n선택된 카드들:\n{cards}\n\n이 카드들이 전하는 메시지를 해석해보겠습니다.",
      systemInstruction: "친절하고 따뜻한 어조로 타로 해석을 제공하세요.",
      testData: {
        userName: "김테스트",
        userConcern: "연애운에 대해 궁금합니다",
        selectedCards: ["마법사", "여제", "태양"]
      },
      spreadName: "3카드 스프레드",
      cardCount: 3,
      customVariables: {
        mood: "신비로운",
        style: "따뜻한"
      }
    };

    console.log('프롬프트 테스트 API 호출 중...');
    
    const response = await axios.post('http://localhost:3000/api/tarot/test-prompt', testData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 60초 타임아웃
    });

    console.log('✅ 성공!');
    console.log('응답:', response.data);
    
    if (response.data.success) {
      console.log('\n🎯 테스트 결과:');
      console.log(response.data.testResult);
    } else {
      console.log('❌ 실패:', response.data.message);
    }

  } catch (error) {
    console.error('❌ 오류 발생:', error.response?.data || error.message);
  }
}

testPromptAPI(); 